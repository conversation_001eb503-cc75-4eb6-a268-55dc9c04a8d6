
[general]

display_bkg_color="#ffffff"
display_result_font="Sans Bold 26"
display_result_color="black"
display_stack_font="Sans Bold 11"
display_stack_color="black"
display_module_font="Sans Bold 8"
display_module_active_color="black"
display_module_inactive_color="grey"
display_module_number=true
display_module_angle=true
display_module_notation=true
display_module_arith=true
display_module_open=true
custom_button_font=false
button_font="Sans 10"
button_width=40
button_height=25
function_button_group=true
dispctrl_button_group=true
logic_button_group=true
standard_button_group=true
mode=1
dec_sep=false
dec_sep_length=3
dec_sep_char=" "
hex_bits=32
hex_signed=true
hex_sep=false
hex_sep_length=4
hex_sep_char=" "
oct_bits=32
oct_signed=true
oct_sep=false
oct_sep_length=3
oct_sep_char=" "
bin_bits=16
bin_signed=true
bin_fixed=false
bin_length=8
bin_sep=false
bin_sep_length=4
bin_sep_char=" "
default_number_base=0
default_angle_base=1
default_notation_mode=0
stack_size=3
remembers_display=false
remembered_valuex="14000"
remembered_valuey="0"
remembered_valuez="0"
remembered_valuet="0"
show_menu_bar=true

[constants]

Pi:pi=3.14159265359
Euler's Number:e=2.71828182846

[user functions]

abs(x)=sqrt(x^2)
sign(x)=x/abs(x)
cot(x)=cos(x)/sin(x)
