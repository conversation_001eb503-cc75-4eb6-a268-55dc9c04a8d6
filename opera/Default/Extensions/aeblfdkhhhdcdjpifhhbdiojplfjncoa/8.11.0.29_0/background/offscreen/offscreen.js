
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="5d836992-3d22-56f1-99d1-0cd9d3c2ebc6")}catch(e){}}();
var os=Object.create;var zr=Object.defineProperty;var as=Object.getOwnPropertyDescriptor;var us=Object.getOwnPropertyNames;var cs=Object.getPrototypeOf,ls=Object.prototype.hasOwnProperty;var E=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports);var fs=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of us(r))!ls.call(e,i)&&i!==t&&zr(e,i,{get:()=>r[i],enumerable:!(n=as(r,i))||n.enumerable});return e};var Yr=(e,r,t)=>(t=e!=null?os(cs(e)):{},fs(r||!e||!e.__esModule?zr(t,"default",{value:e,enumerable:!0}):t,e));var Zr=E((ar,Jr)=>{(function(e,r){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],r);else if(typeof ar<"u")r(Jr);else{var t={exports:{}};r(t),e.browser=t.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:ar,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let r="The message port closed before a response was received.",t=n=>{let i={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(i).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class o extends WeakMap{constructor(R,b=void 0){super(b),this.createItem=R}get(R){return this.has(R)||this.set(R,this.createItem(R)),super.get(R)}}let c=T=>T&&typeof T=="object"&&typeof T.then=="function",u=(T,R)=>(...b)=>{n.runtime.lastError?T.reject(new Error(n.runtime.lastError.message)):R.singleCallbackArg||b.length<=1&&R.singleCallbackArg!==!1?T.resolve(b[0]):T.resolve(b)},l=T=>T==1?"argument":"arguments",f=(T,R)=>function(L,...D){if(D.length<R.minArgs)throw new Error(`Expected at least ${R.minArgs} ${l(R.minArgs)} for ${T}(), got ${D.length}`);if(D.length>R.maxArgs)throw new Error(`Expected at most ${R.maxArgs} ${l(R.maxArgs)} for ${T}(), got ${D.length}`);return new Promise((z,Y)=>{if(R.fallbackToNoCallback)try{L[T](...D,u({resolve:z,reject:Y},R))}catch(O){console.warn(`${T} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,O),L[T](...D),R.fallbackToNoCallback=!1,R.noCallback=!0,z()}else R.noCallback?(L[T](...D),z()):L[T](...D,u({resolve:z,reject:Y},R))})},g=(T,R,b)=>new Proxy(R,{apply(L,D,z){return b.call(D,T,...z)}}),p=Function.call.bind(Object.prototype.hasOwnProperty),v=(T,R={},b={})=>{let L=Object.create(null),D={has(Y,O){return O in T||O in L},get(Y,O,J){if(O in L)return L[O];if(!(O in T))return;let F=T[O];if(typeof F=="function")if(typeof R[O]=="function")F=g(T,T[O],R[O]);else if(p(b,O)){let he=f(O,b[O]);F=g(T,T[O],he)}else F=F.bind(T);else if(typeof F=="object"&&F!==null&&(p(R,O)||p(b,O)))F=v(F,R[O],b[O]);else if(p(b,"*"))F=v(F,R[O],b["*"]);else return Object.defineProperty(L,O,{configurable:!0,enumerable:!0,get(){return T[O]},set(he){T[O]=he}}),F;return L[O]=F,F},set(Y,O,J,F){return O in L?L[O]=J:T[O]=J,!0},defineProperty(Y,O,J){return Reflect.defineProperty(L,O,J)},deleteProperty(Y,O){return Reflect.deleteProperty(L,O)}},z=Object.create(T);return new Proxy(z,D)},m=T=>({addListener(R,b,...L){R.addListener(T.get(b),...L)},hasListener(R,b){return R.hasListener(T.get(b))},removeListener(R,b){R.removeListener(T.get(b))}}),j=new o(T=>typeof T!="function"?T:function(b){let L=v(b,{},{getContent:{minArgs:0,maxArgs:0}});T(L)}),C=new o(T=>typeof T!="function"?T:function(b,L,D){let z=!1,Y,O=new Promise(we=>{Y=function(te){z=!0,we(te)}}),J;try{J=T(b,L,Y)}catch(we){J=Promise.reject(we)}let F=J!==!0&&c(J);if(J!==!0&&!F&&!z)return!1;let he=we=>{we.then(te=>{D(te)},te=>{let or;te&&(te instanceof Error||typeof te.message=="string")?or=te.message:or="An unexpected error occurred",D({__mozWebExtensionPolyfillReject__:!0,message:or})}).catch(te=>{console.error("Failed to send onMessage rejected reply",te)})};return he(F?J:O),!0}),V=({reject:T,resolve:R},b)=>{n.runtime.lastError?n.runtime.lastError.message===r?R():T(new Error(n.runtime.lastError.message)):b&&b.__mozWebExtensionPolyfillReject__?T(new Error(b.message)):R(b)},Hr=(T,R,b,...L)=>{if(L.length<R.minArgs)throw new Error(`Expected at least ${R.minArgs} ${l(R.minArgs)} for ${T}(), got ${L.length}`);if(L.length>R.maxArgs)throw new Error(`Expected at most ${R.maxArgs} ${l(R.maxArgs)} for ${T}(), got ${L.length}`);return new Promise((D,z)=>{let Y=V.bind(null,{resolve:D,reject:z});L.push(Y),b.sendMessage(...L)})},ss={devtools:{network:{onRequestFinished:m(j)}},runtime:{onMessage:m(C),onMessageExternal:m(C),sendMessage:Hr.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:Hr.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},sr={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return i.privacy={network:{"*":sr},services:{"*":sr},websites:{"*":sr}},v(n,ss,i)};e.exports=t(chrome)}else e.exports=globalThis.browser})});var xe=E((Lf,Qr)=>{"use strict";var ps="2.0.0",gs=Number.MAX_SAFE_INTEGER||9007199254740991,ms=16,hs=250,ds=["major","premajor","minor","preminor","patch","prepatch","prerelease"];Qr.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:ms,MAX_SAFE_BUILD_LENGTH:hs,MAX_SAFE_INTEGER:gs,RELEASE_TYPES:ds,SEMVER_SPEC_VERSION:ps,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});var Oe=E((Nf,et)=>{"use strict";var vs=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};et.exports=vs});var de=E((ne,rt)=>{"use strict";var{MAX_SAFE_COMPONENT_LENGTH:cr,MAX_SAFE_BUILD_LENGTH:ys,MAX_LENGTH:As}=xe(),Es=Oe();ne=rt.exports={};var Ts=ne.re=[],Rs=ne.safeRe=[],h=ne.src=[],ws=ne.safeSrc=[],d=ne.t={},xs=0,lr="[a-zA-Z0-9-]",Os=[["\\s",1],["\\d",As],[lr,ys]],bs=e=>{for(let[r,t]of Os)e=e.split(`${r}*`).join(`${r}{0,${t}}`).split(`${r}+`).join(`${r}{1,${t}}`);return e},w=(e,r,t)=>{let n=bs(r),i=xs++;Es(e,i,r),d[e]=i,h[i]=r,ws[i]=n,Ts[i]=new RegExp(r,t?"g":void 0),Rs[i]=new RegExp(n,t?"g":void 0)};w("NUMERICIDENTIFIER","0|[1-9]\\d*");w("NUMERICIDENTIFIERLOOSE","\\d+");w("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${lr}*`);w("MAINVERSION",`(${h[d.NUMERICIDENTIFIER]})\\.(${h[d.NUMERICIDENTIFIER]})\\.(${h[d.NUMERICIDENTIFIER]})`);w("MAINVERSIONLOOSE",`(${h[d.NUMERICIDENTIFIERLOOSE]})\\.(${h[d.NUMERICIDENTIFIERLOOSE]})\\.(${h[d.NUMERICIDENTIFIERLOOSE]})`);w("PRERELEASEIDENTIFIER",`(?:${h[d.NONNUMERICIDENTIFIER]}|${h[d.NUMERICIDENTIFIER]})`);w("PRERELEASEIDENTIFIERLOOSE",`(?:${h[d.NONNUMERICIDENTIFIER]}|${h[d.NUMERICIDENTIFIERLOOSE]})`);w("PRERELEASE",`(?:-(${h[d.PRERELEASEIDENTIFIER]}(?:\\.${h[d.PRERELEASEIDENTIFIER]})*))`);w("PRERELEASELOOSE",`(?:-?(${h[d.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${h[d.PRERELEASEIDENTIFIERLOOSE]})*))`);w("BUILDIDENTIFIER",`${lr}+`);w("BUILD",`(?:\\+(${h[d.BUILDIDENTIFIER]}(?:\\.${h[d.BUILDIDENTIFIER]})*))`);w("FULLPLAIN",`v?${h[d.MAINVERSION]}${h[d.PRERELEASE]}?${h[d.BUILD]}?`);w("FULL",`^${h[d.FULLPLAIN]}$`);w("LOOSEPLAIN",`[v=\\s]*${h[d.MAINVERSIONLOOSE]}${h[d.PRERELEASELOOSE]}?${h[d.BUILD]}?`);w("LOOSE",`^${h[d.LOOSEPLAIN]}$`);w("GTLT","((?:<|>)?=?)");w("XRANGEIDENTIFIERLOOSE",`${h[d.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);w("XRANGEIDENTIFIER",`${h[d.NUMERICIDENTIFIER]}|x|X|\\*`);w("XRANGEPLAIN",`[v=\\s]*(${h[d.XRANGEIDENTIFIER]})(?:\\.(${h[d.XRANGEIDENTIFIER]})(?:\\.(${h[d.XRANGEIDENTIFIER]})(?:${h[d.PRERELEASE]})?${h[d.BUILD]}?)?)?`);w("XRANGEPLAINLOOSE",`[v=\\s]*(${h[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${h[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${h[d.XRANGEIDENTIFIERLOOSE]})(?:${h[d.PRERELEASELOOSE]})?${h[d.BUILD]}?)?)?`);w("XRANGE",`^${h[d.GTLT]}\\s*${h[d.XRANGEPLAIN]}$`);w("XRANGELOOSE",`^${h[d.GTLT]}\\s*${h[d.XRANGEPLAINLOOSE]}$`);w("COERCEPLAIN",`(^|[^\\d])(\\d{1,${cr}})(?:\\.(\\d{1,${cr}}))?(?:\\.(\\d{1,${cr}}))?`);w("COERCE",`${h[d.COERCEPLAIN]}(?:$|[^\\d])`);w("COERCEFULL",h[d.COERCEPLAIN]+`(?:${h[d.PRERELEASE]})?(?:${h[d.BUILD]})?(?:$|[^\\d])`);w("COERCERTL",h[d.COERCE],!0);w("COERCERTLFULL",h[d.COERCEFULL],!0);w("LONETILDE","(?:~>?)");w("TILDETRIM",`(\\s*)${h[d.LONETILDE]}\\s+`,!0);ne.tildeTrimReplace="$1~";w("TILDE",`^${h[d.LONETILDE]}${h[d.XRANGEPLAIN]}$`);w("TILDELOOSE",`^${h[d.LONETILDE]}${h[d.XRANGEPLAINLOOSE]}$`);w("LONECARET","(?:\\^)");w("CARETTRIM",`(\\s*)${h[d.LONECARET]}\\s+`,!0);ne.caretTrimReplace="$1^";w("CARET",`^${h[d.LONECARET]}${h[d.XRANGEPLAIN]}$`);w("CARETLOOSE",`^${h[d.LONECARET]}${h[d.XRANGEPLAINLOOSE]}$`);w("COMPARATORLOOSE",`^${h[d.GTLT]}\\s*(${h[d.LOOSEPLAIN]})$|^$`);w("COMPARATOR",`^${h[d.GTLT]}\\s*(${h[d.FULLPLAIN]})$|^$`);w("COMPARATORTRIM",`(\\s*)${h[d.GTLT]}\\s*(${h[d.LOOSEPLAIN]}|${h[d.XRANGEPLAIN]})`,!0);ne.comparatorTrimReplace="$1$2$3";w("HYPHENRANGE",`^\\s*(${h[d.XRANGEPLAIN]})\\s+-\\s+(${h[d.XRANGEPLAIN]})\\s*$`);w("HYPHENRANGELOOSE",`^\\s*(${h[d.XRANGEPLAINLOOSE]})\\s+-\\s+(${h[d.XRANGEPLAINLOOSE]})\\s*$`);w("STAR","(<|>)?=?\\s*\\*");w("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");w("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});var De=E((_f,tt)=>{"use strict";var Ss=Object.freeze({loose:!0}),Is=Object.freeze({}),Ls=e=>e?typeof e!="object"?Ss:e:Is;tt.exports=Ls});var fr=E(($f,st)=>{"use strict";var nt=/^[0-9]+$/,it=(e,r)=>{let t=nt.test(e),n=nt.test(r);return t&&n&&(e=+e,r=+r),e===r?0:t&&!n?-1:n&&!t?1:e<r?-1:1},Ns=(e,r)=>it(r,e);st.exports={compareIdentifiers:it,rcompareIdentifiers:Ns}});var W=E((Cf,at)=>{"use strict";var Ve=Oe(),{MAX_LENGTH:ot,MAX_SAFE_INTEGER:We}=xe(),{safeRe:Be,t:Ge}=de(),_s=De(),{compareIdentifiers:ve}=fr(),pr=class e{constructor(r,t){if(t=_s(t),r instanceof e){if(r.loose===!!t.loose&&r.includePrerelease===!!t.includePrerelease)return r;r=r.version}else if(typeof r!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof r}".`);if(r.length>ot)throw new TypeError(`version is longer than ${ot} characters`);Ve("SemVer",r,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let n=r.trim().match(t.loose?Be[Ge.LOOSE]:Be[Ge.FULL]);if(!n)throw new TypeError(`Invalid Version: ${r}`);if(this.raw=r,this.major=+n[1],this.minor=+n[2],this.patch=+n[3],this.major>We||this.major<0)throw new TypeError("Invalid major version");if(this.minor>We||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>We||this.patch<0)throw new TypeError("Invalid patch version");n[4]?this.prerelease=n[4].split(".").map(i=>{if(/^[0-9]+$/.test(i)){let o=+i;if(o>=0&&o<We)return o}return i}):this.prerelease=[],this.build=n[5]?n[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(r){if(Ve("SemVer.compare",this.version,this.options,r),!(r instanceof e)){if(typeof r=="string"&&r===this.version)return 0;r=new e(r,this.options)}return r.version===this.version?0:this.compareMain(r)||this.comparePre(r)}compareMain(r){return r instanceof e||(r=new e(r,this.options)),ve(this.major,r.major)||ve(this.minor,r.minor)||ve(this.patch,r.patch)}comparePre(r){if(r instanceof e||(r=new e(r,this.options)),this.prerelease.length&&!r.prerelease.length)return-1;if(!this.prerelease.length&&r.prerelease.length)return 1;if(!this.prerelease.length&&!r.prerelease.length)return 0;let t=0;do{let n=this.prerelease[t],i=r.prerelease[t];if(Ve("prerelease compare",t,n,i),n===void 0&&i===void 0)return 0;if(i===void 0)return 1;if(n===void 0)return-1;if(n===i)continue;return ve(n,i)}while(++t)}compareBuild(r){r instanceof e||(r=new e(r,this.options));let t=0;do{let n=this.build[t],i=r.build[t];if(Ve("build compare",t,n,i),n===void 0&&i===void 0)return 0;if(i===void 0)return 1;if(n===void 0)return-1;if(n===i)continue;return ve(n,i)}while(++t)}inc(r,t,n){if(r.startsWith("pre")){if(!t&&n===!1)throw new Error("invalid increment argument: identifier is empty");if(t){let i=`-${t}`.match(this.options.loose?Be[Ge.PRERELEASELOOSE]:Be[Ge.PRERELEASE]);if(!i||i[1]!==t)throw new Error(`invalid identifier: ${t}`)}}switch(r){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,n);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,n);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,n),this.inc("pre",t,n);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",t,n),this.inc("pre",t,n);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{let i=Number(n)?1:0;if(this.prerelease.length===0)this.prerelease=[i];else{let o=this.prerelease.length;for(;--o>=0;)typeof this.prerelease[o]=="number"&&(this.prerelease[o]++,o=-2);if(o===-1){if(t===this.prerelease.join(".")&&n===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(i)}}if(t){let o=[t,i];n===!1&&(o=[t]),ve(this.prerelease[0],t)===0?isNaN(this.prerelease[1])&&(this.prerelease=o):this.prerelease=o}break}default:throw new Error(`invalid increment argument: ${r}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};at.exports=pr});var le=E((Pf,ct)=>{"use strict";var ut=W(),$s=(e,r,t=!1)=>{if(e instanceof ut)return e;try{return new ut(e,r)}catch(n){if(!t)return null;throw n}};ct.exports=$s});var ft=E((Uf,lt)=>{"use strict";var Cs=le(),Ps=(e,r)=>{let t=Cs(e,r);return t?t.version:null};lt.exports=Ps});var gt=E((qf,pt)=>{"use strict";var Us=le(),qs=(e,r)=>{let t=Us(e.trim().replace(/^[=v]+/,""),r);return t?t.version:null};pt.exports=qs});var dt=E((Mf,ht)=>{"use strict";var mt=W(),Ms=(e,r,t,n,i)=>{typeof t=="string"&&(i=n,n=t,t=void 0);try{return new mt(e instanceof mt?e.version:e,t).inc(r,n,i).version}catch{return null}};ht.exports=Ms});var At=E((jf,yt)=>{"use strict";var vt=le(),js=(e,r)=>{let t=vt(e,null,!0),n=vt(r,null,!0),i=t.compare(n);if(i===0)return null;let o=i>0,c=o?t:n,u=o?n:t,l=!!c.prerelease.length;if(!!u.prerelease.length&&!l){if(!u.patch&&!u.minor)return"major";if(u.compareMain(c)===0)return u.minor&&!u.patch?"minor":"patch"}let g=l?"pre":"";return t.major!==n.major?g+"major":t.minor!==n.minor?g+"minor":t.patch!==n.patch?g+"patch":"prerelease"};yt.exports=js});var Tt=E((Ff,Et)=>{"use strict";var Fs=W(),Ds=(e,r)=>new Fs(e,r).major;Et.exports=Ds});var wt=E((Df,Rt)=>{"use strict";var Vs=W(),Ws=(e,r)=>new Vs(e,r).minor;Rt.exports=Ws});var Ot=E((Vf,xt)=>{"use strict";var Bs=W(),Gs=(e,r)=>new Bs(e,r).patch;xt.exports=Gs});var St=E((Wf,bt)=>{"use strict";var Ks=le(),ks=(e,r)=>{let t=Ks(e,r);return t&&t.prerelease.length?t.prerelease:null};bt.exports=ks});var Z=E((Bf,Lt)=>{"use strict";var It=W(),Xs=(e,r,t)=>new It(e,t).compare(new It(r,t));Lt.exports=Xs});var _t=E((Gf,Nt)=>{"use strict";var Hs=Z(),zs=(e,r,t)=>Hs(r,e,t);Nt.exports=zs});var Ct=E((Kf,$t)=>{"use strict";var Ys=Z(),Js=(e,r)=>Ys(e,r,!0);$t.exports=Js});var Ke=E((kf,Ut)=>{"use strict";var Pt=W(),Zs=(e,r,t)=>{let n=new Pt(e,t),i=new Pt(r,t);return n.compare(i)||n.compareBuild(i)};Ut.exports=Zs});var Mt=E((Xf,qt)=>{"use strict";var Qs=Ke(),eo=(e,r)=>e.sort((t,n)=>Qs(t,n,r));qt.exports=eo});var Ft=E((Hf,jt)=>{"use strict";var ro=Ke(),to=(e,r)=>e.sort((t,n)=>ro(n,t,r));jt.exports=to});var be=E((zf,Dt)=>{"use strict";var no=Z(),io=(e,r,t)=>no(e,r,t)>0;Dt.exports=io});var ke=E((Yf,Vt)=>{"use strict";var so=Z(),oo=(e,r,t)=>so(e,r,t)<0;Vt.exports=oo});var gr=E((Jf,Wt)=>{"use strict";var ao=Z(),uo=(e,r,t)=>ao(e,r,t)===0;Wt.exports=uo});var mr=E((Zf,Bt)=>{"use strict";var co=Z(),lo=(e,r,t)=>co(e,r,t)!==0;Bt.exports=lo});var Xe=E((Qf,Gt)=>{"use strict";var fo=Z(),po=(e,r,t)=>fo(e,r,t)>=0;Gt.exports=po});var He=E((ep,Kt)=>{"use strict";var go=Z(),mo=(e,r,t)=>go(e,r,t)<=0;Kt.exports=mo});var hr=E((rp,kt)=>{"use strict";var ho=gr(),vo=mr(),yo=be(),Ao=Xe(),Eo=ke(),To=He(),Ro=(e,r,t,n)=>{switch(r){case"===":return typeof e=="object"&&(e=e.version),typeof t=="object"&&(t=t.version),e===t;case"!==":return typeof e=="object"&&(e=e.version),typeof t=="object"&&(t=t.version),e!==t;case"":case"=":case"==":return ho(e,t,n);case"!=":return vo(e,t,n);case">":return yo(e,t,n);case">=":return Ao(e,t,n);case"<":return Eo(e,t,n);case"<=":return To(e,t,n);default:throw new TypeError(`Invalid operator: ${r}`)}};kt.exports=Ro});var Ht=E((tp,Xt)=>{"use strict";var wo=W(),xo=le(),{safeRe:ze,t:Ye}=de(),Oo=(e,r)=>{if(e instanceof wo)return e;if(typeof e=="number"&&(e=String(e)),typeof e!="string")return null;r=r||{};let t=null;if(!r.rtl)t=e.match(r.includePrerelease?ze[Ye.COERCEFULL]:ze[Ye.COERCE]);else{let l=r.includePrerelease?ze[Ye.COERCERTLFULL]:ze[Ye.COERCERTL],f;for(;(f=l.exec(e))&&(!t||t.index+t[0].length!==e.length);)(!t||f.index+f[0].length!==t.index+t[0].length)&&(t=f),l.lastIndex=f.index+f[1].length+f[2].length;l.lastIndex=-1}if(t===null)return null;let n=t[2],i=t[3]||"0",o=t[4]||"0",c=r.includePrerelease&&t[5]?`-${t[5]}`:"",u=r.includePrerelease&&t[6]?`+${t[6]}`:"";return xo(`${n}.${i}.${o}${c}${u}`,r)};Xt.exports=Oo});var Yt=E((np,zt)=>{"use strict";var dr=class{constructor(){this.max=1e3,this.map=new Map}get(r){let t=this.map.get(r);if(t!==void 0)return this.map.delete(r),this.map.set(r,t),t}delete(r){return this.map.delete(r)}set(r,t){if(!this.delete(r)&&t!==void 0){if(this.map.size>=this.max){let i=this.map.keys().next().value;this.delete(i)}this.map.set(r,t)}return this}};zt.exports=dr});var Q=E((ip,en)=>{"use strict";var bo=/\s+/g,vr=class e{constructor(r,t){if(t=Io(t),r instanceof e)return r.loose===!!t.loose&&r.includePrerelease===!!t.includePrerelease?r:new e(r.raw,t);if(r instanceof yr)return this.raw=r.value,this.set=[[r]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=r.trim().replace(bo," "),this.set=this.raw.split("||").map(n=>this.parseRange(n.trim())).filter(n=>n.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let n=this.set[0];if(this.set=this.set.filter(i=>!Zt(i[0])),this.set.length===0)this.set=[n];else if(this.set.length>1){for(let i of this.set)if(i.length===1&&Uo(i[0])){this.set=[i];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let r=0;r<this.set.length;r++){r>0&&(this.formatted+="||");let t=this.set[r];for(let n=0;n<t.length;n++)n>0&&(this.formatted+=" "),this.formatted+=t[n].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(r){let n=((this.options.includePrerelease&&Co)|(this.options.loose&&Po))+":"+r,i=Jt.get(n);if(i)return i;let o=this.options.loose,c=o?X[K.HYPHENRANGELOOSE]:X[K.HYPHENRANGE];r=r.replace(c,Ko(this.options.includePrerelease)),$("hyphen replace",r),r=r.replace(X[K.COMPARATORTRIM],No),$("comparator trim",r),r=r.replace(X[K.TILDETRIM],_o),$("tilde trim",r),r=r.replace(X[K.CARETTRIM],$o),$("caret trim",r);let u=r.split(" ").map(p=>qo(p,this.options)).join(" ").split(/\s+/).map(p=>Go(p,this.options));o&&(u=u.filter(p=>($("loose invalid filter",p,this.options),!!p.match(X[K.COMPARATORLOOSE])))),$("range list",u);let l=new Map,f=u.map(p=>new yr(p,this.options));for(let p of f){if(Zt(p))return[p];l.set(p.value,p)}l.size>1&&l.has("")&&l.delete("");let g=[...l.values()];return Jt.set(n,g),g}intersects(r,t){if(!(r instanceof e))throw new TypeError("a Range is required");return this.set.some(n=>Qt(n,t)&&r.set.some(i=>Qt(i,t)&&n.every(o=>i.every(c=>o.intersects(c,t)))))}test(r){if(!r)return!1;if(typeof r=="string")try{r=new Lo(r,this.options)}catch{return!1}for(let t=0;t<this.set.length;t++)if(ko(this.set[t],r,this.options))return!0;return!1}};en.exports=vr;var So=Yt(),Jt=new So,Io=De(),yr=Se(),$=Oe(),Lo=W(),{safeRe:X,t:K,comparatorTrimReplace:No,tildeTrimReplace:_o,caretTrimReplace:$o}=de(),{FLAG_INCLUDE_PRERELEASE:Co,FLAG_LOOSE:Po}=xe(),Zt=e=>e.value==="<0.0.0-0",Uo=e=>e.value==="",Qt=(e,r)=>{let t=!0,n=e.slice(),i=n.pop();for(;t&&n.length;)t=n.every(o=>i.intersects(o,r)),i=n.pop();return t},qo=(e,r)=>($("comp",e,r),e=Fo(e,r),$("caret",e),e=Mo(e,r),$("tildes",e),e=Vo(e,r),$("xrange",e),e=Bo(e,r),$("stars",e),e),k=e=>!e||e.toLowerCase()==="x"||e==="*",Mo=(e,r)=>e.trim().split(/\s+/).map(t=>jo(t,r)).join(" "),jo=(e,r)=>{let t=r.loose?X[K.TILDELOOSE]:X[K.TILDE];return e.replace(t,(n,i,o,c,u)=>{$("tilde",e,n,i,o,c,u);let l;return k(i)?l="":k(o)?l=`>=${i}.0.0 <${+i+1}.0.0-0`:k(c)?l=`>=${i}.${o}.0 <${i}.${+o+1}.0-0`:u?($("replaceTilde pr",u),l=`>=${i}.${o}.${c}-${u} <${i}.${+o+1}.0-0`):l=`>=${i}.${o}.${c} <${i}.${+o+1}.0-0`,$("tilde return",l),l})},Fo=(e,r)=>e.trim().split(/\s+/).map(t=>Do(t,r)).join(" "),Do=(e,r)=>{$("caret",e,r);let t=r.loose?X[K.CARETLOOSE]:X[K.CARET],n=r.includePrerelease?"-0":"";return e.replace(t,(i,o,c,u,l)=>{$("caret",e,i,o,c,u,l);let f;return k(o)?f="":k(c)?f=`>=${o}.0.0${n} <${+o+1}.0.0-0`:k(u)?o==="0"?f=`>=${o}.${c}.0${n} <${o}.${+c+1}.0-0`:f=`>=${o}.${c}.0${n} <${+o+1}.0.0-0`:l?($("replaceCaret pr",l),o==="0"?c==="0"?f=`>=${o}.${c}.${u}-${l} <${o}.${c}.${+u+1}-0`:f=`>=${o}.${c}.${u}-${l} <${o}.${+c+1}.0-0`:f=`>=${o}.${c}.${u}-${l} <${+o+1}.0.0-0`):($("no pr"),o==="0"?c==="0"?f=`>=${o}.${c}.${u}${n} <${o}.${c}.${+u+1}-0`:f=`>=${o}.${c}.${u}${n} <${o}.${+c+1}.0-0`:f=`>=${o}.${c}.${u} <${+o+1}.0.0-0`),$("caret return",f),f})},Vo=(e,r)=>($("replaceXRanges",e,r),e.split(/\s+/).map(t=>Wo(t,r)).join(" ")),Wo=(e,r)=>{e=e.trim();let t=r.loose?X[K.XRANGELOOSE]:X[K.XRANGE];return e.replace(t,(n,i,o,c,u,l)=>{$("xRange",e,n,i,o,c,u,l);let f=k(o),g=f||k(c),p=g||k(u),v=p;return i==="="&&v&&(i=""),l=r.includePrerelease?"-0":"",f?i===">"||i==="<"?n="<0.0.0-0":n="*":i&&v?(g&&(c=0),u=0,i===">"?(i=">=",g?(o=+o+1,c=0,u=0):(c=+c+1,u=0)):i==="<="&&(i="<",g?o=+o+1:c=+c+1),i==="<"&&(l="-0"),n=`${i+o}.${c}.${u}${l}`):g?n=`>=${o}.0.0${l} <${+o+1}.0.0-0`:p&&(n=`>=${o}.${c}.0${l} <${o}.${+c+1}.0-0`),$("xRange return",n),n})},Bo=(e,r)=>($("replaceStars",e,r),e.trim().replace(X[K.STAR],"")),Go=(e,r)=>($("replaceGTE0",e,r),e.trim().replace(X[r.includePrerelease?K.GTE0PRE:K.GTE0],"")),Ko=e=>(r,t,n,i,o,c,u,l,f,g,p,v)=>(k(n)?t="":k(i)?t=`>=${n}.0.0${e?"-0":""}`:k(o)?t=`>=${n}.${i}.0${e?"-0":""}`:c?t=`>=${t}`:t=`>=${t}${e?"-0":""}`,k(f)?l="":k(g)?l=`<${+f+1}.0.0-0`:k(p)?l=`<${f}.${+g+1}.0-0`:v?l=`<=${f}.${g}.${p}-${v}`:e?l=`<${f}.${g}.${+p+1}-0`:l=`<=${l}`,`${t} ${l}`.trim()),ko=(e,r,t)=>{for(let n=0;n<e.length;n++)if(!e[n].test(r))return!1;if(r.prerelease.length&&!t.includePrerelease){for(let n=0;n<e.length;n++)if($(e[n].semver),e[n].semver!==yr.ANY&&e[n].semver.prerelease.length>0){let i=e[n].semver;if(i.major===r.major&&i.minor===r.minor&&i.patch===r.patch)return!0}return!1}return!0}});var Se=E((sp,an)=>{"use strict";var Ie=Symbol("SemVer ANY"),Tr=class e{static get ANY(){return Ie}constructor(r,t){if(t=rn(t),r instanceof e){if(r.loose===!!t.loose)return r;r=r.value}r=r.trim().split(/\s+/).join(" "),Er("comparator",r,t),this.options=t,this.loose=!!t.loose,this.parse(r),this.semver===Ie?this.value="":this.value=this.operator+this.semver.version,Er("comp",this)}parse(r){let t=this.options.loose?tn[nn.COMPARATORLOOSE]:tn[nn.COMPARATOR],n=r.match(t);if(!n)throw new TypeError(`Invalid comparator: ${r}`);this.operator=n[1]!==void 0?n[1]:"",this.operator==="="&&(this.operator=""),n[2]?this.semver=new sn(n[2],this.options.loose):this.semver=Ie}toString(){return this.value}test(r){if(Er("Comparator.test",r,this.options.loose),this.semver===Ie||r===Ie)return!0;if(typeof r=="string")try{r=new sn(r,this.options)}catch{return!1}return Ar(r,this.operator,this.semver,this.options)}intersects(r,t){if(!(r instanceof e))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new on(r.value,t).test(this.value):r.operator===""?r.value===""?!0:new on(this.value,t).test(r.semver):(t=rn(t),t.includePrerelease&&(this.value==="<0.0.0-0"||r.value==="<0.0.0-0")||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||r.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&r.operator.startsWith(">")||this.operator.startsWith("<")&&r.operator.startsWith("<")||this.semver.version===r.semver.version&&this.operator.includes("=")&&r.operator.includes("=")||Ar(this.semver,"<",r.semver,t)&&this.operator.startsWith(">")&&r.operator.startsWith("<")||Ar(this.semver,">",r.semver,t)&&this.operator.startsWith("<")&&r.operator.startsWith(">")))}};an.exports=Tr;var rn=De(),{safeRe:tn,t:nn}=de(),Ar=hr(),Er=Oe(),sn=W(),on=Q()});var Le=E((op,un)=>{"use strict";var Xo=Q(),Ho=(e,r,t)=>{try{r=new Xo(r,t)}catch{return!1}return r.test(e)};un.exports=Ho});var ln=E((ap,cn)=>{"use strict";var zo=Q(),Yo=(e,r)=>new zo(e,r).set.map(t=>t.map(n=>n.value).join(" ").trim().split(" "));cn.exports=Yo});var pn=E((up,fn)=>{"use strict";var Jo=W(),Zo=Q(),Qo=(e,r,t)=>{let n=null,i=null,o=null;try{o=new Zo(r,t)}catch{return null}return e.forEach(c=>{o.test(c)&&(!n||i.compare(c)===-1)&&(n=c,i=new Jo(n,t))}),n};fn.exports=Qo});var mn=E((cp,gn)=>{"use strict";var ea=W(),ra=Q(),ta=(e,r,t)=>{let n=null,i=null,o=null;try{o=new ra(r,t)}catch{return null}return e.forEach(c=>{o.test(c)&&(!n||i.compare(c)===1)&&(n=c,i=new ea(n,t))}),n};gn.exports=ta});var vn=E((lp,dn)=>{"use strict";var Rr=W(),na=Q(),hn=be(),ia=(e,r)=>{e=new na(e,r);let t=new Rr("0.0.0");if(e.test(t)||(t=new Rr("0.0.0-0"),e.test(t)))return t;t=null;for(let n=0;n<e.set.length;++n){let i=e.set[n],o=null;i.forEach(c=>{let u=new Rr(c.semver.version);switch(c.operator){case">":u.prerelease.length===0?u.patch++:u.prerelease.push(0),u.raw=u.format();case"":case">=":(!o||hn(u,o))&&(o=u);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${c.operator}`)}}),o&&(!t||hn(t,o))&&(t=o)}return t&&e.test(t)?t:null};dn.exports=ia});var An=E((fp,yn)=>{"use strict";var sa=Q(),oa=(e,r)=>{try{return new sa(e,r).range||"*"}catch{return null}};yn.exports=oa});var Je=E((pp,wn)=>{"use strict";var aa=W(),Rn=Se(),{ANY:ua}=Rn,ca=Q(),la=Le(),En=be(),Tn=ke(),fa=He(),pa=Xe(),ga=(e,r,t,n)=>{e=new aa(e,n),r=new ca(r,n);let i,o,c,u,l;switch(t){case">":i=En,o=fa,c=Tn,u=">",l=">=";break;case"<":i=Tn,o=pa,c=En,u="<",l="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(la(e,r,n))return!1;for(let f=0;f<r.set.length;++f){let g=r.set[f],p=null,v=null;if(g.forEach(m=>{m.semver===ua&&(m=new Rn(">=0.0.0")),p=p||m,v=v||m,i(m.semver,p.semver,n)?p=m:c(m.semver,v.semver,n)&&(v=m)}),p.operator===u||p.operator===l||(!v.operator||v.operator===u)&&o(e,v.semver))return!1;if(v.operator===l&&c(e,v.semver))return!1}return!0};wn.exports=ga});var On=E((gp,xn)=>{"use strict";var ma=Je(),ha=(e,r,t)=>ma(e,r,">",t);xn.exports=ha});var Sn=E((mp,bn)=>{"use strict";var da=Je(),va=(e,r,t)=>da(e,r,"<",t);bn.exports=va});var Nn=E((hp,Ln)=>{"use strict";var In=Q(),ya=(e,r,t)=>(e=new In(e,t),r=new In(r,t),e.intersects(r,t));Ln.exports=ya});var $n=E((dp,_n)=>{"use strict";var Aa=Le(),Ea=Z();_n.exports=(e,r,t)=>{let n=[],i=null,o=null,c=e.sort((g,p)=>Ea(g,p,t));for(let g of c)Aa(g,r,t)?(o=g,i||(i=g)):(o&&n.push([i,o]),o=null,i=null);i&&n.push([i,null]);let u=[];for(let[g,p]of n)g===p?u.push(g):!p&&g===c[0]?u.push("*"):p?g===c[0]?u.push(`<=${p}`):u.push(`${g} - ${p}`):u.push(`>=${g}`);let l=u.join(" || "),f=typeof r.raw=="string"?r.raw:String(r);return l.length<f.length?l:r}});var jn=E((vp,Mn)=>{"use strict";var Cn=Q(),xr=Se(),{ANY:wr}=xr,Ne=Le(),Or=Z(),Ta=(e,r,t={})=>{if(e===r)return!0;e=new Cn(e,t),r=new Cn(r,t);let n=!1;e:for(let i of e.set){for(let o of r.set){let c=wa(i,o,t);if(n=n||c!==null,c)continue e}if(n)return!1}return!0},Ra=[new xr(">=0.0.0-0")],Pn=[new xr(">=0.0.0")],wa=(e,r,t)=>{if(e===r)return!0;if(e.length===1&&e[0].semver===wr){if(r.length===1&&r[0].semver===wr)return!0;t.includePrerelease?e=Ra:e=Pn}if(r.length===1&&r[0].semver===wr){if(t.includePrerelease)return!0;r=Pn}let n=new Set,i,o;for(let m of e)m.operator===">"||m.operator===">="?i=Un(i,m,t):m.operator==="<"||m.operator==="<="?o=qn(o,m,t):n.add(m.semver);if(n.size>1)return null;let c;if(i&&o){if(c=Or(i.semver,o.semver,t),c>0)return null;if(c===0&&(i.operator!==">="||o.operator!=="<="))return null}for(let m of n){if(i&&!Ne(m,String(i),t)||o&&!Ne(m,String(o),t))return null;for(let j of r)if(!Ne(m,String(j),t))return!1;return!0}let u,l,f,g,p=o&&!t.includePrerelease&&o.semver.prerelease.length?o.semver:!1,v=i&&!t.includePrerelease&&i.semver.prerelease.length?i.semver:!1;p&&p.prerelease.length===1&&o.operator==="<"&&p.prerelease[0]===0&&(p=!1);for(let m of r){if(g=g||m.operator===">"||m.operator===">=",f=f||m.operator==="<"||m.operator==="<=",i){if(v&&m.semver.prerelease&&m.semver.prerelease.length&&m.semver.major===v.major&&m.semver.minor===v.minor&&m.semver.patch===v.patch&&(v=!1),m.operator===">"||m.operator===">="){if(u=Un(i,m,t),u===m&&u!==i)return!1}else if(i.operator===">="&&!Ne(i.semver,String(m),t))return!1}if(o){if(p&&m.semver.prerelease&&m.semver.prerelease.length&&m.semver.major===p.major&&m.semver.minor===p.minor&&m.semver.patch===p.patch&&(p=!1),m.operator==="<"||m.operator==="<="){if(l=qn(o,m,t),l===m&&l!==o)return!1}else if(o.operator==="<="&&!Ne(o.semver,String(m),t))return!1}if(!m.operator&&(o||i)&&c!==0)return!1}return!(i&&f&&!o&&c!==0||o&&g&&!i&&c!==0||v||p)},Un=(e,r,t)=>{if(!e)return r;let n=Or(e.semver,r.semver,t);return n>0?e:n<0||r.operator===">"&&e.operator===">="?r:e},qn=(e,r,t)=>{if(!e)return r;let n=Or(e.semver,r.semver,t);return n<0?e:n>0||r.operator==="<"&&e.operator==="<="?r:e};Mn.exports=Ta});var Wn=E((yp,Vn)=>{"use strict";var br=de(),Fn=xe(),xa=W(),Dn=fr(),Oa=le(),ba=ft(),Sa=gt(),Ia=dt(),La=At(),Na=Tt(),_a=wt(),$a=Ot(),Ca=St(),Pa=Z(),Ua=_t(),qa=Ct(),Ma=Ke(),ja=Mt(),Fa=Ft(),Da=be(),Va=ke(),Wa=gr(),Ba=mr(),Ga=Xe(),Ka=He(),ka=hr(),Xa=Ht(),Ha=Se(),za=Q(),Ya=Le(),Ja=ln(),Za=pn(),Qa=mn(),eu=vn(),ru=An(),tu=Je(),nu=On(),iu=Sn(),su=Nn(),ou=$n(),au=jn();Vn.exports={parse:Oa,valid:ba,clean:Sa,inc:Ia,diff:La,major:Na,minor:_a,patch:$a,prerelease:Ca,compare:Pa,rcompare:Ua,compareLoose:qa,compareBuild:Ma,sort:ja,rsort:Fa,gt:Da,lt:Va,eq:Wa,neq:Ba,gte:Ga,lte:Ka,cmp:ka,coerce:Xa,Comparator:Ha,Range:za,satisfies:Ya,toComparators:Ja,maxSatisfying:Za,minSatisfying:Qa,minVersion:eu,validRange:ru,outside:tu,gtr:nu,ltr:iu,intersects:su,simplifyRange:ou,subset:au,SemVer:xa,re:br.re,src:br.src,tokens:br.t,SEMVER_SPEC_VERSION:Fn.SEMVER_SPEC_VERSION,RELEASE_TYPES:Fn.RELEASE_TYPES,compareIdentifiers:Dn.compareIdentifiers,rcompareIdentifiers:Dn.rcompareIdentifiers}});var ae=E(y=>{"use strict";var lu=y&&y.__spreadArray||function(e,r,t){if(t||arguments.length===2)for(var n=0,i=r.length,o;n<i;n++)(o||!(n in r))&&(o||(o=Array.prototype.slice.call(r,0,n)),o[n]=r[n]);return e.concat(o||Array.prototype.slice.call(r))};Object.defineProperty(y,"__esModule",{value:!0});y.dual=y.getEndomorphismMonoid=y.not=y.SK=y.hole=y.pipe=y.untupled=y.tupled=y.absurd=y.decrement=y.increment=y.tuple=y.flow=y.flip=y.constVoid=y.constUndefined=y.constNull=y.constFalse=y.constTrue=y.constant=y.unsafeCoerce=y.identity=y.apply=y.getRing=y.getSemiring=y.getMonoid=y.getSemigroup=y.getBooleanAlgebra=void 0;var fu=function(e){return function(){return{meet:function(r,t){return function(n){return e.meet(r(n),t(n))}},join:function(r,t){return function(n){return e.join(r(n),t(n))}},zero:function(){return e.zero},one:function(){return e.one},implies:function(r,t){return function(n){return e.implies(r(n),t(n))}},not:function(r){return function(t){return e.not(r(t))}}}}};y.getBooleanAlgebra=fu;var pu=function(e){return function(){return{concat:function(r,t){return function(n){return e.concat(r(n),t(n))}}}}};y.getSemigroup=pu;var gu=function(e){var r=(0,y.getSemigroup)(e);return function(){return{concat:r().concat,empty:function(){return e.empty}}}};y.getMonoid=gu;var mu=function(e){return{add:function(r,t){return function(n){return e.add(r(n),t(n))}},zero:function(){return e.zero},mul:function(r,t){return function(n){return e.mul(r(n),t(n))}},one:function(){return e.one}}};y.getSemiring=mu;var hu=function(e){var r=(0,y.getSemiring)(e);return{add:r.add,mul:r.mul,one:r.one,zero:r.zero,sub:function(t,n){return function(i){return e.sub(t(i),n(i))}}}};y.getRing=hu;var du=function(e){return function(r){return r(e)}};y.apply=du;function Sr(e){return e}y.identity=Sr;y.unsafeCoerce=Sr;function _e(e){return function(){return e}}y.constant=_e;y.constTrue=_e(!0);y.constFalse=_e(!1);y.constNull=_e(null);y.constUndefined=_e(void 0);y.constVoid=y.constUndefined;function vu(e){return function(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];return r.length>1?e(r[1],r[0]):function(n){return e(n)(r[0])}}}y.flip=vu;function Bn(e,r,t,n,i,o,c,u,l){switch(arguments.length){case 1:return e;case 2:return function(){return r(e.apply(this,arguments))};case 3:return function(){return t(r(e.apply(this,arguments)))};case 4:return function(){return n(t(r(e.apply(this,arguments))))};case 5:return function(){return i(n(t(r(e.apply(this,arguments)))))};case 6:return function(){return o(i(n(t(r(e.apply(this,arguments))))))};case 7:return function(){return c(o(i(n(t(r(e.apply(this,arguments)))))))};case 8:return function(){return u(c(o(i(n(t(r(e.apply(this,arguments))))))))};case 9:return function(){return l(u(c(o(i(n(t(r(e.apply(this,arguments)))))))))}}}y.flow=Bn;function yu(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return e}y.tuple=yu;function Au(e){return e+1}y.increment=Au;function Eu(e){return e-1}y.decrement=Eu;function Gn(e){throw new Error("Called `absurd` function which should be uncallable")}y.absurd=Gn;function Tu(e){return function(r){return e.apply(void 0,r)}}y.tupled=Tu;function Ru(e){return function(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];return e(r)}}y.untupled=Ru;function wu(e,r,t,n,i,o,c,u,l){switch(arguments.length){case 1:return e;case 2:return r(e);case 3:return t(r(e));case 4:return n(t(r(e)));case 5:return i(n(t(r(e))));case 6:return o(i(n(t(r(e)))));case 7:return c(o(i(n(t(r(e))))));case 8:return u(c(o(i(n(t(r(e)))))));case 9:return l(u(c(o(i(n(t(r(e))))))));default:{for(var f=arguments[0],g=1;g<arguments.length;g++)f=arguments[g](f);return f}}}y.pipe=wu;y.hole=Gn;var xu=function(e,r){return r};y.SK=xu;function Ou(e){return function(r){return!e(r)}}y.not=Ou;var bu=function(){return{concat:function(e,r){return Bn(e,r)},empty:Sr}};y.getEndomorphismMonoid=bu;var Su=function(e,r){var t=typeof e=="number"?function(n){return n.length>=e}:e;return function(){var n=Array.from(arguments);return t(arguments)?r.apply(this,n):function(i){return r.apply(void 0,lu([i],n,!1))}}};y.dual=Su});var $e=E(A=>{"use strict";var Iu=A&&A.__spreadArray||function(e,r,t){if(t||arguments.length===2)for(var n=0,i=r.length,o;n<i;n++)(o||!(n in r))&&(o||(o=Array.prototype.slice.call(r,0,n)),o[n]=r[n]);return e.concat(o||Array.prototype.slice.call(r))};Object.defineProperty(A,"__esModule",{value:!0});A.flatMapReader=A.flatMapTask=A.flatMapIO=A.flatMapEither=A.flatMapOption=A.flatMapNullable=A.liftOption=A.liftNullable=A.fromReadonlyNonEmptyArray=A.has=A.emptyRecord=A.emptyReadonlyArray=A.tail=A.head=A.isNonEmpty=A.singleton=A.right=A.left=A.isRight=A.isLeft=A.some=A.none=A.isSome=A.isNone=void 0;var ye=ae(),Lu=function(e){return e._tag==="None"};A.isNone=Lu;var Nu=function(e){return e._tag==="Some"};A.isSome=Nu;A.none={_tag:"None"};var _u=function(e){return{_tag:"Some",value:e}};A.some=_u;var $u=function(e){return e._tag==="Left"};A.isLeft=$u;var Cu=function(e){return e._tag==="Right"};A.isRight=Cu;var Pu=function(e){return{_tag:"Left",left:e}};A.left=Pu;var Uu=function(e){return{_tag:"Right",right:e}};A.right=Uu;var qu=function(e){return[e]};A.singleton=qu;var Mu=function(e){return e.length>0};A.isNonEmpty=Mu;var ju=function(e){return e[0]};A.head=ju;var Fu=function(e){return e.slice(1)};A.tail=Fu;A.emptyReadonlyArray=[];A.emptyRecord={};A.has=Object.prototype.hasOwnProperty;var Du=function(e){return Iu([e[0]],e.slice(1),!0)};A.fromReadonlyNonEmptyArray=Du;var Vu=function(e){return function(r,t){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=r.apply(void 0,n);return e.fromEither(o==null?(0,A.left)(t.apply(void 0,n)):(0,A.right)(o))}}};A.liftNullable=Vu;var Wu=function(e){return function(r,t){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=r.apply(void 0,n);return e.fromEither((0,A.isNone)(o)?(0,A.left)(t.apply(void 0,n)):(0,A.right)(o.value))}}};A.liftOption=Wu;var Bu=function(e,r){return(0,ye.dual)(3,function(t,n,i){return r.flatMap(t,(0,A.liftNullable)(e)(n,i))})};A.flatMapNullable=Bu;var Gu=function(e,r){return(0,ye.dual)(3,function(t,n,i){return r.flatMap(t,(0,A.liftOption)(e)(n,i))})};A.flatMapOption=Gu;var Ku=function(e,r){return(0,ye.dual)(2,function(t,n){return r.flatMap(t,function(i){return e.fromEither(n(i))})})};A.flatMapEither=Ku;var ku=function(e,r){return(0,ye.dual)(2,function(t,n){return r.flatMap(t,function(i){return e.fromIO(n(i))})})};A.flatMapIO=ku;var Xu=function(e,r){return(0,ye.dual)(2,function(t,n){return r.flatMap(t,function(i){return e.fromTask(n(i))})})};A.flatMapTask=Xu;var Hu=function(e,r){return(0,ye.dual)(2,function(t,n){return r.flatMap(t,function(i){return e.fromReader(n(i))})})};A.flatMapReader=Hu});var Nr=E(P=>{"use strict";var zu=P&&P.__createBinding||(Object.create?function(e,r,t,n){n===void 0&&(n=t);var i=Object.getOwnPropertyDescriptor(r,t);(!i||("get"in i?!r.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,n,i)}:function(e,r,t,n){n===void 0&&(n=t),e[n]=r[t]}),Yu=P&&P.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),Ju=P&&P.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var t in e)t!=="default"&&Object.prototype.hasOwnProperty.call(e,t)&&zu(r,e,t);return Yu(r,e),r};Object.defineProperty(P,"__esModule",{value:!0});P.sequenceS=P.sequenceT=P.getApplySemigroup=P.apS=P.apSecond=P.apFirst=P.ap=void 0;var Zu=ae(),Qu=Ju($e());function ec(e,r){return function(t){return function(n){return e.ap(e.map(n,function(i){return function(o){return r.ap(i,o)}}),t)}}}P.ap=ec;function rc(e){return function(r){return function(t){return e.ap(e.map(t,function(n){return function(){return n}}),r)}}}P.apFirst=rc;function tc(e){return function(r){return function(t){return e.ap(e.map(t,function(){return function(n){return n}}),r)}}}P.apSecond=tc;function nc(e){return function(r,t){return function(n){return e.ap(e.map(n,function(i){return function(o){var c;return Object.assign({},i,(c={},c[r]=o,c))}}),t)}}}P.apS=nc;function ic(e){return function(r){return{concat:function(t,n){return e.ap(e.map(t,function(i){return function(o){return r.concat(i,o)}}),n)}}}}P.getApplySemigroup=ic;function Lr(e,r,t){return function(n){for(var i=Array(t.length+1),o=0;o<t.length;o++)i[o]=t[o];return i[t.length]=n,r===0?e.apply(null,i):Lr(e,r-1,i)}}var Ir={1:function(e){return[e]},2:function(e){return function(r){return[e,r]}},3:function(e){return function(r){return function(t){return[e,r,t]}}},4:function(e){return function(r){return function(t){return function(n){return[e,r,t,n]}}}},5:function(e){return function(r){return function(t){return function(n){return function(i){return[e,r,t,n,i]}}}}}};function sc(e){return Qu.has.call(Ir,e)||(Ir[e]=Lr(Zu.tuple,e-1,[])),Ir[e]}function oc(e){return function(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var n=r.length,i=sc(n),o=e.map(r[0],i),c=1;c<n;c++)o=e.ap(o,r[c]);return o}}P.sequenceT=oc;function ac(e){var r=e.length;switch(r){case 1:return function(t){var n;return n={},n[e[0]]=t,n};case 2:return function(t){return function(n){var i;return i={},i[e[0]]=t,i[e[1]]=n,i}};case 3:return function(t){return function(n){return function(i){var o;return o={},o[e[0]]=t,o[e[1]]=n,o[e[2]]=i,o}}};case 4:return function(t){return function(n){return function(i){return function(o){var c;return c={},c[e[0]]=t,c[e[1]]=n,c[e[2]]=i,c[e[3]]=o,c}}}};case 5:return function(t){return function(n){return function(i){return function(o){return function(c){var u;return u={},u[e[0]]=t,u[e[1]]=n,u[e[2]]=i,u[e[3]]=o,u[e[4]]=c,u}}}}};default:return Lr(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var i={},o=0;o<r;o++)i[e[o]]=t[o];return i},r-1,[])}}function uc(e){return function(r){for(var t=Object.keys(r),n=t.length,i=ac(t),o=e.map(r[t[0]],i),c=1;c<n;c++)o=e.ap(o,r[t[c]]);return o}}P.sequenceS=uc});var Qe=E(B=>{"use strict";Object.defineProperty(B,"__esModule",{value:!0});B.asUnit=B.as=B.getFunctorComposition=B.let=B.bindTo=B.flap=B.map=void 0;var cc=ae();function Kn(e,r){return function(t){return function(n){return e.map(n,function(i){return r.map(i,t)})}}}B.map=Kn;function lc(e){return function(r){return function(t){return e.map(t,function(n){return n(r)})}}}B.flap=lc;function fc(e){return function(r){return function(t){return e.map(t,function(n){var i;return i={},i[r]=n,i})}}}B.bindTo=fc;function pc(e){return function(r,t){return function(n){return e.map(n,function(i){var o;return Object.assign({},i,(o={},o[r]=t(i),o))})}}}B.let=pc;function gc(e,r){var t=Kn(e,r);return{map:function(n,i){return(0,cc.pipe)(n,t(i))}}}B.getFunctorComposition=gc;function kn(e){return function(r,t){return e.map(r,function(){return t})}}B.as=kn;function mc(e){var r=kn(e);return function(t){return r(t,void 0)}}B.asUnit=mc});var Hn=E(Ae=>{"use strict";Object.defineProperty(Ae,"__esModule",{value:!0});Ae.getApplicativeComposition=Ae.getApplicativeMonoid=void 0;var Xn=Nr(),hc=ae(),dc=Qe();function vc(e){var r=(0,Xn.getApplySemigroup)(e);return function(t){return{concat:r(t).concat,empty:e.of(t.empty)}}}Ae.getApplicativeMonoid=vc;function yc(e,r){var t=(0,dc.getFunctorComposition)(e,r).map,n=(0,Xn.ap)(e,r);return{map:t,of:function(i){return e.of(r.of(i))},ap:function(i,o){return(0,hc.pipe)(i,n(o))}}}Ae.getApplicativeComposition=yc});var _r=E(ue=>{"use strict";Object.defineProperty(ue,"__esModule",{value:!0});ue.bind=ue.tap=ue.chainFirst=void 0;function Ac(e){var r=zn(e);return function(t){return function(n){return r(n,t)}}}ue.chainFirst=Ac;function zn(e){return function(r,t){return e.chain(r,function(n){return e.map(t(n),function(){return n})})}}ue.tap=zn;function Ec(e){return function(r,t){return function(n){return e.chain(n,function(i){return e.map(t(i),function(o){var c;return Object.assign({},i,(c={},c[r]=o,c))})})}}}ue.bind=Ec});var Yn=E(er=>{"use strict";Object.defineProperty(er,"__esModule",{value:!0});er.tailRec=void 0;var Tc=function(e,r){for(var t=r(e);t._tag==="Left";)t=r(t.left);return t.right};er.tailRec=Tc});var ri=E(_=>{"use strict";var Rc=_&&_.__createBinding||(Object.create?function(e,r,t,n){n===void 0&&(n=t);var i=Object.getOwnPropertyDescriptor(r,t);(!i||("get"in i?!r.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,n,i)}:function(e,r,t,n){n===void 0&&(n=t),e[n]=r[t]}),wc=_&&_.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),xc=_&&_.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var t in e)t!=="default"&&Object.prototype.hasOwnProperty.call(e,t)&&Rc(r,e,t);return wc(r,e),r};Object.defineProperty(_,"__esModule",{value:!0});_.tapEither=_.filterOrElse=_.chainFirstEitherK=_.chainEitherK=_.fromEitherK=_.chainOptionK=_.fromOptionK=_.fromPredicate=_.fromOption=void 0;var Oc=_r(),Jn=ae(),fe=xc($e());function Zn(e){return function(r){return function(t){return e.fromEither(fe.isNone(t)?fe.left(r()):fe.right(t.value))}}}_.fromOption=Zn;function bc(e){return function(r,t){return function(n){return e.fromEither(r(n)?fe.right(n):fe.left(t(n)))}}}_.fromPredicate=bc;function Qn(e){var r=Zn(e);return function(t){var n=r(t);return function(i){return(0,Jn.flow)(i,n)}}}_.fromOptionK=Qn;function Sc(e,r){var t=Qn(e);return function(n){var i=t(n);return function(o){return function(c){return r.chain(c,i(o))}}}}_.chainOptionK=Sc;function $r(e){return function(r){return(0,Jn.flow)(r,e.fromEither)}}_.fromEitherK=$r;function Ic(e,r){var t=$r(e);return function(n){return function(i){return r.chain(i,t(n))}}}_.chainEitherK=Ic;function Lc(e,r){var t=ei(e,r);return function(n){return function(i){return t(i,n)}}}_.chainFirstEitherK=Lc;function Nc(e,r){return function(t,n){return function(i){return r.chain(i,function(o){return e.fromEither(t(o)?fe.right(o):fe.left(n(o)))})}}}_.filterOrElse=Nc;function ei(e,r){var t=$r(e),n=(0,Oc.tap)(r);return function(i,o){return n(i,t(o))}}_.tapEither=ei});var ti=E(x=>{"use strict";Object.defineProperty(x,"__esModule",{value:!0});x.right=x.left=x.flap=x.Functor=x.Bifunctor=x.URI=x.bimap=x.mapLeft=x.map=x.separated=void 0;var Cr=ae(),_c=Qe(),$c=function(e,r){return{left:e,right:r}};x.separated=$c;var Cc=function(e,r){return(0,Cr.pipe)(e,(0,x.map)(r))},Pc=function(e,r){return(0,Cr.pipe)(e,(0,x.mapLeft)(r))},Uc=function(e,r,t){return(0,Cr.pipe)(e,(0,x.bimap)(r,t))},qc=function(e){return function(r){return(0,x.separated)((0,x.left)(r),e((0,x.right)(r)))}};x.map=qc;var Mc=function(e){return function(r){return(0,x.separated)(e((0,x.left)(r)),(0,x.right)(r))}};x.mapLeft=Mc;var jc=function(e,r){return function(t){return(0,x.separated)(e((0,x.left)(t)),r((0,x.right)(t)))}};x.bimap=jc;x.URI="Separated";x.Bifunctor={URI:x.URI,mapLeft:Pc,bimap:Uc};x.Functor={URI:x.URI,map:Cc};x.flap=(0,_c.flap)(x.Functor);var Fc=function(e){return e.left};x.left=Fc;var Dc=function(e){return e.right};x.right=Dc});var ii=E(H=>{"use strict";var Vc=H&&H.__createBinding||(Object.create?function(e,r,t,n){n===void 0&&(n=t);var i=Object.getOwnPropertyDescriptor(r,t);(!i||("get"in i?!r.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,n,i)}:function(e,r,t,n){n===void 0&&(n=t),e[n]=r[t]}),Wc=H&&H.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),Bc=H&&H.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var t in e)t!=="default"&&Object.prototype.hasOwnProperty.call(e,t)&&Vc(r,e,t);return Wc(r,e),r};Object.defineProperty(H,"__esModule",{value:!0});H.filterE=H.witherDefault=H.wiltDefault=void 0;var ni=Bc($e());function Gc(e,r){return function(t){var n=e.traverse(t);return function(i,o){return t.map(n(i,o),r.separate)}}}H.wiltDefault=Gc;function Kc(e,r){return function(t){var n=e.traverse(t);return function(i,o){return t.map(n(i,o),r.compact)}}}H.witherDefault=Kc;function kc(e){return function(r){var t=e.wither(r);return function(n){return function(i){return t(i,function(o){return r.map(n(o),function(c){return c?ni.some(o):ni.none})})}}}}H.filterE=kc});var pi=E(s=>{"use strict";var Xc=s&&s.__createBinding||(Object.create?function(e,r,t,n){n===void 0&&(n=t);var i=Object.getOwnPropertyDescriptor(r,t);(!i||("get"in i?!r.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,n,i)}:function(e,r,t,n){n===void 0&&(n=t),e[n]=r[t]}),Hc=s&&s.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),oi=s&&s.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var t in e)t!=="default"&&Object.prototype.hasOwnProperty.call(e,t)&&Xc(r,e,t);return Hc(r,e),r};Object.defineProperty(s,"__esModule",{value:!0});s.match=s.foldW=s.matchW=s.isRight=s.isLeft=s.fromOption=s.fromPredicate=s.FromEither=s.MonadThrow=s.throwError=s.ChainRec=s.Extend=s.extend=s.Alt=s.alt=s.altW=s.Bifunctor=s.mapLeft=s.bimap=s.Traversable=s.sequence=s.traverse=s.Foldable=s.reduceRight=s.foldMap=s.reduce=s.Monad=s.Chain=s.Applicative=s.Apply=s.ap=s.apW=s.Pointed=s.of=s.asUnit=s.as=s.Functor=s.map=s.getAltValidation=s.getApplicativeValidation=s.getWitherable=s.getFilterable=s.getCompactable=s.getSemigroup=s.getEq=s.getShow=s.URI=s.flatMap=s.right=s.left=void 0;s.chainFirstW=s.chainFirst=s.chain=s.chainW=s.sequenceArray=s.traverseArray=s.traverseArrayWithIndex=s.traverseReadonlyArrayWithIndex=s.traverseReadonlyNonEmptyArrayWithIndex=s.ApT=s.apSW=s.apS=s.bindW=s.bind=s.let=s.bindTo=s.Do=s.exists=s.elem=s.toError=s.toUnion=s.chainNullableK=s.fromNullableK=s.tryCatchK=s.tryCatch=s.fromNullable=s.orElse=s.orElseW=s.swap=s.filterOrElseW=s.filterOrElse=s.flatMapOption=s.flatMapNullable=s.liftOption=s.liftNullable=s.chainOptionKW=s.chainOptionK=s.fromOptionK=s.duplicate=s.flatten=s.flattenW=s.tap=s.apSecondW=s.apSecond=s.apFirstW=s.apFirst=s.flap=s.getOrElse=s.getOrElseW=s.fold=void 0;s.getValidation=s.getValidationMonoid=s.getValidationSemigroup=s.getApplyMonoid=s.getApplySemigroup=s.either=s.stringifyJSON=s.parseJSON=void 0;var ai=Hn(),Ce=Nr(),ui=oi(_r()),zc=Yn(),Pe=ri(),M=ae(),Ue=Qe(),ee=oi($e()),ie=ti(),si=ii();s.left=ee.left;s.right=ee.right;s.flatMap=(0,M.dual)(2,function(e,r){return(0,s.isLeft)(e)?e:r(e.right)});var G=function(e,r){return(0,M.pipe)(e,(0,s.map)(r))},pe=function(e,r){return(0,M.pipe)(e,(0,s.ap)(r))},qe=function(e,r,t){return(0,M.pipe)(e,(0,s.reduce)(r,t))},Me=function(e){return function(r,t){var n=(0,s.foldMap)(e);return(0,M.pipe)(r,n(t))}},je=function(e,r,t){return(0,M.pipe)(e,(0,s.reduceRight)(r,t))},rr=function(e){var r=(0,s.traverse)(e);return function(t,n){return(0,M.pipe)(t,r(n))}},Pr=function(e,r,t){return(0,M.pipe)(e,(0,s.bimap)(r,t))},Ur=function(e,r){return(0,M.pipe)(e,(0,s.mapLeft)(r))},ci=function(e,r){return(0,M.pipe)(e,(0,s.alt)(r))},qr=function(e,r){return(0,M.pipe)(e,(0,s.extend)(r))},Mr=function(e,r){return(0,zc.tailRec)(r(e),function(t){return(0,s.isLeft)(t)?(0,s.right)((0,s.left)(t.left)):(0,s.isLeft)(t.right)?(0,s.left)(r(t.right.left)):(0,s.right)((0,s.right)(t.right.right))})};s.URI="Either";var Yc=function(e,r){return{show:function(t){return(0,s.isLeft)(t)?"left(".concat(e.show(t.left),")"):"right(".concat(r.show(t.right),")")}}};s.getShow=Yc;var Jc=function(e,r){return{equals:function(t,n){return t===n||((0,s.isLeft)(t)?(0,s.isLeft)(n)&&e.equals(t.left,n.left):(0,s.isRight)(n)&&r.equals(t.right,n.right))}}};s.getEq=Jc;var Zc=function(e){return{concat:function(r,t){return(0,s.isLeft)(t)?r:(0,s.isLeft)(r)?t:(0,s.right)(e.concat(r.right,t.right))}}};s.getSemigroup=Zc;var Qc=function(e){var r=(0,s.left)(e.empty);return{URI:s.URI,_E:void 0,compact:function(t){return(0,s.isLeft)(t)?t:t.right._tag==="None"?r:(0,s.right)(t.right.value)},separate:function(t){return(0,s.isLeft)(t)?(0,ie.separated)(t,t):(0,s.isLeft)(t.right)?(0,ie.separated)((0,s.right)(t.right.left),r):(0,ie.separated)(r,(0,s.right)(t.right.right))}}};s.getCompactable=Qc;var el=function(e){var r=(0,s.left)(e.empty),t=(0,s.getCompactable)(e),n=t.compact,i=t.separate,o=function(u,l){return(0,s.isLeft)(u)||l(u.right)?u:r},c=function(u,l){return(0,s.isLeft)(u)?(0,ie.separated)(u,u):l(u.right)?(0,ie.separated)(r,(0,s.right)(u.right)):(0,ie.separated)((0,s.right)(u.right),r)};return{URI:s.URI,_E:void 0,map:G,compact:n,separate:i,filter:o,filterMap:function(u,l){if((0,s.isLeft)(u))return u;var f=l(u.right);return f._tag==="None"?r:(0,s.right)(f.value)},partition:c,partitionMap:function(u,l){if((0,s.isLeft)(u))return(0,ie.separated)(u,u);var f=l(u.right);return(0,s.isLeft)(f)?(0,ie.separated)((0,s.right)(f.left),r):(0,ie.separated)(r,(0,s.right)(f.right))}}};s.getFilterable=el;var rl=function(e){var r=(0,s.getFilterable)(e),t=(0,s.getCompactable)(e);return{URI:s.URI,_E:void 0,map:G,compact:r.compact,separate:r.separate,filter:r.filter,filterMap:r.filterMap,partition:r.partition,partitionMap:r.partitionMap,traverse:rr,sequence:s.sequence,reduce:qe,foldMap:Me,reduceRight:je,wither:(0,si.witherDefault)(s.Traversable,t),wilt:(0,si.wiltDefault)(s.Traversable,t)}};s.getWitherable=rl;var tl=function(e){return{URI:s.URI,_E:void 0,map:G,ap:function(r,t){return(0,s.isLeft)(r)?(0,s.isLeft)(t)?(0,s.left)(e.concat(r.left,t.left)):r:(0,s.isLeft)(t)?t:(0,s.right)(r.right(t.right))},of:s.of}};s.getApplicativeValidation=tl;var nl=function(e){return{URI:s.URI,_E:void 0,map:G,alt:function(r,t){if((0,s.isRight)(r))return r;var n=t();return(0,s.isLeft)(n)?(0,s.left)(e.concat(r.left,n.left)):n}}};s.getAltValidation=nl;var il=function(e){return function(r){return(0,s.isLeft)(r)?r:(0,s.right)(e(r.right))}};s.map=il;s.Functor={URI:s.URI,map:G};s.as=(0,M.dual)(2,(0,Ue.as)(s.Functor));s.asUnit=(0,Ue.asUnit)(s.Functor);s.of=s.right;s.Pointed={URI:s.URI,of:s.of};var sl=function(e){return function(r){return(0,s.isLeft)(r)?r:(0,s.isLeft)(e)?e:(0,s.right)(r.right(e.right))}};s.apW=sl;s.ap=s.apW;s.Apply={URI:s.URI,map:G,ap:pe};s.Applicative={URI:s.URI,map:G,ap:pe,of:s.of};s.Chain={URI:s.URI,map:G,ap:pe,chain:s.flatMap};s.Monad={URI:s.URI,map:G,ap:pe,of:s.of,chain:s.flatMap};var ol=function(e,r){return function(t){return(0,s.isLeft)(t)?e:r(e,t.right)}};s.reduce=ol;var al=function(e){return function(r){return function(t){return(0,s.isLeft)(t)?e.empty:r(t.right)}}};s.foldMap=al;var ul=function(e,r){return function(t){return(0,s.isLeft)(t)?e:r(t.right,e)}};s.reduceRight=ul;s.Foldable={URI:s.URI,reduce:qe,foldMap:Me,reduceRight:je};var cl=function(e){return function(r){return function(t){return(0,s.isLeft)(t)?e.of((0,s.left)(t.left)):e.map(r(t.right),s.right)}}};s.traverse=cl;var ll=function(e){return function(r){return(0,s.isLeft)(r)?e.of((0,s.left)(r.left)):e.map(r.right,s.right)}};s.sequence=ll;s.Traversable={URI:s.URI,map:G,reduce:qe,foldMap:Me,reduceRight:je,traverse:rr,sequence:s.sequence};var fl=function(e,r){return function(t){return(0,s.isLeft)(t)?(0,s.left)(e(t.left)):(0,s.right)(r(t.right))}};s.bimap=fl;var pl=function(e){return function(r){return(0,s.isLeft)(r)?(0,s.left)(e(r.left)):r}};s.mapLeft=pl;s.Bifunctor={URI:s.URI,bimap:Pr,mapLeft:Ur};var gl=function(e){return function(r){return(0,s.isLeft)(r)?e():r}};s.altW=gl;s.alt=s.altW;s.Alt={URI:s.URI,map:G,alt:ci};var ml=function(e){return function(r){return(0,s.isLeft)(r)?r:(0,s.right)(e(r))}};s.extend=ml;s.Extend={URI:s.URI,map:G,extend:qr};s.ChainRec={URI:s.URI,map:G,ap:pe,chain:s.flatMap,chainRec:Mr};s.throwError=s.left;s.MonadThrow={URI:s.URI,map:G,ap:pe,of:s.of,chain:s.flatMap,throwError:s.throwError};s.FromEither={URI:s.URI,fromEither:M.identity};s.fromPredicate=(0,Pe.fromPredicate)(s.FromEither);s.fromOption=(0,Pe.fromOption)(s.FromEither);s.isLeft=ee.isLeft;s.isRight=ee.isRight;var hl=function(e,r){return function(t){return(0,s.isLeft)(t)?e(t.left):r(t.right)}};s.matchW=hl;s.foldW=s.matchW;s.match=s.matchW;s.fold=s.match;var dl=function(e){return function(r){return(0,s.isLeft)(r)?e(r.left):r.right}};s.getOrElseW=dl;s.getOrElse=s.getOrElseW;s.flap=(0,Ue.flap)(s.Functor);s.apFirst=(0,Ce.apFirst)(s.Apply);s.apFirstW=s.apFirst;s.apSecond=(0,Ce.apSecond)(s.Apply);s.apSecondW=s.apSecond;s.tap=(0,M.dual)(2,ui.tap(s.Chain));s.flattenW=(0,s.flatMap)(M.identity);s.flatten=s.flattenW;s.duplicate=(0,s.extend)(M.identity);s.fromOptionK=(0,Pe.fromOptionK)(s.FromEither);s.chainOptionK=(0,Pe.chainOptionK)(s.FromEither,s.Chain);s.chainOptionKW=s.chainOptionK;var tr={fromEither:s.FromEither.fromEither};s.liftNullable=ee.liftNullable(tr);s.liftOption=ee.liftOption(tr);var li={flatMap:s.flatMap};s.flatMapNullable=ee.flatMapNullable(tr,li);s.flatMapOption=ee.flatMapOption(tr,li);s.filterOrElse=(0,Pe.filterOrElse)(s.FromEither,s.Chain);s.filterOrElseW=s.filterOrElse;var vl=function(e){return(0,s.isLeft)(e)?(0,s.right)(e.left):(0,s.left)(e.right)};s.swap=vl;var yl=function(e){return function(r){return(0,s.isLeft)(r)?e(r.left):r}};s.orElseW=yl;s.orElse=s.orElseW;var Al=function(e){return function(r){return r==null?(0,s.left)(e):(0,s.right)(r)}};s.fromNullable=Al;var El=function(e,r){try{return(0,s.right)(e())}catch(t){return(0,s.left)(r(t))}};s.tryCatch=El;var Tl=function(e,r){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return(0,s.tryCatch)(function(){return e.apply(void 0,t)},r)}};s.tryCatchK=Tl;var Rl=function(e){var r=(0,s.fromNullable)(e);return function(t){return(0,M.flow)(t,r)}};s.fromNullableK=Rl;var wl=function(e){var r=(0,s.fromNullableK)(e);return function(t){return(0,s.flatMap)(r(t))}};s.chainNullableK=wl;s.toUnion=(0,s.foldW)(M.identity,M.identity);function xl(e){try{return e instanceof Error?e:new Error(String(e))}catch{return new Error}}s.toError=xl;function fi(e){return function(r,t){if(t===void 0){var n=fi(e);return function(i){return n(r,i)}}return(0,s.isLeft)(t)?!1:e.equals(r,t.right)}}s.elem=fi;var Ol=function(e){return function(r){return(0,s.isLeft)(r)?!1:e(r.right)}};s.exists=Ol;s.Do=(0,s.of)(ee.emptyRecord);s.bindTo=(0,Ue.bindTo)(s.Functor);var bl=(0,Ue.let)(s.Functor);s.let=bl;s.bind=ui.bind(s.Chain);s.bindW=s.bind;s.apS=(0,Ce.apS)(s.Apply);s.apSW=s.apS;s.ApT=(0,s.of)(ee.emptyReadonlyArray);var Sl=function(e){return function(r){var t=e(0,ee.head(r));if((0,s.isLeft)(t))return t;for(var n=[t.right],i=1;i<r.length;i++){var o=e(i,r[i]);if((0,s.isLeft)(o))return o;n.push(o.right)}return(0,s.right)(n)}};s.traverseReadonlyNonEmptyArrayWithIndex=Sl;var Il=function(e){var r=(0,s.traverseReadonlyNonEmptyArrayWithIndex)(e);return function(t){return ee.isNonEmpty(t)?r(t):s.ApT}};s.traverseReadonlyArrayWithIndex=Il;s.traverseArrayWithIndex=s.traverseReadonlyArrayWithIndex;var Ll=function(e){return(0,s.traverseReadonlyArrayWithIndex)(function(r,t){return e(t)})};s.traverseArray=Ll;s.sequenceArray=(0,s.traverseArray)(M.identity);s.chainW=s.flatMap;s.chain=s.flatMap;s.chainFirst=s.tap;s.chainFirstW=s.tap;function Nl(e,r){return(0,s.tryCatch)(function(){return JSON.parse(e)},r)}s.parseJSON=Nl;var _l=function(e,r){return(0,s.tryCatch)(function(){var t=JSON.stringify(e);if(typeof t!="string")throw new Error("Converting unsupported structure to JSON");return t},r)};s.stringifyJSON=_l;s.either={URI:s.URI,map:G,of:s.of,ap:pe,chain:s.flatMap,reduce:qe,foldMap:Me,reduceRight:je,traverse:rr,sequence:s.sequence,bimap:Pr,mapLeft:Ur,alt:ci,extend:qr,chainRec:Mr,throwError:s.throwError};s.getApplySemigroup=(0,Ce.getApplySemigroup)(s.Apply);s.getApplyMonoid=(0,ai.getApplicativeMonoid)(s.Applicative);var $l=function(e,r){return(0,Ce.getApplySemigroup)((0,s.getApplicativeValidation)(e))(r)};s.getValidationSemigroup=$l;var Cl=function(e,r){return(0,ai.getApplicativeMonoid)((0,s.getApplicativeValidation)(e))(r)};s.getValidationMonoid=Cl;function Pl(e){var r=(0,s.getApplicativeValidation)(e).ap,t=(0,s.getAltValidation)(e).alt;return{URI:s.URI,_E:void 0,map:G,of:s.of,chain:s.flatMap,bimap:Pr,mapLeft:Ur,reduce:qe,foldMap:Me,reduceRight:je,extend:qr,traverse:rr,sequence:s.sequence,chainRec:Mr,throwError:s.throwError,ap:r,alt:t}}s.getValidation=Pl});var es=E(a=>{"use strict";var S=a&&a.__extends||function(){var e=function(r,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(n[o]=i[o])},e(r,t)};return function(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");e(r,t);function n(){this.constructor=r}r.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}(),Ee=a&&a.__assign||function(){return Ee=Object.assign||function(e){for(var r,t=1,n=arguments.length;t<n;t++){r=arguments[t];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e},Ee.apply(this,arguments)},Ul=a&&a.__spreadArray||function(e,r,t){if(t||arguments.length===2)for(var n=0,i=r.length,o;n<i;n++)(o||!(n in r))&&(o||(o=Array.prototype.slice.call(r,0,n)),o[n]=r[n]);return e.concat(o||Array.prototype.slice.call(r))};Object.defineProperty(a,"__esModule",{value:!0});a.partial=a.PartialType=a.type=a.InterfaceType=a.array=a.ArrayType=a.recursion=a.RecursiveType=a.Int=a.brand=a.RefinementType=a.keyof=a.KeyofType=a.literal=a.LiteralType=a.void=a.undefined=a.null=a.UnknownRecord=a.AnyDictionaryType=a.UnknownArray=a.AnyArrayType=a.boolean=a.BooleanType=a.bigint=a.BigIntType=a.number=a.NumberType=a.string=a.StringType=a.unknown=a.UnknownType=a.voidType=a.VoidType=a.UndefinedType=a.nullType=a.NullType=a.getIndex=a.getTags=a.emptyTags=a.mergeAll=a.getDomainKeys=a.appendContext=a.getContextEntry=a.getFunctionName=a.identity=a.Type=a.success=a.failure=a.failures=void 0;a.alias=a.clean=a.StrictType=a.dictionary=a.object=a.ObjectType=a.Dictionary=a.getDefaultContext=a.getValidationError=a.interface=a.Array=a.taggedUnion=a.TaggedUnionType=a.Integer=a.refinement=a.any=a.AnyType=a.never=a.NeverType=a.Function=a.FunctionType=a.exact=a.ExactType=a.strict=a.readonlyArray=a.ReadonlyArrayType=a.readonly=a.ReadonlyType=a.tuple=a.TupleType=a.intersection=a.IntersectionType=a.union=a.UnionType=a.record=a.DictionaryType=void 0;var U=pi();a.failures=U.left;var ql=function(e,r,t){return(0,a.failures)([{value:e,context:r,message:t}])};a.failure=ql;a.success=U.right;var I=function(){function e(r,t,n,i){this.name=r,this.is=t,this.validate=n,this.encode=i,this.decode=this.decode.bind(this)}return e.prototype.pipe=function(r,t){var n=this;return t===void 0&&(t="pipe(".concat(this.name,", ").concat(r.name,")")),new e(t,r.is,function(i,o){var c=n.validate(i,o);return(0,U.isLeft)(c)?c:r.validate(c.right,o)},this.encode===a.identity&&r.encode===a.identity?a.identity:function(i){return n.encode(r.encode(i))})},e.prototype.asDecoder=function(){return this},e.prototype.asEncoder=function(){return this},e.prototype.decode=function(r){return this.validate(r,[{key:"",type:this,actual:r}])},e}();a.Type=I;var Ml=function(e){return e};a.identity=Ml;function hi(e){return e.displayName||e.name||"<function".concat(e.length,">")}a.getFunctionName=hi;function jl(e,r){return{key:e,type:r}}a.getContextEntry=jl;function re(e,r,t,n){for(var i=e.length,o=Array(i+1),c=0;c<i;c++)o[c]=e[c];return o[i]={key:r,type:t,actual:n},o}a.appendContext=re;function se(e,r){for(var t=r.length,n=0;n<t;n++)e.push(r[n])}var ge=Object.prototype.hasOwnProperty;function Fr(e){return Object.keys(e).map(function(r){return"".concat(r,": ").concat(e[r].name)}).join(", ")}function Fe(e){for(var r=0;r<e.length;r++)if(e[r].encode!==a.identity)return!1;return!0}function di(e){return"{ ".concat(Fr(e)," }")}function vi(e){return"Partial<".concat(e,">")}function Fl(e,r,t,n){n===void 0&&(n="{ [K in ".concat(r.name,"]: ").concat(t.name," }"));var i=e.length;return new Kr(n,function(o){return a.UnknownRecord.is(o)&&e.every(function(c){return t.is(o[c])})},function(o,c){var u=a.UnknownRecord.validate(o,c);if((0,U.isLeft)(u))return u;for(var l=u.right,f={},g=[],p=!1,v=0;v<i;v++){var m=e[v],j=l[m],C=t.validate(j,re(c,m,t,j));if((0,U.isLeft)(C))se(g,C.left);else{var V=C.right;p=p||V!==j,f[m]=V}}return g.length>0?(0,a.failures)(g):(0,a.success)(p||Object.keys(l).length!==i?f:l)},t.encode===a.identity?a.identity:function(o){for(var c={},u=0;u<i;u++){var l=e[u];c[l]=t.encode(o[l])}return c},r,t)}function Wr(e){var r;if(Ei(e)){var t=e.value;if(a.string.is(t))return r={},r[t]=null,r}else{if(Kl(e))return e.keys;if(Ri(e)){var n=e.types.map(function(i){return Wr(i)});return n.some(nr.is)?void 0:Object.assign.apply(Object,Ul([{}],n,!1))}}}a.getDomainKeys=Wr;function Dl(e,r,t){return t===void 0&&(t="{ [K in ".concat(e.name,"]: ").concat(r.name," }")),new Kr(t,function(n){return a.UnknownRecord.is(n)?Object.keys(n).every(function(i){return e.is(i)&&r.is(n[i])}):mi(r)&&Array.isArray(n)},function(n,i){if(a.UnknownRecord.is(n)){for(var o={},c=[],u=Object.keys(n),l=u.length,f=!1,g=0;g<l;g++){var p=u[g],v=n[p],m=e.validate(p,re(i,p,e,p));if((0,U.isLeft)(m))se(c,m.left);else{var j=m.right;f=f||j!==p,p=j;var C=r.validate(v,re(i,p,r,v));if((0,U.isLeft)(C))se(c,C.left);else{var V=C.right;f=f||V!==v,o[p]=V}}}return c.length>0?(0,a.failures)(c):(0,a.success)(f?o:n)}return mi(r)&&Array.isArray(n)?(0,a.success)(n):(0,a.failure)(n,i)},e.encode===a.identity&&r.encode===a.identity?a.identity:function(n){for(var i={},o=Object.keys(n),c=o.length,u=0;u<c;u++){var l=o[u];i[String(e.encode(l))]=r.encode(n[l])}return i},e,r)}function yi(e){return"("+e.map(function(r){return r.name}).join(" | ")+")"}function Dr(e,r){for(var t=!0,n=!0,i=!a.UnknownRecord.is(e),o=0,c=r;o<c.length;o++){var u=c[o];u!==e&&(t=!1),a.UnknownRecord.is(u)&&(n=!1)}if(t)return e;if(n)return r[r.length-1];for(var l={},f=0,g=r;f<g.length;f++){var u=g[f];for(var p in u)(!ge.call(l,p)||i||u[p]!==e[p])&&(l[p]=u[p])}return l}a.mergeAll=Dr;function Vr(e){switch(e._tag){case"RefinementType":case"ReadonlyType":return Vr(e.type);case"InterfaceType":case"StrictType":case"PartialType":return e.props;case"IntersectionType":return e.types.reduce(function(r,t){return Object.assign(r,Vr(t))},{})}}function gi(e,r){for(var t=Object.getOwnPropertyNames(e),n=!1,i={},o=0;o<t.length;o++){var c=t[o];ge.call(r,c)?i[c]=e[c]:n=!0}return n?i:e}function Vl(e){return Ti(e)?"{| ".concat(Fr(e.props)," |}"):kl(e)?vi("{| ".concat(Fr(e.props)," |}")):"Exact<".concat(e.name,">")}function Wl(e){return e.length>0}a.emptyTags={};function Ai(e,r){for(var t=[],n=0,i=e;n<i.length;n++){var o=i[n];r.indexOf(o)!==-1&&t.push(o)}return t}function Bl(e,r){if(e===a.emptyTags)return r;if(r===a.emptyTags)return e;var t=Object.assign({},e);for(var n in r)if(ge.call(e,n)){var i=Ai(e[n],r[n]);if(Wl(i))t[n]=i;else{t=a.emptyTags;break}}else t[n]=r[n];return t}function Gl(e,r){if(e===a.emptyTags||r===a.emptyTags)return a.emptyTags;var t=a.emptyTags;for(var n in e)if(ge.call(r,n)){var i=Ai(e[n],r[n]);i.length===0&&(t===a.emptyTags&&(t={}),t[n]=e[n].concat(r[n]))}return t}function mi(e){return e._tag==="AnyType"}function Ei(e){return e._tag==="LiteralType"}function Kl(e){return e._tag==="KeyofType"}function Ti(e){return e._tag==="InterfaceType"}function kl(e){return e._tag==="PartialType"}function Xl(e){return e._tag==="StrictType"}function Hl(e){return e._tag==="ExactType"}function zl(e){return e._tag==="RefinementType"}function Yl(e){return e._tag==="IntersectionType"}function Ri(e){return e._tag==="UnionType"}function Jl(e){return e._tag==="RecursiveType"}var jr=[];function ce(e){if(jr.indexOf(e)!==-1)return a.emptyTags;if(Ti(e)||Xl(e)){var r=a.emptyTags;for(var t in e.props){var n=e.props[t];Ei(n)&&(r===a.emptyTags&&(r={}),r[t]=[n.value])}return r}else{if(Hl(e)||zl(e))return ce(e.type);if(Yl(e))return e.types.reduce(function(o,c){return Bl(o,ce(c))},a.emptyTags);if(Ri(e))return e.types.slice(1).reduce(function(o,c){return Gl(o,ce(c))},ce(e.types[0]));if(Jl(e)){jr.push(e);var i=ce(e.type);return jr.pop(),i}}return a.emptyTags}a.getTags=ce;function wi(e){var r=ce(e[0]),t=Object.keys(r),n=e.length,i=function(f){for(var g=r[f].slice(),p=[r[f]],v=1;v<n;v++){var m=e[v],j=ce(m),C=j[f];if(C===void 0)return"continue-keys";if(C.some(function(V){return g.indexOf(V)!==-1}))return"continue-keys";g.push.apply(g,C),p.push(C)}return{value:[f,p]}};e:for(var o=0,c=t;o<c.length;o++){var u=c[o],l=i(u);if(typeof l=="object")return l.value;switch(l){case"continue-keys":continue e}}}a.getIndex=wi;var xi=function(e){S(r,e);function r(){var t=e.call(this,"null",function(n){return n===null},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="NullType",t}return r}(I);a.NullType=xi;a.nullType=new xi;a.null=a.nullType;var Oi=function(e){S(r,e);function r(){var t=e.call(this,"undefined",function(n){return n===void 0},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="UndefinedType",t}return r}(I);a.UndefinedType=Oi;var nr=new Oi;a.undefined=nr;var bi=function(e){S(r,e);function r(){var t=e.call(this,"void",nr.is,nr.validate,a.identity)||this;return t._tag="VoidType",t}return r}(I);a.VoidType=bi;a.voidType=new bi;a.void=a.voidType;var Si=function(e){S(r,e);function r(){var t=e.call(this,"unknown",function(n){return!0},a.success,a.identity)||this;return t._tag="UnknownType",t}return r}(I);a.UnknownType=Si;a.unknown=new Si;var Ii=function(e){S(r,e);function r(){var t=e.call(this,"string",function(n){return typeof n=="string"},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="StringType",t}return r}(I);a.StringType=Ii;a.string=new Ii;var Li=function(e){S(r,e);function r(){var t=e.call(this,"number",function(n){return typeof n=="number"},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="NumberType",t}return r}(I);a.NumberType=Li;a.number=new Li;var Ni=function(e){S(r,e);function r(){var t=e.call(this,"bigint",function(n){return typeof n=="bigint"},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="BigIntType",t}return r}(I);a.BigIntType=Ni;a.bigint=new Ni;var _i=function(e){S(r,e);function r(){var t=e.call(this,"boolean",function(n){return typeof n=="boolean"},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="BooleanType",t}return r}(I);a.BooleanType=_i;a.boolean=new _i;var $i=function(e){S(r,e);function r(){var t=e.call(this,"UnknownArray",Array.isArray,function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="AnyArrayType",t}return r}(I);a.AnyArrayType=$i;a.UnknownArray=new $i;a.Array=a.UnknownArray;var Ci=function(e){S(r,e);function r(){var t=e.call(this,"UnknownRecord",function(n){return n!==null&&typeof n=="object"&&!Array.isArray(n)},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="AnyDictionaryType",t}return r}(I);a.AnyDictionaryType=Ci;a.UnknownRecord=new Ci;var Pi=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.value=c,u._tag="LiteralType",u}return r}(I);a.LiteralType=Pi;function Zl(e,r){r===void 0&&(r=JSON.stringify(e));var t=function(n){return n===e};return new Pi(r,t,function(n,i){return t(n)?(0,a.success)(e):(0,a.failure)(n,i)},a.identity,e)}a.literal=Zl;var Ui=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.keys=c,u._tag="KeyofType",u}return r}(I);a.KeyofType=Ui;function Ql(e,r){r===void 0&&(r=Object.keys(e).map(function(n){return JSON.stringify(n)}).join(" | "));var t=function(n){return a.string.is(n)&&ge.call(e,n)};return new Ui(r,t,function(n,i){return t(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity,e)}a.keyof=Ql;var qi=function(e){S(r,e);function r(t,n,i,o,c,u){var l=e.call(this,t,n,i,o)||this;return l.type=c,l.predicate=u,l._tag="RefinementType",l}return r}(I);a.RefinementType=qi;function Mi(e,r,t){return Xr(e,r,t)}a.brand=Mi;a.Int=Mi(a.number,function(e){return Number.isInteger(e)},"Int");var Br=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.runDefinition=c,u._tag="RecursiveType",u}return r}(I);a.RecursiveType=Br;Object.defineProperty(Br.prototype,"type",{get:function(){return this.runDefinition()},enumerable:!0,configurable:!0});function ef(e,r){var t,n=function(){return t||(t=r(i),t.name=e),t},i=new Br(e,function(o){return n().is(o)},function(o,c){return n().validate(o,c)},function(o){return n().encode(o)},n);return i}a.recursion=ef;var ji=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.type=c,u._tag="ArrayType",u}return r}(I);a.ArrayType=ji;function Fi(e,r){return r===void 0&&(r="Array<".concat(e.name,">")),new ji(r,function(t){return a.UnknownArray.is(t)&&t.every(e.is)},function(t,n){var i=a.UnknownArray.validate(t,n);if((0,U.isLeft)(i))return i;for(var o=i.right,c=o.length,u=o,l=[],f=0;f<c;f++){var g=o[f],p=e.validate(g,re(n,String(f),e,g));if((0,U.isLeft)(p))se(l,p.left);else{var v=p.right;v!==g&&(u===o&&(u=o.slice()),u[f]=v)}}return l.length>0?(0,a.failures)(l):(0,a.success)(u)},e.encode===a.identity?a.identity:function(t){return t.map(e.encode)},e)}a.array=Fi;var Di=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.props=c,u._tag="InterfaceType",u}return r}(I);a.InterfaceType=Di;function Gr(e,r){r===void 0&&(r=di(e));var t=Object.keys(e),n=t.map(function(o){return e[o]}),i=t.length;return new Di(r,function(o){if(a.UnknownRecord.is(o)){for(var c=0;c<i;c++){var u=t[c],l=o[u];if(l===void 0&&!ge.call(o,u)||!n[c].is(l))return!1}return!0}return!1},function(o,c){var u=a.UnknownRecord.validate(o,c);if((0,U.isLeft)(u))return u;for(var l=u.right,f=l,g=[],p=0;p<i;p++){var v=t[p],m=f[v],j=n[p],C=j.validate(m,re(c,v,j,m));if((0,U.isLeft)(C))se(g,C.left);else{var V=C.right;(V!==m||V===void 0&&!ge.call(f,v))&&(f===l&&(f=Ee({},l)),f[v]=V)}}return g.length>0?(0,a.failures)(g):(0,a.success)(f)},Fe(n)?a.identity:function(o){for(var c=Ee({},o),u=0;u<i;u++){var l=t[u],f=n[u].encode;f!==a.identity&&(c[l]=f(o[l]))}return c},e)}a.type=Gr;a.interface=Gr;var Vi=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.props=c,u._tag="PartialType",u}return r}(I);a.PartialType=Vi;function rf(e,r){r===void 0&&(r=vi(di(e)));var t=Object.keys(e),n=t.map(function(o){return e[o]}),i=t.length;return new Vi(r,function(o){if(a.UnknownRecord.is(o)){for(var c=0;c<i;c++){var u=t[c],l=o[u];if(l!==void 0&&!e[u].is(l))return!1}return!0}return!1},function(o,c){var u=a.UnknownRecord.validate(o,c);if((0,U.isLeft)(u))return u;for(var l=u.right,f=l,g=[],p=0;p<i;p++){var v=t[p],m=f[v],j=e[v],C=j.validate(m,re(c,v,j,m));if((0,U.isLeft)(C))m!==void 0&&se(g,C.left);else{var V=C.right;V!==m&&(f===l&&(f=Ee({},l)),f[v]=V)}}return g.length>0?(0,a.failures)(g):(0,a.success)(f)},Fe(n)?a.identity:function(o){for(var c=Ee({},o),u=0;u<i;u++){var l=t[u],f=o[l];f!==void 0&&(c[l]=n[u].encode(f))}return c},e)}a.partial=rf;var Kr=function(e){S(r,e);function r(t,n,i,o,c,u){var l=e.call(this,t,n,i,o)||this;return l.domain=c,l.codomain=u,l._tag="DictionaryType",l}return r}(I);a.DictionaryType=Kr;function Wi(e,r,t){var n=Wr(e);return n?Fl(Object.keys(n),e,r,t):Dl(e,r,t)}a.record=Wi;var kr=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.types=c,u._tag="UnionType",u}return r}(I);a.UnionType=kr;function Bi(e,r){r===void 0&&(r=yi(e));var t=wi(e);if(t!==void 0&&e.length>0){var n=t[0],i=t[1],o=i.length,c=function(u){for(var l=0;l<o;l++)if(i[l].indexOf(u)!==-1)return l};return new ir(r,function(u){if(a.UnknownRecord.is(u)){var l=c(u[n]);return l!==void 0?e[l].is(u):!1}return!1},function(u,l){var f=a.UnknownRecord.validate(u,l);if((0,U.isLeft)(f))return f;var g=f.right,p=c(g[n]);if(p===void 0)return(0,a.failure)(u,l);var v=e[p];return v.validate(g,re(l,String(p),v,g))},Fe(e)?a.identity:function(u){var l=c(u[n]);if(l===void 0)throw new Error("no codec found to encode value in union codec ".concat(r));return e[l].encode(u)},e,n)}else return new kr(r,function(u){return e.some(function(l){return l.is(u)})},function(u,l){for(var f=[],g=0;g<e.length;g++){var p=e[g],v=p.validate(u,re(l,String(g),p,u));if((0,U.isLeft)(v))se(f,v.left);else return(0,a.success)(v.right)}return(0,a.failures)(f)},Fe(e)?a.identity:function(u){for(var l=0,f=e;l<f.length;l++){var g=f[l];if(g.is(u))return g.encode(u)}throw new Error("no codec found to encode value in union type ".concat(r))},e)}a.union=Bi;var Gi=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.types=c,u._tag="IntersectionType",u}return r}(I);a.IntersectionType=Gi;function tf(e,r){r===void 0&&(r="(".concat(e.map(function(n){return n.name}).join(" & "),")"));var t=e.length;return new Gi(r,function(n){return e.every(function(i){return i.is(n)})},e.length===0?a.success:function(n,i){for(var o=[],c=[],u=0;u<t;u++){var l=e[u],f=l.validate(n,re(i,String(u),l,n));(0,U.isLeft)(f)?se(c,f.left):o.push(f.right)}return c.length>0?(0,a.failures)(c):(0,a.success)(Dr(n,o))},e.length===0?a.identity:function(n){return Dr(n,e.map(function(i){return i.encode(n)}))},e)}a.intersection=tf;var Ki=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.types=c,u._tag="TupleType",u}return r}(I);a.TupleType=Ki;function nf(e,r){r===void 0&&(r="[".concat(e.map(function(n){return n.name}).join(", "),"]"));var t=e.length;return new Ki(r,function(n){return a.UnknownArray.is(n)&&n.length===t&&e.every(function(i,o){return i.is(n[o])})},function(n,i){var o=a.UnknownArray.validate(n,i);if((0,U.isLeft)(o))return o;for(var c=o.right,u=c.length>t?c.slice(0,t):c,l=[],f=0;f<t;f++){var g=c[f],p=e[f],v=p.validate(g,re(i,String(f),p,g));if((0,U.isLeft)(v))se(l,v.left);else{var m=v.right;m!==g&&(u===c&&(u=c.slice()),u[f]=m)}}return l.length>0?(0,a.failures)(l):(0,a.success)(u)},Fe(e)?a.identity:function(n){return e.map(function(i,o){return i.encode(n[o])})},e)}a.tuple=nf;var ki=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.type=c,u._tag="ReadonlyType",u}return r}(I);a.ReadonlyType=ki;function sf(e,r){return r===void 0&&(r="Readonly<".concat(e.name,">")),new ki(r,e.is,e.validate,e.encode,e)}a.readonly=sf;var Xi=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.type=c,u._tag="ReadonlyArrayType",u}return r}(I);a.ReadonlyArrayType=Xi;function of(e,r){r===void 0&&(r="ReadonlyArray<".concat(e.name,">"));var t=Fi(e);return new Xi(r,t.is,t.validate,t.encode,e)}a.readonlyArray=of;var af=function(e,r){return zi(Gr(e),r)};a.strict=af;var Hi=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.type=c,u._tag="ExactType",u}return r}(I);a.ExactType=Hi;function zi(e,r){r===void 0&&(r=Vl(e));var t=Vr(e);return new Hi(r,e.is,function(n,i){var o=a.UnknownRecord.validate(n,i);if((0,U.isLeft)(o))return o;var c=e.validate(n,i);return(0,U.isLeft)(c)?c:(0,U.right)(gi(c.right,t))},function(n){return e.encode(gi(n,t))},e)}a.exact=zi;var Yi=function(e){S(r,e);function r(){var t=e.call(this,"Function",function(n){return typeof n=="function"},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="FunctionType",t}return r}(I);a.FunctionType=Yi;a.Function=new Yi;var Ji=function(e){S(r,e);function r(){var t=e.call(this,"never",function(n){return!1},function(n,i){return(0,a.failure)(n,i)},function(){throw new Error("cannot encode never")})||this;return t._tag="NeverType",t}return r}(I);a.NeverType=Ji;a.never=new Ji;var Zi=function(e){S(r,e);function r(){var t=e.call(this,"any",function(n){return!0},a.success,a.identity)||this;return t._tag="AnyType",t}return r}(I);a.AnyType=Zi;a.any=new Zi;function Xr(e,r,t){return t===void 0&&(t="(".concat(e.name," | ").concat(hi(r),")")),new qi(t,function(n){return e.is(n)&&r(n)},function(n,i){var o=e.validate(n,i);if((0,U.isLeft)(o))return o;var c=o.right;return r(c)?(0,a.success)(c):(0,a.failure)(c,i)},e.encode,e,r)}a.refinement=Xr;a.Integer=Xr(a.number,Number.isInteger,"Integer");var ir=function(e){S(r,e);function r(t,n,i,o,c,u){var l=e.call(this,t,n,i,o,c)||this;return l.tag=u,l}return r}(kr);a.TaggedUnionType=ir;var uf=function(e,r,t){t===void 0&&(t=yi(r));var n=Bi(r,t);return n instanceof ir?n:(console.warn("[io-ts] Cannot build a tagged union for ".concat(t,", returning a de-optimized union")),new ir(t,n.is,n.validate,n.encode,r,e))};a.taggedUnion=uf;var cf=function(e,r){return{value:e,context:r}};a.getValidationError=cf;var lf=function(e){return[{key:"",type:e}]};a.getDefaultContext=lf;a.Dictionary=a.UnknownRecord;var Qi=function(e){S(r,e);function r(){var t=e.call(this,"object",function(n){return n!==null&&typeof n=="object"},function(n,i){return t.is(n)?(0,a.success)(n):(0,a.failure)(n,i)},a.identity)||this;return t._tag="ObjectType",t}return r}(I);a.ObjectType=Qi;a.object=new Qi;a.dictionary=Wi;var ff=function(e){S(r,e);function r(t,n,i,o,c){var u=e.call(this,t,n,i,o)||this;return u.props=c,u._tag="StrictType",u}return r}(I);a.StrictType=ff;function pf(e){return e}a.clean=pf;function gf(e){return function(){return e}}a.alias=gf});self.browser||(self.browser=Zr());function ur(e){throw new Error("Unexpected value, should have never reached this point.")}var N;(function(e){e.Brave="Brave",e.Chrome="Chrome",e.Firefox="Firefox",e.Safari="Safari",e.Edge="Microsoft Edge",e.Opera="Opera",e.IE="Internet Explorer",e.AndroidBrowser="Android Browser",e.WebKit="WebKit",e.Other="Browser"})(N||(N={}));var q;(function(e){e.macOS="MacOSX",e.iOS="iOS",e.Windows="Windows",e.Android="Android",e.Linux="Linux",e.ChromeOS="ChromeOS",e.Other=""})(q||(q={}));var xf=new Map([["Brave",N.Brave],["Google Chrome",N.Chrome],["Microsoft Edge",N.Edge],["Opera",N.Opera],["Chromium",N.Chrome]]),Of=new Map([["Android",q.Android],["Chrome OS",q.ChromeOS],["Fuchsia",q.Other],["iOS",q.iOS],["Linux",q.Linux],["macOS",q.macOS],["Windows",q.Windows],["Unknown",q.Other]]);var bf=[{term:"msie",name:N.IE,versionRegex:/msie ([\d.]+)/},{term:"trident",name:N.IE,versionRegex:/rv:([\d.]+)/},{term:"edge",name:N.Edge,versionRegex:/edge\/([\d.]+)/},{term:"edg",name:N.Edge,versionRegex:/edg\/([\d.]+)/},{term:"opr",name:N.Opera,versionRegex:/opr\/([\d.]+)/},{term:"opera mobi",name:N.Opera,versionRegex:/version\/([\d.]+)/},{term:"opios",name:N.Opera,versionRegex:/opios\/([\d.]+)/},{term:"firefox",name:N.Firefox,versionRegex:/firefox\/([\d.]+)/},{term:"fxios",name:N.Firefox,versionRegex:/fxios\/([\d.]+)/},{term:"chrome",name:N.Chrome,versionRegex:/chrome\/([\d.]+)/},{term:"crios",name:N.Chrome,versionRegex:/crios\/([\d.]+)/},{term:"android",name:N.AndroidBrowser},{term:"safari",name:N.Safari,versionRegex:/version\/([\d.]+)/},{term:"applewebkit",name:N.WebKit,versionRegex:/applewebkit\/([\d.]+)/}],Sf=[{term:"iphone",name:q.iOS,versionRegex:/os ([\d._]+)/},{term:"ipad",name:q.iOS,versionRegex:/os ([\d._]+)/},{term:"ipod",name:q.iOS,versionRegex:/os ([\d._]+)/},{term:"mac os x",name:q.macOS,versionRegex:/os x ([\d._]+)/},{term:"android;",name:q.Android,versionRegex:/android; ([\d.]+)/},{term:"android",name:q.Android,versionRegex:/android ([\d.]+)/},{term:"linux",name:q.Linux},{term:"cros",name:q.ChromeOS},{term:"windows",name:q.Windows,versionRegex:/windows nt ([\d.]+)/}];var Ze=Yr(Wn());var Tp=[{name:N.Brave,minVersion:"109.0.0",displayVersion:"1.47.0"},{name:N.Chrome,minVersion:"109.0.0",displayVersion:"109"},{name:N.Edge,minVersion:"109.0.0",displayVersion:"109"},{name:N.Firefox,minVersion:"109.0.0",displayVersion:"109"},{name:N.Opera,minVersion:"95.0.0",displayVersion:"95"},{name:N.Safari,minVersion:"16.0.0",displayVersion:"16"}];var Te=Yr(es()),rs,mf=Symbol(),oe=class{constructor(r){this[rs]=0,this.toString=()=>this.str,this.toJSON=()=>this.str,this.loggableString=()=>this,this.replace=(t,n)=>me(this.str.replace(t,n.str)),this.concat=t=>me(this.str+t.str),this.str=r}};rs=mf;oe.assertLogSafe=e=>new oe(e);var hf=(e,r)=>me(e.map(t=>t.str).join(r?.str)),me=oe.assertLogSafe,Re=(e,...r)=>hf(e.flatMap((t,n)=>{let i=r[n];return i!==void 0?[me(t),i]:[me(t)]}),me(""));var df=new Te.Type("LoggableString",e=>e instanceof oe,(e,r)=>e instanceof oe?Te.success(e):Te.failure(e,r,"cannot decode unknown value to LoggableString"),e=>e.str);var ts;(function(e){e.ContextError=Re`ContextError`,e.RequestError=Re`RequestError`,e.ValidationError=Re`ValidationError`})(ts||(ts={}));var ns=["features","config","all-vaults","opxAutomaticallyDisabled","accounts","devtools","notification-history","watchtower_settings","sls_app_connected","devtools-add-account-prompt","privacy-force-sandbox","privacy-key-sandbox-api-key","diagnostics","whatsnew-features"];var is=()=>{let e=new Map;return ns.forEach(r=>{let t=window.localStorage.getItem(r);t&&e.set(r,t)}),e};browser.runtime.onMessage.addListener(async e=>{let r=e.name;if(r==="GetStorageEntries")return Promise.resolve({type:"Success",data:[...is()]});ur(r)});

//# debugId=5d836992-3d22-56f1-99d1-0cd9d3c2ebc6
