
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="5724b4ce-09db-58a1-bcb2-8d89a623662e")}catch(e){}}();
import{a as t,b as C}from"/chunks/chunk-EXMDW5FT.js";import{c as e}from"/chunks/chunk-54UHBIHR.js";var o=e(t()),l=e(C()),d=i=>(0,l.jsxs)("svg",{width:32,height:32,viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...i,children:[(0,l.jsx)("path",{d:"M0 16C0 7.16344 7.16344 0 16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16Z",fill:"black",fillOpacity:.01}),(0,l.jsx)("rect",{width:32,height:32,fill:"black",fillOpacity:.01}),(0,l.jsxs)("g",{clipPath:"url(#clip0_4872_577053)",children:[(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 0C24.8363 0 32 7.16373 32 16C32 24.8363 24.8363 32 16 32C7.16373 32 0 24.8363 0 16C0 7.16373 7.16373 0 16 0Z",fill:"#0A2D4D",fillOpacity:.2}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 1C24.2843 1 31 7.71571 31 16C31 24.2843 24.2843 31 16 31C7.71571 31 1 24.2843 1 16C1 7.71571 7.71571 1 16 1Z",fill:"white"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 4C22.6271 4 28 9.37287 28 16C28 22.6271 22.6271 28 16 28C9.37287 28 4 22.6271 4 16C4 9.37287 9.37287 4 16 4Z",fill:"url(#paint0_linear_4872_577053)"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 7C18.3869 7 20.6761 7.94821 22.364 9.63604C24.0518 11.3239 25 13.6131 25 16C25 18.3869 24.0518 20.6761 22.364 22.364C20.6761 24.0518 18.3869 25 16 25C13.6131 25 11.3239 24.0518 9.63604 22.364C7.94821 20.6761 7 18.3869 7 16C7 13.6131 7.94821 11.3239 9.63604 9.63604C11.3239 7.94821 13.6131 7 16 7Z",fill:"white"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 11C17.3261 11 18.5979 11.5268 19.5355 12.4645C20.4732 13.4021 21 14.6739 21 16C21 17.3261 20.4732 18.5979 19.5355 19.5355C18.5979 20.4732 17.3261 21 16 21C14.6739 21 13.4021 20.4732 12.4645 19.5355C11.5268 18.5979 11 17.3261 11 16C11 14.6739 11.5268 13.4021 12.4645 12.4645C13.4021 11.5268 14.6739 11 16 11Z",fill:"#DCE4FA"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 10.9815C14 10.288 14 9.94123 14.125 9.67631C14.2352 9.44303 14.4113 9.25339 14.6279 9.13462C14.8738 9 15.1958 9 15.8396 9H16.1596C16.8035 9 17.1254 9 17.3714 9.13462C17.5873 9.25308 17.7633 9.44262 17.8743 9.67631C17.9992 9.94123 17.9992 10.288 17.9992 10.9815V16.3446C17.9992 16.4168 17.9992 16.4523 17.9922 16.4857C17.9854 16.5154 17.9743 16.5437 17.9592 16.5697C17.9422 16.5998 17.9192 16.6246 17.8723 16.6752L17.3054 17.2858C17.1984 17.4011 17.1454 17.4582 17.1254 17.5249C17.1077 17.5837 17.1077 17.647 17.1254 17.7058C17.1454 17.7726 17.1984 17.8297 17.3054 17.9449L17.8723 18.5555C17.9049 18.5871 17.934 18.6225 17.9592 18.6611C17.9739 18.6871 17.9847 18.7154 17.9912 18.7451C17.9988 18.7913 18.0015 18.8382 17.9992 18.8851V21.0185C17.9992 21.712 17.9992 22.0588 17.8743 22.3237C17.764 22.557 17.5879 22.7466 17.3714 22.8654C17.1254 23 16.8035 23 16.1596 23H15.8396C15.1958 23 14.8738 23 14.6279 22.8654C14.4113 22.7466 14.2352 22.557 14.125 22.3237C14 22.0588 14 21.712 14 21.0185V15.6554C14 15.5832 14 15.5477 14.007 15.5143C14.0138 15.4846 14.025 15.4563 14.04 15.4303C14.057 15.4002 14.08 15.3754 14.127 15.3248L14.6939 14.7142C14.8008 14.5989 14.8538 14.5418 14.8738 14.4751C14.8916 14.4163 14.8916 14.353 14.8738 14.2942C14.8538 14.2274 14.8008 14.1703 14.6939 14.0551L14.127 13.4445C14.08 13.3938 14.057 13.3691 14.04 13.3389C14.0253 13.3129 14.0145 13.2846 14.008 13.2549C14 13.2215 14 13.186 14 13.1149V10.9815Z",fill:"url(#paint1_linear_4872_577053)"})]}),(0,l.jsxs)("defs",{children:[(0,l.jsxs)("linearGradient",{id:"paint0_linear_4872_577053",x1:16,y1:4,x2:16,y2:28,gradientUnits:"userSpaceOnUse",children:[(0,l.jsx)("stop",{stopColor:"#1D48F5"}),(0,l.jsx)("stop",{offset:1,stopColor:"#499FF5"})]}),(0,l.jsxs)("linearGradient",{id:"paint1_linear_4872_577053",x1:15.9996,y1:9,x2:15.9996,y2:23,gradientUnits:"userSpaceOnUse",children:[(0,l.jsx)("stop",{stopColor:"#151729"}),(0,l.jsx)("stop",{offset:1,stopColor:"#1B1C33"})]}),(0,l.jsx)("clipPath",{id:"clip0_4872_577053",children:(0,l.jsx)("rect",{width:32,height:32,fill:"white"})})]})]}),p=d;export{p as default};

//# debugId=5724b4ce-09db-58a1-bcb2-8d89a623662e
