
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="5b9860d0-da04-5eb1-8df9-74b6fa75b73a")}catch(e){}}();
import{a as o,b as i}from"/chunks/chunk-EXMDW5FT.js";import{c as t}from"/chunks/chunk-54UHBIHR.js";var h=t(o()),e=t(i()),s=r=>(0,e.jsxs)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...r,children:[(0,e.jsx)("rect",{width:24,height:24,rx:4,fill:"#BBADFF"}),(0,e.jsx)("mask",{id:"mask0_3067_250049",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:24,height:24,children:(0,e.jsx)("rect",{width:24,height:24,rx:4.125,fill:"#C2EAFF"})}),(0,e.jsxs)("g",{mask:"url(#mask0_3067_250049)",children:[(0,e.jsx)("rect",{x:4.52264,y:14.5605,width:14.9547,height:12.3436,rx:3,fill:"white",stroke:"#6851D6",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.jsx)("path",{d:"M16.7401 14.4883V10.3046C16.7401 7.68669 14.6179 5.56445 12 5.56445V5.56445C9.38206 5.56445 7.25983 7.68669 7.25983 10.3046V14.4883",stroke:"#6851D6",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.8771 21.332C13.3324 21.0419 13.6343 20.5326 13.6343 19.9527C13.6343 19.0501 12.9026 18.3184 12 18.3184C11.0974 18.3184 10.3656 19.0501 10.3656 19.9527C10.3656 20.5326 10.6676 21.0419 11.1229 21.332V23.9499H12.8771V21.332Z",fill:"#6851D6"})]}),(0,e.jsx)("rect",{x:.5,y:.5,width:23,height:23,rx:3.5,stroke:"#6851D6"})]}),l=s;export{l as default};

//# debugId=5b9860d0-da04-5eb1-8df9-74b6fa75b73a
