
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="5899fbaa-6b87-588f-905a-65726657ec19")}catch(e){}}();
import{a as s,b as t}from"/chunks/chunk-EXMDW5FT.js";import{c as i}from"/chunks/chunk-54UHBIHR.js";var l=i(s()),e=i(t()),o=r=>(0,e.jsxs)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...r,children:[(0,e.jsx)("rect",{width:24,height:24,rx:4,fill:"#BBADFF"}),(0,e.jsx)("path",{d:"M12 3.9668L20.0339 12.0007L12 20.0346L3.96606 12.0007L12 3.9668Z",stroke:"#6851D6",strokeWidth:1.11625,strokeLinejoin:"round"}),(0,e.jsx)("mask",{id:"mask0_3065_246337",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:24,height:24,children:(0,e.jsx)("rect",{width:24,height:24,rx:4.125,fill:"#C2EAFF"})}),(0,e.jsx)("g",{mask:"url(#mask0_3065_246337)",children:(0,e.jsx)("rect",{x:.5,y:.5,width:23,height:23,rx:3.5,stroke:"#6851D6"})}),(0,e.jsx)("ellipse",{cx:11.9999,cy:5.43336,rx:2.74976,ry:2.74976,fill:"white",stroke:"#6851D6",strokeLinejoin:"round"}),(0,e.jsx)("ellipse",{cx:12,cy:18.6309,rx:2.66993,ry:2.66993,fill:"white",stroke:"#6851D6",strokeLinejoin:"round"}),(0,e.jsx)("ellipse",{cx:5.43556,cy:12,rx:2.66993,ry:2.66993,fill:"white",stroke:"#6851D6",strokeLinejoin:"round"}),(0,e.jsx)("ellipse",{cx:18.7236,cy:12,rx:2.66993,ry:2.66993,fill:"white",stroke:"#6851D6",strokeLinejoin:"round"})]}),h=o;export{h as default};

//# debugId=5899fbaa-6b87-588f-905a-65726657ec19
