
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="d767cf33-8455-564f-8665-405ee072152b")}catch(e){}}();
import{a as h,b as e}from"/chunks/chunk-EXMDW5FT.js";import{c as i}from"/chunks/chunk-54UHBIHR.js";var o=i(h()),l=i(e()),d=t=>(0,l.jsxs)("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:[(0,l.jsxs)("g",{clipPath:"url(#clip0_575_2386)",children:[(0,l.jsx)("path",{d:"M9.5 11L11 9.5L14.6196 12.3957C15.356 12.9848 15.4168 14.0832 14.75 14.75V14.75C14.0832 15.4168 12.9848 15.356 12.3957 14.6196L9.5 11Z",fill:"#B47B46"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13 7C13 10.3137 10.3137 13 7 13C3.68629 13 1 10.3137 1 7C1 3.68629 3.68629 1 7 1C10.3137 1 13 3.68629 13 7ZM11 7C11 9.20914 9.20914 11 7 11C4.79086 11 3 9.20914 3 7C3 4.79086 4.79086 3 7 3C9.20914 3 11 4.79086 11 7Z",fill:"#B9BDC2"})]}),(0,l.jsx)("defs",{children:(0,l.jsx)("clipPath",{id:"clip0_575_2386",children:(0,l.jsx)("rect",{width:16,height:16,fill:"white"})})})]}),C=d;export{C as default};

//# debugId=d767cf33-8455-564f-8665-405ee072152b
