
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="31b3ce19-be37-56a9-a2fa-f68eb470df4a")}catch(e){}}();
import{a as o,b as i}from"/chunks/chunk-EXMDW5FT.js";import{c as e}from"/chunks/chunk-54UHBIHR.js";var h=e(o()),t=e(i()),s=r=>(0,t.jsxs)("svg",{width:32,height:32,viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...r,children:[(0,t.jsx)("rect",{x:8.60449,y:8.56445,width:15.1975,height:15.1975,rx:1,fill:"white"}),(0,t.jsx)("rect",{x:6.13867,y:6.13867,width:19.7227,height:19.7227,fill:"#ABFCCB"}),(0,t.jsx)("rect",{x:1,y:1,width:30,height:30,rx:5,fill:"#81CA72"}),(0,t.jsx)("mask",{id:"mask0_18952:191300",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:32,height:32,children:(0,t.jsx)("rect",{width:32,height:32,rx:6,fill:"#C4C4C4"})}),(0,t.jsxs)("g",{mask:"url(#mask0_18952:191300)",children:[(0,t.jsx)("path",{d:"M6.24146 12.2211V6.24902H12.1203",stroke:"#157901",strokeWidth:1.5,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M6.18188 19.7789V25.751H12.0607",stroke:"#157901",strokeWidth:1.5,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M25.8181 12.2211V6.24902H19.9393",stroke:"#157901",strokeWidth:1.5,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M25.7585 19.7789V25.751H19.8797",stroke:"#157901",strokeWidth:1.5,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("rect",{x:10.75,y:10.75,width:10.4999,height:10.4999,rx:1.25,fill:"white",stroke:"#157901",strokeWidth:1.5})]}),(0,t.jsx)("rect",{x:.75,y:.75,width:30.5,height:30.5,rx:5.25,stroke:"#157901",strokeWidth:1.5})]}),k=s;export{k as default};

//# debugId=31b3ce19-be37-56a9-a2fa-f68eb470df4a
