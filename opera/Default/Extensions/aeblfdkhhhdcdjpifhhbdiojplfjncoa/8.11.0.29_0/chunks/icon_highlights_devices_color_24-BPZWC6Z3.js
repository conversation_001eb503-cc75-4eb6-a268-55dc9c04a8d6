
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="f8d2c994-927c-51f3-9608-3b2339eb0801")}catch(e){}}();
import{a as o,b as i}from"/chunks/chunk-EXMDW5FT.js";import{c as e}from"/chunks/chunk-54UHBIHR.js";var h=e(o()),t=e(i()),s=r=>(0,t.jsxs)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...r,children:[(0,t.jsx)("rect",{width:24,height:24,rx:4,fill:"#81CA72"}),(0,t.jsx)("mask",{id:"mask0_3065_246444",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:24,height:24,children:(0,t.jsx)("rect",{width:24,height:24,rx:4.125,fill:"#C2EAFF"})}),(0,t.jsxs)("g",{mask:"url(#mask0_3065_246444)",children:[(0,t.jsx)("rect",{x:11.4649,y:4.91602,width:15.4111,height:22.5898,rx:1,fill:"#81CA72",stroke:"#157901",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("rect",{x:7.04257,y:10.332,width:13.1086,height:17.7595,rx:1,transform:"rotate(-0.271122 7.04257 10.332)",fill:"#C0E4B8",stroke:"#157901",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("ellipse",{cx:13.6422,cy:17.9918,rx:1.64219,ry:1.64219,fill:"white"}),(0,t.jsx)("rect",{x:4.93274,y:15.6855,width:7.63898,height:13.598,rx:1,fill:"white",stroke:"#157901",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,t.jsx)("rect",{x:.5,y:.5,width:23,height:23,rx:3.5,stroke:"#157901"})]}),l=s;export{l as default};

//# debugId=f8d2c994-927c-51f3-9608-3b2339eb0801
