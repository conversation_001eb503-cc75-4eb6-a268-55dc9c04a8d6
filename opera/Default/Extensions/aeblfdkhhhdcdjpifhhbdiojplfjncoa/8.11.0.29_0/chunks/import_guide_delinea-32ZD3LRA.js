
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="280c99c5-b7f3-5534-b678-6cc0006dff71")}catch(e){}}();
import{a as e,b as h}from"/chunks/chunk-EXMDW5FT.js";import{c as t}from"/chunks/chunk-54UHBIHR.js";var g=t(e()),i=t(h()),d=l=>(0,i.jsxs)("svg",{width:32,height:32,viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...l,children:[(0,i.jsxs)("g",{clipPath:"url(#clip0_4872_577102)",children:[(0,i.jsx)("rect",{width:32,height:32,rx:6,fill:"white"}),(0,i.jsx)("rect",{x:7,y:6,width:3,height:6,fill:"#231556"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M22 3H10V6H22V12H25V6V3H22Z",fill:"#231556"}),(0,i.jsx)("rect",{x:5,y:13.5,width:15,height:3,fill:"#231556"}),(0,i.jsx)("rect",{x:21.5,y:13.5,width:5.5,height:3,fill:"#00FF5A"}),(0,i.jsx)("rect",{x:5,y:19.5,width:15,height:3,fill:"#231556"}),(0,i.jsx)("rect",{x:21.5,y:19.5,width:5.5,height:3,fill:"#00FF5A"}),(0,i.jsx)("rect",{x:5,y:25.5,width:15,height:3,fill:"#231556"}),(0,i.jsx)("rect",{x:21.5,y:25.5,width:5.5,height:3,fill:"#00FF5A"})]}),(0,i.jsx)("defs",{children:(0,i.jsx)("clipPath",{id:"clip0_4872_577102",children:(0,i.jsx)("rect",{width:32,height:32,rx:6,fill:"white"})})})]}),w=d;export{w as default};

//# debugId=280c99c5-b7f3-5534-b678-6cc0006dff71
