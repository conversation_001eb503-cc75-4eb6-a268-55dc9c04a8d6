
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="35346537-8f85-55e7-b745-0db4978bf154")}catch(e){}}();
import{a as o,b as i}from"/chunks/chunk-EXMDW5FT.js";import{c as e}from"/chunks/chunk-54UHBIHR.js";var h=e(o()),t=e(i()),s=r=>(0,t.jsxs)("svg",{width:32,height:32,viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...r,children:[(0,t.jsxs)("g",{clipPath:"url(#clip0_7712_50967)",children:[(0,t.jsx)("rect",{width:32,height:32,rx:6,fill:"black",fillOpacity:.01}),(0,t.jsx)("mask",{id:"mask0_7712_50967",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:32,height:32,children:(0,t.jsx)("rect",{width:32,height:32,rx:6,fill:"#B4B6BC"})}),(0,t.jsxs)("g",{mask:"url(#mask0_7712_50967)",children:[(0,t.jsx)("rect",{x:1,y:1,width:30,height:30,rx:5,fill:"#98D8DE"}),(0,t.jsx)("path",{d:"M15.4624 6.76352H36.6813V36.7886H6.73218V15.7772L15.4624 6.76352Z",fill:"#FFF3F6",stroke:"#1C82A0",strokeWidth:1.5,strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M15.6052 15.5573H6.92871L15.6052 6.75374L15.6052 15.5573Z",fill:"#D6EEEB",stroke:"#1C82A0",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M12.5396 19.9769L31.9999 19.9769",stroke:"#66C6D3",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M12.5396 23.6241L31.9999 23.6241",stroke:"#66C6D3",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M12.5396 27.2713L31.9999 27.2713",stroke:"#66C6D3",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("rect",{x:.75,y:.75,width:30.5,height:30.5,rx:5.25,stroke:"#1C82A0",strokeWidth:1.5})]})]}),(0,t.jsx)("defs",{children:(0,t.jsx)("clipPath",{id:"clip0_7712_50967",children:(0,t.jsx)("rect",{width:32,height:32,rx:6,fill:"white"})})})]}),d=s;export{d as default};

//# debugId=35346537-8f85-55e7-b745-0db4978bf154
