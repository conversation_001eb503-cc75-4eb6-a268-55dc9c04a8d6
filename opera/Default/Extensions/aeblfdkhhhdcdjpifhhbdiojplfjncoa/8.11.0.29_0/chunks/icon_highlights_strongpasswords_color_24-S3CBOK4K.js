
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="59a2595b-de95-5c75-9807-49425acbc17d")}catch(e){}}();
import{a as o,b as e}from"/chunks/chunk-EXMDW5FT.js";import{c as r}from"/chunks/chunk-54UHBIHR.js";var n=r(o()),t=r(e()),s=i=>(0,t.jsxs)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...i,children:[(0,t.jsx)("rect",{width:24,height:24,rx:4,fill:"#BBADFF"}),(0,t.jsx)("mask",{id:"mask0_3065_246269",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:24,height:24,children:(0,t.jsx)("rect",{width:24,height:24,rx:4.125,fill:"#C2EAFF"})}),(0,t.jsxs)("g",{mask:"url(#mask0_3065_246269)",children:[(0,t.jsx)("circle",{r:13.6422,transform:"matrix(1 0 0.00878081 0.999961 12 21.7398)",fill:"white",stroke:"#6851D6",strokeWidth:.997105,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("circle",{r:9.94943,transform:"matrix(0.722531 -0.691338 0.697656 0.716433 11.8758 21.8864)",fill:"#BBADFF"}),(0,t.jsx)("path",{d:"M12 8.30859V10.4841",stroke:"#6851D6",strokeWidth:.997105,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M-1.28558 21.6914L0.547828 21.6914",stroke:"#6851D6",strokeWidth:.997105,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M23.2304 21.6914L25.0638 21.6914",stroke:"#6851D6",strokeWidth:.997105,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M20.4507 14.1777L21.989 12.6394",stroke:"#6851D6",strokeWidth:.997105,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M3.46667 14.1777L1.92836 12.6394",stroke:"#6851D6",strokeWidth:.997105,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M8.87281 25.8256L18.1027 16.5957",stroke:"#6851D6",strokeWidth:.997105,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("ellipse",{cx:12.0122,cy:22.9403,rx:2.17853,ry:2.17853,transform:"rotate(45 12.0122 22.9403)",fill:"#F3FFF6",stroke:"#6851D6",strokeWidth:.997105,strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,t.jsx)("rect",{x:.5,y:.5,width:23,height:23,rx:3.5,stroke:"#6851D6"})]}),k=s;export{k as default};

//# debugId=59a2595b-de95-5c75-9807-49425acbc17d
