
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="0ca1d16e-9140-59f4-8729-dd73f52a69bb")}catch(e){}}();
import{a as n,b as t}from"/chunks/chunk-EXMDW5FT.js";import{c as e}from"/chunks/chunk-54UHBIHR.js";var a=e(n()),C=e(t()),o=i=>(0,C.jsxs)("svg",{width:32,height:33,viewBox:"0 0 32 33",fill:"none",xmlns:"http://www.w3.org/2000/svg",...i,children:[(0,C.jsx)("rect",{width:32,height:32,transform:"translate(0 0.28125)",fill:"url(#paint0_linear_5466_353500)"}),(0,C.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.2945 22.548C13.5582 23.2335 13.8754 23.839 14.2462 24.316C11.2638 23.5949 8.91629 21.2472 8.19554 18.2646C8.67257 18.6355 9.27831 18.9529 9.96408 19.2166C10.7264 19.5098 11.616 19.7476 12.5937 19.9172C12.7634 20.8954 13.0012 21.7854 13.2945 22.548ZM13.8994 20.0972C14.0482 20.8448 14.2391 21.5196 14.4619 22.099C14.7409 22.8242 15.0577 23.3682 15.3776 23.72C15.6974 24.0716 15.9709 24.1817 16.1844 24.1817C16.3979 24.1817 16.6714 24.0716 16.9912 23.72C17.3111 23.3682 17.6279 22.8242 17.9068 22.099C18.1297 21.5196 18.3206 20.8449 18.4694 20.0973C17.7381 20.1734 16.9721 20.2135 16.1846 20.2135C15.3969 20.2135 14.6307 20.1733 13.8994 20.0972ZM18.6733 18.8156C17.8922 18.9108 17.0562 18.9628 16.1846 18.9628C15.3128 18.9628 14.4767 18.9108 13.6955 18.8155C13.6003 18.0347 13.5484 17.1988 13.5484 16.3275C13.5484 15.4555 13.6004 14.6192 13.6957 13.838C14.4768 13.7428 15.3129 13.6908 16.1846 13.6908C17.0561 13.6908 17.8921 13.7427 18.6731 13.8379C18.7684 14.6192 18.8204 15.4555 18.8204 16.3275C18.8204 17.1989 18.7684 18.0347 18.6733 18.8156ZM19.775 19.9173C19.6054 20.8954 19.3676 21.7854 19.0742 22.548C18.8106 23.2335 18.4933 23.8391 18.1225 24.3161C21.1051 23.595 23.4528 21.2472 24.1736 18.2646C23.6966 18.6355 23.0909 18.9529 22.4051 19.2166C21.6427 19.5099 20.7529 19.7476 19.775 19.9173ZM24.1734 14.3888C23.4523 11.4064 21.1046 9.05889 18.122 8.33813C18.493 8.8152 18.8104 9.42104 19.0742 10.1069C19.3674 10.8692 19.6051 11.7586 19.7748 12.7362C20.7528 12.9058 21.6426 13.1436 22.4051 13.4369C23.0908 13.7006 23.6964 14.0179 24.1734 14.3888ZM19.9548 14.0418C20.7022 14.1906 21.3768 14.3815 21.9561 14.6043C22.6813 14.8832 23.2253 15.2001 23.5771 15.52C23.9287 15.8397 24.0388 16.1133 24.0388 16.3268C24.0388 16.5402 23.9287 16.8138 23.5771 17.1335C23.2253 17.4535 22.6813 17.7703 21.9561 18.0492C21.3769 18.272 20.7023 18.4629 19.955 18.6116C20.031 17.8806 20.0712 17.1147 20.0712 16.3275C20.0712 15.5397 20.031 14.7733 19.9548 14.0418ZM18.4691 12.5562C18.3203 11.8092 18.1295 11.135 17.9068 10.556C17.6279 9.83073 17.3111 9.28676 16.9912 8.93494C16.6714 8.5833 16.3979 8.47321 16.1844 8.47321C15.9709 8.47321 15.6974 8.5833 15.3776 8.93494C15.0577 9.28676 14.7409 9.83073 14.4619 10.556C14.2392 11.135 14.0484 11.8092 13.8997 12.5563C14.6309 12.4802 15.397 12.44 16.1846 12.44C16.972 12.44 17.7379 12.4802 18.4691 12.5562ZM12.594 12.7362C12.7636 11.7587 13.0014 10.8692 13.2945 10.1069C13.5583 9.42107 13.8758 8.81524 14.2467 8.33818C11.2644 9.059 8.91677 11.4064 8.19574 14.3888C8.67274 14.018 9.27841 13.7006 9.96408 13.4369C10.7265 13.1437 11.6162 12.9059 12.594 12.7362ZM12.414 14.0419C12.3378 14.7734 12.2976 15.5397 12.2976 16.3275C12.2976 17.1147 12.3378 17.8805 12.4138 18.6116C11.6666 18.4628 10.9922 18.272 10.4131 18.0492C9.68786 17.7703 9.14389 17.4535 8.79207 17.1335C8.44043 16.8138 8.33034 16.5402 8.33034 16.3268C8.33034 16.1133 8.44043 15.8397 8.79207 15.52C9.14389 15.2001 9.68786 14.8832 10.4131 14.6043C10.9922 14.3816 11.6667 14.1907 12.414 14.0419ZM16.1844 26.1114C21.5881 26.1114 25.9688 21.7307 25.9688 16.3271C25.9688 10.9234 21.5881 6.54297 16.1844 6.54297C10.7809 6.54297 6.40039 10.9234 6.40039 16.3271C6.40039 21.7307 10.7809 26.1114 16.1844 26.1114Z",fill:"url(#paint1_linear_5466_353500)"}),(0,C.jsxs)("defs",{children:[(0,C.jsxs)("linearGradient",{id:"paint0_linear_5466_353500",x1:-12.4639,y1:55.8552,x2:25.0638,y2:-7.6899,gradientUnits:"userSpaceOnUse",children:[(0,C.jsx)("stop",{stopColor:"#FFAA05"}),(0,C.jsx)("stop",{offset:1,stopColor:"#FFD480"})]}),(0,C.jsxs)("linearGradient",{id:"paint1_linear_5466_353500",x1:28.4759,y1:2.24381,x2:12.0678,y2:34.0299,gradientUnits:"userSpaceOnUse",children:[(0,C.jsx)("stop",{offset:.03125,stopColor:"#EB7100"}),(0,C.jsx)("stop",{offset:.927083,stopColor:"#CC4E00"})]})]})]}),l=o;export{l as default};

//# debugId=0ca1d16e-9140-59f4-8729-dd73f52a69bb
