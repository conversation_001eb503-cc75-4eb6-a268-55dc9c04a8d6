
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="85e593f8-d082-5f87-911d-caf6cbf65116")}catch(e){}}();
import{a as l,b as i}from"/chunks/chunk-EXMDW5FT.js";import{c as o}from"/chunks/chunk-54UHBIHR.js";var s=o(l()),t=o(i()),r=e=>(0,t.jsxs)("svg",{width:32,height:32,viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,t.jsx)("rect",{width:32,height:32}),(0,t.jsxs)("g",{clipPath:"url(#clip0_5471_8894)",children:[(0,t.jsx)("path",{d:"M15.9993 1.00213C7.71545 1.00213 1 7.71758 1 16.0014C1 24.0456 7.33287 30.6107 15.2849 30.9833C15.5223 30.9944 15.7599 31.0008 15.9995 31.0008C19.8398 31.0008 23.342 29.5568 25.9957 27.1837C24.2375 28.3497 22.1819 29.0206 19.9851 29.0206C16.4131 29.0206 13.2141 27.2486 11.0625 24.4542C9.40396 22.4964 8.32974 19.6024 8.25656 16.3545V15.6481C8.32969 12.4004 9.40391 9.50576 11.0621 7.54843C13.214 4.75404 16.4129 2.98203 19.9846 2.98203C22.182 2.98203 24.238 3.65294 25.9963 4.81971C23.3556 2.45743 19.8746 1.01639 16.0557 1.00221C16.0367 1.00221 16.018 1.00147 15.999 1.00147L15.9993 1.00213Z",fill:"url(#paint0_linear_5471_8894)"}),(0,t.jsx)("path",{d:"M11.0623 7.54931C12.4387 5.92434 14.2164 4.94446 16.1585 4.94446C20.5253 4.94446 24.0645 9.89519 24.0645 16.0021C24.0645 22.109 20.525 27.0598 16.1585 27.0598C14.2164 27.0598 12.4387 26.0803 11.0626 24.4553C13.2141 27.2497 16.4129 29.0217 19.9852 29.0217C22.1821 29.0217 24.2377 28.3508 25.9958 27.1848C29.0659 24.4381 30.9991 20.446 30.9991 16.0022C30.9991 11.5589 29.0663 7.56703 25.9969 4.82074C24.2383 3.65396 22.1823 2.98306 19.9852 2.98306C16.4132 2.98306 13.2142 4.75508 11.0627 7.54947",fill:"url(#paint1_linear_5471_8894)"})]}),(0,t.jsxs)("defs",{children:[(0,t.jsxs)("linearGradient",{id:"paint0_linear_5471_8894",x1:13.4985,y1:1.49107,x2:13.4985,y2:30.5647,gradientUnits:"userSpaceOnUse",children:[(0,t.jsx)("stop",{stopColor:"#FF1B2D"}),(0,t.jsx)("stop",{offset:.3,stopColor:"#FF1B2D"}),(0,t.jsx)("stop",{offset:.614,stopColor:"#FF1B2D"}),(0,t.jsx)("stop",{offset:1,stopColor:"#A70014"})]}),(0,t.jsxs)("linearGradient",{id:"paint1_linear_5471_8894",x1:21.0304,y1:3.20498,x2:21.0304,y2:28.919,gradientUnits:"userSpaceOnUse",children:[(0,t.jsx)("stop",{stopColor:"#9C0000"}),(0,t.jsx)("stop",{offset:.7,stopColor:"#FF4B4B"}),(0,t.jsx)("stop",{offset:1,stopColor:"#FF4B4B"})]}),(0,t.jsx)("clipPath",{id:"clip0_5471_8894",children:(0,t.jsx)("rect",{width:30,height:30,fill:"white",transform:"translate(1 1)"})})]})]}),a=r;export{a as default};

//# debugId=85e593f8-d082-5f87-911d-caf6cbf65116
