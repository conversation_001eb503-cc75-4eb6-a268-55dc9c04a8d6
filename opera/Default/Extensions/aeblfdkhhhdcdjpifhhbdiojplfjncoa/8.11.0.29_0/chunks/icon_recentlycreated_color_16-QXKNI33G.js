
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="9ddb44c1-ea2b-599f-aa45-570408f1a432")}catch(e){}}();
import{a as t,b as o}from"/chunks/chunk-EXMDW5FT.js";import{c as e}from"/chunks/chunk-54UHBIHR.js";var i=e(t()),l=e(o()),d=C=>(0,l.jsxs)("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...C,children:[(0,l.jsx)("path",{d:"M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8Z",fill:"#E6E6E8"}),(0,l.jsx)("path",{d:"M8 8V1L11 1.5L14 5L15 9L13 13L8 8Z",fill:"#FFD480"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14ZM8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16Z",fill:"#EB7100"}),(0,l.jsx)("path",{d:"M8 4V8L10 10",stroke:"#CC4E00",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9795 8.5C13.9931 8.33513 14 8.16838 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 8.16838 2.00694 8.33513 2.02054 8.5C2.27461 5.42024 4.85467 3 8 3C11.1453 3 13.7254 5.42024 13.9795 8.5Z",fill:"black",fillOpacity:.1})]}),n=d;export{n as default};

//# debugId=9ddb44c1-ea2b-599f-aa45-570408f1a432
