
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="178dc40e-bfed-5873-b7e7-69d91f35af5b")}catch(e){}}();
import{a as o,b as i}from"/chunks/chunk-EXMDW5FT.js";import{c as e}from"/chunks/chunk-54UHBIHR.js";var n=e(o()),t=e(i()),s=r=>(0,t.jsxs)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...r,children:[(0,t.jsx)("rect",{width:24,height:24,rx:4,fill:"#81CA72"}),(0,t.jsx)("rect",{x:.5,y:.5,width:23,height:23,rx:3.5,stroke:"#157901"}),(0,t.jsx)("rect",{x:3.86005,y:5.01367,width:6.49685,height:6.49685,rx:1,fill:"#C0E4B8",stroke:"#157901",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("rect",{x:13.1698,y:8.16992,width:5.26347,height:5.26347,rx:1,transform:"rotate(-45 13.1698 8.16992)",fill:"white",stroke:"#157901",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("ellipse",{cx:7.10847,cy:16.889,rx:3.24842,ry:3.24842,fill:"white",stroke:"#157901",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M16.0255 14.6992C16.4104 14.0326 17.3727 14.0326 17.7576 14.6992L20.0715 18.7071C20.4564 19.3737 19.9753 20.2071 19.2055 20.2071H14.5776C13.8078 20.2071 13.3267 19.3737 13.7116 18.7071L16.0255 14.6992Z",fill:"#C0E4B8",stroke:"#157901",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"})]}),l=s;export{l as default};

//# debugId=178dc40e-bfed-5873-b7e7-69d91f35af5b
