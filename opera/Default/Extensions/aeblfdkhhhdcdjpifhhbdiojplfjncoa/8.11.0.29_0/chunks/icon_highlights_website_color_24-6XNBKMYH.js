
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="7cad5b44-0186-5b14-9bf7-859d6fd2f89a")}catch(e){}}();
import{a as r,b as i}from"/chunks/chunk-EXMDW5FT.js";import{c as e}from"/chunks/chunk-54UHBIHR.js";var l=e(r()),t=e(i()),s=h=>(0,t.jsxs)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...h,children:[(0,t.jsx)("rect",{width:24,height:24,rx:4,fill:"#FFD480"}),(0,t.jsx)("mask",{id:"mask0_3067_248596",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:24,height:24,children:(0,t.jsx)("rect",{width:24,height:24,rx:4.125,fill:"#C2EAFF"})}),(0,t.jsxs)("g",{mask:"url(#mask0_3067_248596)",children:[(0,t.jsx)("rect",{x:7.06189,y:6.75,width:23.8278,height:20.4397,rx:2,fill:"white"}),(0,t.jsx)("path",{d:"M6.99438 8.69336C6.99438 7.58879 7.88982 6.69336 8.99438 6.69336H26.3847V12.1927H6.99438V8.69336Z",fill:"#FFD480"}),(0,t.jsx)("rect",{x:7.06335,y:6.79297,width:23.778,height:20.5429,rx:2,stroke:"#EB7100",strokeMiterlimit:10,strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.jsx)("path",{d:"M7.56702 12.582L27.8126 12.582",stroke:"#EB7100",strokeLinecap:"round"}),(0,t.jsx)("rect",{x:13.1128,y:8.7168,width:11.9059,height:2.21508,rx:1.10754,fill:"white"}),(0,t.jsx)("ellipse",{cx:9.83172,cy:9.81277,rx:1.09402,ry:1.09402,fill:"#EB7100"}),(0,t.jsx)("rect",{x:.5,y:.5,width:23,height:23,rx:3.5,stroke:"#EB7100"})]})]}),o=s;export{o as default};

//# debugId=7cad5b44-0186-5b14-9bf7-859d6fd2f89a
