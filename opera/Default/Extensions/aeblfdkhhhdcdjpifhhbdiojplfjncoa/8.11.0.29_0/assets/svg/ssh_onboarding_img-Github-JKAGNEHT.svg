<svg width="175" height="100" viewBox="0 0 175 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_158_337197)">
<path d="M169.67 43.4837C170.821 36.9569 166.463 30.733 159.936 29.5822L124.483 23.3308C117.956 22.18 111.733 26.538 110.582 33.0648L104.33 68.5178C103.18 75.0446 107.538 81.2685 114.064 82.4193L149.517 88.6706C156.044 89.8215 162.268 85.4635 163.419 78.9367L169.67 43.4837Z" fill="#24292F"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M140.486 36.3827C135.688 35.5317 130.744 36.4139 126.541 38.8713C122.338 41.3286 119.149 45.2005 117.547 49.7931C115.945 54.3856 116.034 59.3986 117.798 63.9337C119.563 68.4687 122.887 72.2295 127.175 74.5419C128.146 74.9046 128.629 74.3391 128.723 73.8071C128.817 73.275 129.031 72.0625 129.327 70.3797C123.494 70.6269 122.997 66.4823 122.997 66.4823C122.838 65.1969 122.228 64.009 121.275 63.1295C119.671 61.5709 121.642 61.9184 121.642 61.9184C122.265 62.1211 122.834 62.4626 123.305 62.917C123.776 63.3715 124.138 63.9269 124.362 64.5414C124.549 65.0861 124.842 65.5888 125.223 66.0206C125.605 66.4523 126.068 66.8045 126.587 67.0571C127.105 67.3097 127.668 67.4577 128.244 67.4926C128.82 67.5274 129.396 67.4485 129.941 67.2602C130.199 66.256 130.807 65.3769 131.657 64.7814C127.259 63.4829 122.853 60.92 124.215 53.1991C124.537 51.1929 125.619 49.3876 127.238 48.1571C126.938 46.3106 127.345 44.4194 128.377 42.8592C128.377 42.8592 130.171 42.6142 133.566 45.9047C137.037 45.5812 140.531 46.1974 143.682 47.6884C147.997 45.7574 149.587 46.5991 149.587 46.5991C150.032 48.4173 149.771 50.3356 148.858 51.9691C149.958 53.6792 150.358 55.7459 149.974 57.7412C148.609 65.4869 143.587 66.3492 139.006 66.0136C139.403 66.5828 139.673 67.2305 139.798 67.9128C139.922 68.5951 139.898 69.2961 139.728 69.9681C139.252 72.6655 138.868 74.8432 138.752 75.499C138.637 76.1548 138.907 76.7255 139.968 76.7213C144.777 75.9982 149.171 73.5932 152.366 69.9354C155.562 66.2776 157.351 61.6048 157.414 56.7505C157.478 51.8962 155.812 47.1761 152.714 43.4319C149.616 39.6878 145.287 37.1632 140.499 36.3085L140.486 36.3827Z" fill="white"/>
</g>
<rect width="64" height="64" transform="translate(0.11377 32) rotate(-10)" fill="black" fill-opacity="0.01"/>
<g clip-path="url(#clip1_158_337197)">
<path d="M6.13687 43.1235C5.17784 37.6845 8.80952 32.4979 14.2485 31.5389L51.6712 24.9403C57.1101 23.9812 62.2967 27.6129 63.2557 33.0519L69.8544 70.4746C70.8134 75.9135 67.1817 81.1001 61.7428 82.0591L24.3201 88.6578C18.8811 89.6168 13.6945 85.9851 12.7355 80.5462L6.13687 43.1235Z" fill="url(#paint0_linear_158_337197)"/>
<path d="M6.62928 43.0366C5.7182 37.8696 9.1683 32.9424 14.3353 32.0313L51.758 25.4327C56.925 24.5216 61.8522 27.9717 62.7633 33.1387L69.3619 70.5614C70.273 75.7284 66.8229 80.6556 61.6559 81.5667L24.2332 88.1654C19.0662 89.0764 14.139 85.6263 13.2279 80.4593L6.62928 43.0366Z" stroke="black" stroke-opacity="0.06"/>
<g filter="url(#filter0_i_158_337197)">
<rect x="10.1929" y="37.3311" width="48" height="48" rx="8" transform="rotate(-10 10.1929 37.3311)" fill="black" fill-opacity="0.45"/>
</g>
<rect x="10.7721" y="37.7366" width="47" height="47" rx="7.5" transform="rotate(-10 10.7721 37.7366)" stroke="#35414C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.3539 41.6029C20.7091 40.5785 21.7792 40.078 22.744 40.4849L32.9303 44.7811C33.5439 45.0399 33.994 45.6216 34.116 46.3134C34.238 47.0052 34.014 47.7058 33.5259 48.1589L25.4233 55.6799C24.6558 56.3923 23.4791 56.2879 22.7949 55.4468C22.1108 54.6057 22.1783 53.3463 22.9458 52.634L28.7324 47.2627L21.4577 44.1945C20.4928 43.7876 19.9987 42.6273 20.3539 41.6029Z" fill="#F8F8F9"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.8219 55.8143C37.6301 54.7266 38.3564 53.6892 39.4442 53.4974L51.2619 51.4137C52.3497 51.2218 53.387 51.9482 53.5788 53.036C53.7706 54.1238 53.0443 55.1611 51.9565 55.3529L40.1388 57.4367C39.051 57.6285 38.0137 56.9021 37.8219 55.8143Z" fill="#F8F8F9"/>
<g opacity="0.3" filter="url(#filter1_f_158_337197)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.4013 74.709C26.7701 74.0179 26.919 73.2029 26.7724 72.3717C26.4243 70.3974 24.5416 69.0792 22.5674 69.4273C20.5931 69.7754 19.2749 71.6581 19.623 73.6323C19.7695 74.4632 20.1878 75.1779 20.7704 75.7011C20.6688 74.2518 21.6768 72.9306 23.1393 72.6727C24.6021 72.4148 26.0013 73.312 26.4013 74.709Z" fill="black"/>
</g>
<path opacity="0.3" d="M59.716 76.3239L46.9135 78.5814L48.8994 75.1849L55.8825 73.9536L59.716 76.3239Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6369 90.2799C31.7405 89.38 35.7074 85.8213 37.3645 81.2796L47.3095 79.5267L50.158 75.4699L56.4781 74.3555L60.3321 77.0088C60.3321 77.0088 64.7403 75.2039 64.654 69.7758L57.4983 64.6921L35.625 68.3873C32.591 64.119 27.275 61.7354 21.7748 62.7052C14.1602 64.0479 9.07589 71.3091 10.4185 78.9236C11.7612 86.5381 19.0224 91.6225 26.6369 90.2799ZM24.2058 76.4926C25.8375 76.2048 26.927 74.6489 26.6393 73.0172C26.3516 71.3855 24.7956 70.296 23.164 70.5837C21.5323 70.8714 20.4428 72.4274 20.7305 74.0591C21.0182 75.6908 22.5742 76.7803 24.2058 76.4926Z" fill="url(#paint1_linear_158_337197)"/>
<g filter="url(#filter2_f_158_337197)">
<ellipse cx="24.2059" cy="76.4921" rx="11" ry="11" transform="rotate(-10 24.2059 76.4921)" stroke="#EB7100" stroke-width="0.5"/>
</g>
<path opacity="0.3" d="M34.054 74.7556C35.013 80.1945 31.3813 85.3811 25.9424 86.3402C20.5034 87.2992 15.3169 83.6675 14.3578 78.2286C13.3988 72.7896 17.0305 67.603 22.4694 66.644C27.9084 65.685 33.0949 69.3167 34.054 74.7556Z" stroke="white" stroke-width="2"/>
<path d="M34.7926 74.6254C35.8235 80.4722 31.9195 86.0478 26.0726 87.0788C20.2258 88.1097 14.6502 84.2057 13.6192 78.3588C12.5883 72.5119 16.4923 66.9364 22.3392 65.9054C28.186 64.8744 33.7616 68.7785 34.7926 74.6254Z" stroke="#EB7100" stroke-width="0.5"/>
<g filter="url(#filter3_i_158_337197)">
<path d="M42.9855 73.1809L64.1868 69.4426L60.7537 67.0017L42.4646 70.2265C41.6487 70.3704 41.104 71.1484 41.2478 71.9642C41.3917 72.78 42.1697 73.3248 42.9855 73.1809Z" fill="#FFAA05"/>
</g>
</g>
<g clip-path="url(#clip2_158_337197)">
<rect width="95.7241" height="95.7241" transform="translate(39.0005 2)" fill="black" fill-opacity="0.01"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8622 87.8806C107.859 87.8806 124.881 70.8589 124.881 49.8616C124.881 28.8642 107.859 11.8428 86.8622 11.8428C65.865 11.8428 48.8433 28.8642 48.8433 49.8616C48.8433 70.8589 65.865 87.8806 86.8622 87.8806ZM82.9797 31.9142C81.8199 31.9142 80.8797 32.8544 80.8797 34.0142V42.0498C80.8797 42.4099 81.1754 42.6991 81.4421 42.9601C81.4752 42.9924 81.5078 43.0243 81.5393 43.0558L82.7929 44.3095C83.1574 44.674 83.1574 45.2649 82.7929 45.6294L81.5393 46.8831C81.5078 46.9146 81.4752 46.9465 81.4422 46.9788C81.1754 47.2398 80.8797 47.529 80.8797 47.8892V65.7107C80.8797 66.8705 81.8199 67.8107 82.9797 67.8107H90.7452C91.905 67.8107 92.8452 66.8705 92.8452 65.7107V57.6157C92.8452 57.2577 92.5522 56.9701 92.2881 56.711C92.2577 56.6812 92.2277 56.6517 92.1985 56.6226L90.9449 55.3689C90.5804 55.0044 90.5804 54.4135 90.9449 54.049L92.1985 52.7953C92.2277 52.7662 92.2577 52.7368 92.2881 52.7069C92.5522 52.4478 92.8452 52.1602 92.8452 51.8022V34.0142C92.8452 32.8544 91.905 31.9142 90.7452 31.9142H82.9797Z" fill="url(#paint2_linear_158_337197)"/>
<mask id="mask0_158_337197" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="48" y="11" width="77" height="77">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8622 87.8806C107.859 87.8806 124.881 70.8589 124.881 49.8616C124.881 28.8642 107.859 11.8428 86.8622 11.8428C65.865 11.8428 48.8433 28.8642 48.8433 49.8616C48.8433 70.8589 65.865 87.8806 86.8622 87.8806ZM82.9797 31.9142C81.8199 31.9142 80.8797 32.8544 80.8797 34.0142V42.0498C80.8797 42.4099 81.1754 42.6991 81.4421 42.9601C81.4752 42.9924 81.5078 43.0243 81.5393 43.0558L82.7929 44.3095C83.1574 44.674 83.1574 45.2649 82.7929 45.6294L81.5393 46.8831C81.5078 46.9146 81.4752 46.9465 81.4422 46.9788C81.1754 47.2398 80.8797 47.529 80.8797 47.8892V65.7107C80.8797 66.8705 81.8199 67.8107 82.9797 67.8107H90.7452C91.905 67.8107 92.8452 66.8705 92.8452 65.7107V57.6157C92.8452 57.2577 92.5522 56.9701 92.2881 56.711C92.2577 56.6812 92.2277 56.6517 92.1985 56.6226L90.9449 55.3689C90.5804 55.0044 90.5804 54.4135 90.9449 54.049L92.1985 52.7953C92.2277 52.7662 92.2577 52.7368 92.2881 52.7069C92.5522 52.4478 92.8452 52.1602 92.8452 51.8022V34.0142C92.8452 32.8544 91.905 31.9142 90.7452 31.9142H82.9797Z" fill="url(#paint3_linear_158_337197)"/>
</mask>
<g mask="url(#mask0_158_337197)">
<g filter="url(#filter4_ddi_158_337197)">
<circle cx="86.8623" cy="49.8623" r="25.4267" fill="url(#paint4_radial_158_337197)"/>
<circle cx="86.8623" cy="49.8623" r="25.4267" fill="url(#paint5_linear_158_337197)"/>
</g>
<g filter="url(#filter5_dii_158_337197)">
<ellipse cx="86.8628" cy="49.8613" rx="12.751" ry="12.751" fill="black" fill-opacity="0.03"/>
</g>
</g>
<circle cx="86.8622" cy="49.8617" r="41.8793" fill="black" fill-opacity="0.01" stroke="black" stroke-opacity="0.3" stroke-width="2.98333"/>
<g style="mix-blend-mode:multiply" filter="url(#filter6_dd_158_337197)">
<circle cx="86.8622" cy="49.8617" r="41.8793" fill="white"/>
</g>
<ellipse cx="86.8632" cy="49.8618" rx="34.4008" ry="34.4008" fill="black" fill-opacity="0.01" stroke="black" stroke-opacity="0.2" stroke-width="2.98333"/>
<g filter="url(#filter7_iii_158_337197)">
<ellipse cx="86.8632" cy="49.8618" rx="34.4008" ry="34.4008" fill="black" fill-opacity="0.01"/>
</g>
<mask id="mask1_158_337197" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="44" y="7" width="85" height="85">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8622 91.741C109.991 91.741 128.741 72.991 128.741 49.8617C128.741 26.7324 109.991 7.98242 86.8622 7.98242C63.7329 7.98242 44.9829 26.7324 44.9829 49.8617C44.9829 72.991 63.7329 91.741 86.8622 91.741ZM86.8624 84.2628C105.862 84.2628 121.263 68.861 121.263 49.862C121.263 30.8629 105.862 15.4611 86.8624 15.4611C67.8634 15.4611 52.4616 30.8629 52.4616 49.862C52.4616 68.861 67.8634 84.2628 86.8624 84.2628Z" fill="white"/>
</mask>
<g mask="url(#mask1_158_337197)">
<ellipse cx="86.8623" cy="49.8623" rx="42.1132" ry="42.1132" fill="url(#paint6_radial_158_337197)"/>
<g opacity="0.7">
<g filter="url(#filter8_iiiiiii_158_337197)">
<ellipse cx="86.8628" cy="49.8623" rx="42.1132" ry="42.1132" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter9_i_158_337197)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8628 91.9755C110.121 91.9755 128.976 73.1208 128.976 49.8623C128.976 26.6038 110.121 7.74902 86.8628 7.74902C63.6043 7.74902 44.7495 26.6038 44.7495 49.8623C44.7495 73.1208 63.6043 91.9755 86.8628 91.9755ZM86.8626 83.3187C105.34 83.3187 120.319 68.3397 120.319 49.8621C120.319 31.3845 105.34 16.4055 86.8626 16.4055C68.385 16.4055 53.406 31.3845 53.406 49.8621C53.406 68.3397 68.385 83.3187 86.8626 83.3187Z" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter10_dd_158_337197)">
<circle cx="86.8624" cy="49.8619" r="33.4566" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter11_f_158_337197)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M107.131 79.7523C116.917 73.2056 123.361 62.0526 123.361 49.3946C123.361 29.2373 107.02 12.8965 86.8624 12.8965C66.705 12.8965 50.3643 29.2373 50.3643 49.3946C50.3643 58.7714 53.9003 67.3223 59.7116 73.7866C54.607 67.6921 51.5339 59.8381 51.5339 51.2664C51.5339 31.8843 67.2462 16.172 86.6283 16.172C106.01 16.172 121.723 31.8843 121.723 51.2664C121.723 62.9949 115.969 73.3797 107.131 79.7523Z" fill="black" fill-opacity="0.07"/>
</g>
</g>
</g>
<mask id="mask2_158_337197" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="80" y="31" width="13" height="37">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8799 34.0141C80.8799 32.8543 81.8201 31.9141 82.9799 31.9141H90.7454C91.9052 31.9141 92.8454 32.8543 92.8454 34.0141V51.8021C92.8454 52.2013 92.481 52.513 92.1987 52.7952V52.7952L90.945 54.0489C90.5806 54.4134 90.5806 55.0043 90.945 55.3688L92.1987 56.6225V56.6225C92.481 56.9048 92.8454 57.2164 92.8454 57.6156V65.7106C92.8454 66.8704 91.9052 67.8106 90.7454 67.8106H82.9799C81.8201 67.8106 80.8799 66.8704 80.8799 65.7106V47.889C80.8799 47.4843 81.2533 47.1691 81.5394 46.883V46.883L82.7931 45.6293C83.1576 45.2648 83.1576 44.6739 82.7931 44.3094L81.5394 43.0557V43.0557C81.2533 42.7695 80.8799 42.4544 80.8799 42.0496V34.0141Z" fill="black"/>
</mask>
<g mask="url(#mask2_158_337197)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8799 34.0141C80.8799 32.8543 81.8201 31.9141 82.9799 31.9141H90.7454C91.9052 31.9141 92.8454 32.8543 92.8454 34.0141V51.8021C92.8454 52.2013 92.481 52.513 92.1987 52.7952V52.7952L90.945 54.0489C90.5806 54.4134 90.5806 55.0043 90.945 55.3688L92.1987 56.6225V56.6225C92.481 56.9048 92.8454 57.2164 92.8454 57.6156V65.7106C92.8454 66.8704 91.9052 67.8106 90.7454 67.8106H82.9799C81.8201 67.8106 80.8799 66.8704 80.8799 65.7106V47.889C80.8799 47.4843 81.2533 47.1691 81.5394 46.883V46.883L82.7931 45.6293C83.1576 45.2648 83.1576 44.6739 82.7931 44.3094L81.5394 43.0557V43.0557C81.2533 42.7695 80.8799 42.4544 80.8799 42.0496V34.0141Z" fill="#808080" fill-opacity="0.7"/>
<g opacity="0.8" filter="url(#filter12_i_158_337197)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8799 34.0141C80.8799 32.8543 81.8201 31.9141 82.9799 31.9141H90.7454C91.9052 31.9141 92.8454 32.8543 92.8454 34.0141V51.8021C92.8454 52.2013 92.481 52.513 92.1987 52.7952V52.7952L90.945 54.0489C90.5806 54.4134 90.5806 55.0043 90.945 55.3688L92.1987 56.6225V56.6225C92.481 56.9048 92.8454 57.2164 92.8454 57.6156V65.7106C92.8454 66.8704 91.9052 67.8106 90.7454 67.8106H82.9799C81.8201 67.8106 80.8799 66.8704 80.8799 65.7106V47.889C80.8799 47.4843 81.2533 47.1691 81.5394 46.883V46.883L82.7931 45.6293C83.1576 45.2648 83.1576 44.6739 82.7931 44.3094L81.5394 43.0557V43.0557C81.2533 42.7695 80.8799 42.4544 80.8799 42.0496V34.0141Z" fill="black" fill-opacity="0.01"/>
</g>
<path d="M90.945 54.0489L91.275 54.3789L91.275 54.3789L90.945 54.0489ZM90.945 55.3688L90.6151 55.6988L90.6151 55.6988L90.945 55.3688ZM82.7931 45.6293L83.1231 45.9593L83.1231 45.9593L82.7931 45.6293ZM82.7931 44.3094L83.1231 43.9794L83.1231 43.9794L82.7931 44.3094ZM82.9799 31.4474C81.5623 31.4474 80.4132 32.5965 80.4132 34.0141H81.3465C81.3465 33.112 82.0778 32.3807 82.9799 32.3807V31.4474ZM90.7454 31.4474H82.9799V32.3807H90.7454V31.4474ZM93.3121 34.0141C93.3121 32.5965 92.1629 31.4474 90.7454 31.4474V32.3807C91.6475 32.3807 92.3787 33.112 92.3787 34.0141H93.3121ZM93.3121 51.8021V34.0141H92.3787V51.8021H93.3121ZM91.275 54.3789L92.5287 53.1252L91.8687 52.4652L90.6151 53.7189L91.275 54.3789ZM91.275 55.0388C91.0928 54.8566 91.0928 54.5611 91.275 54.3789L90.6151 53.7189C90.0683 54.2656 90.0683 55.1521 90.6151 55.6988L91.275 55.0388ZM92.5287 56.2925L91.275 55.0388L90.6151 55.6988L91.8687 56.9525L92.5287 56.2925ZM93.3121 65.7106V57.6156H92.3787V65.7106H93.3121ZM90.7454 68.2773C92.1629 68.2773 93.3121 67.1281 93.3121 65.7106H92.3787C92.3787 66.6126 91.6475 67.3439 90.7454 67.3439V68.2773ZM82.9799 68.2773H90.7454V67.3439H82.9799V68.2773ZM80.4132 65.7106C80.4132 67.1281 81.5623 68.2773 82.9799 68.2773V67.3439C82.0778 67.3439 81.3465 66.6127 81.3465 65.7106H80.4132ZM80.4132 47.889V65.7106H81.3465V47.889H80.4132ZM82.4631 45.2993L81.2095 46.553L81.8694 47.213L83.1231 45.9593L82.4631 45.2993ZM82.4631 44.6394C82.6454 44.8216 82.6454 45.1171 82.4631 45.2993L83.1231 45.9593C83.6698 45.4126 83.6698 44.5261 83.1231 43.9794L82.4631 44.6394ZM81.2095 43.3857L82.4631 44.6394L83.1231 43.9794L81.8694 42.7257L81.2095 43.3857ZM80.4132 34.0141V42.0496H81.3465V34.0141H80.4132ZM81.8694 42.7257C81.7044 42.5607 81.5758 42.4451 81.47 42.3097C81.37 42.1818 81.3465 42.1025 81.3465 42.0496H80.4132C80.4132 42.4015 80.5765 42.6822 80.7347 42.8846C80.8871 43.0795 81.0883 43.2645 81.2095 43.3857L81.8694 42.7257ZM81.3465 47.889C81.3465 47.8362 81.37 47.7569 81.47 47.629C81.5758 47.4936 81.7044 47.378 81.8694 47.2129L81.2095 46.553C81.0883 46.6742 80.8871 46.8592 80.7347 47.0541C80.5765 47.2565 80.4132 47.5371 80.4132 47.889H81.3465ZM91.8687 56.9525C92.0303 57.114 92.1565 57.2287 92.259 57.361C92.3561 57.4863 92.3787 57.564 92.3787 57.6156H93.3121C93.3121 57.268 93.1525 56.9902 92.9968 56.7893C92.8465 56.5955 92.6494 56.4132 92.5287 56.2925L91.8687 56.9525ZM92.3787 51.8021C92.3787 51.8537 92.3561 51.9314 92.259 52.0567C92.1565 52.189 92.0303 52.3037 91.8687 52.4652L92.5287 53.1252C92.6494 53.0045 92.8465 52.8222 92.9968 52.6284C93.1524 52.4275 93.3121 52.1497 93.3121 51.8021H92.3787Z" fill="black" fill-opacity="0.5"/>
</g>
</g>
<defs>
<filter id="filter0_i_158_337197" x="11.459" y="30.2627" width="53.0732" height="54.0732" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_158_337197"/>
</filter>
<filter id="filter1_f_158_337197" x="18.5674" y="68.3711" width="9.26074" height="8.33008" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_158_337197"/>
</filter>
<filter id="filter2_f_158_337197" x="10.9541" y="63.2402" width="26.5039" height="26.5039" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_158_337197"/>
</filter>
<filter id="filter3_i_158_337197" x="41.2246" y="67.002" width="22.9619" height="7.20215" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.921569 0 0 0 0 0.443137 0 0 0 0 0 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_158_337197"/>
</filter>
<filter id="filter4_ddi_158_337197" x="59.1981" y="22.9439" width="55.3285" height="56.0743" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.49166"/>
<feGaussianBlur stdDeviation="1.11875"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.49166" operator="dilate" in="SourceAlpha" result="effect2_dropShadow_158_337197"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_158_337197" result="effect2_dropShadow_158_337197"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_158_337197" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feGaussianBlur stdDeviation="0.466665"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_158_337197"/>
</filter>
<filter id="filter5_dii_158_337197" x="73.6452" y="36.877" width="26.4353" height="29.4686" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.233333" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_158_337197"/>
<feOffset dy="0.233333"/>
<feGaussianBlur stdDeviation="0.116666"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.34902 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_158_337197"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_158_337197" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.73332"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.466665"/>
<feGaussianBlur stdDeviation="0.699998"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_158_337197" result="effect3_innerShadow_158_337197"/>
</filter>
<filter id="filter6_dd_158_337197" x="41.9996" y="5.74493" width="89.7254" height="89.7254" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.745831"/>
<feGaussianBlur stdDeviation="1.49166"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.372916"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_158_337197" result="effect2_dropShadow_158_337197"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_158_337197" result="shape"/>
</filter>
<filter id="filter7_iii_158_337197" x="52.4624" y="15.4609" width="68.8018" height="72.5351" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.73332"/>
<feGaussianBlur stdDeviation="4.66665"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feGaussianBlur stdDeviation="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_158_337197" result="effect2_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_158_337197" result="effect3_innerShadow_158_337197"/>
</filter>
<filter id="filter8_iiiiiii_158_337197" x="44.7495" y="7.74902" width="84.2266" height="86.3266" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="5.36665" operator="erode" in="SourceAlpha" result="effect1_innerShadow_158_337197"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4.89999" operator="erode" in="SourceAlpha" result="effect2_innerShadow_158_337197"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_158_337197" result="effect2_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3.03332" operator="erode" in="SourceAlpha" result="effect3_innerShadow_158_337197"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_158_337197" result="effect3_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.09999"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_158_337197" result="effect4_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.63333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_158_337197" result="effect5_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.09999"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0196078 0 0 0 0 0.447059 0 0 0 0 0.92549 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect5_innerShadow_158_337197" result="effect6_innerShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.49999"/>
<feGaussianBlur stdDeviation="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect6_innerShadow_158_337197" result="effect7_innerShadow_158_337197"/>
</filter>
<filter id="filter9_i_158_337197" x="44.7495" y="6.11569" width="84.2266" height="85.8599" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.63333"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_158_337197"/>
</filter>
<filter id="filter10_dd_158_337197" x="52.7058" y="15.7053" width="68.3131" height="68.5464" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.699998" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_158_337197"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_158_337197"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.466665"/>
<feGaussianBlur stdDeviation="0.233333"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_158_337197" result="effect2_dropShadow_158_337197"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_158_337197" result="shape"/>
</filter>
<filter id="filter11_f_158_337197" x="49.8976" y="12.4298" width="73.9294" height="67.7888" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.233333" result="effect1_foregroundBlur_158_337197"/>
</filter>
<filter id="filter12_i_158_337197" x="80.8799" y="31.9141" width="11.9653" height="43.3631" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.46665"/>
<feGaussianBlur stdDeviation="10.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_158_337197"/>
</filter>
<linearGradient id="paint0_linear_158_337197" x1="7.49782" y1="6.94314" x2="31.45" y2="144.277" gradientUnits="userSpaceOnUse">
<stop stop-color="#B9BDC2"/>
<stop offset="0.9999" stop-color="#848D96"/>
</linearGradient>
<linearGradient id="paint1_linear_158_337197" x1="32.362" y1="113.997" x2="24.7987" y2="64.5826" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFAA05"/>
<stop offset="1" stop-color="#FFD480"/>
</linearGradient>
<linearGradient id="paint2_linear_158_337197" x1="48.8433" y1="11.8428" x2="48.8433" y2="87.8806" gradientUnits="userSpaceOnUse">
<stop stop-color="#0077FF" stop-opacity="0.998825"/>
<stop offset="1" stop-color="#00B9F9"/>
</linearGradient>
<linearGradient id="paint3_linear_158_337197" x1="48.8433" y1="11.8428" x2="48.8433" y2="87.8806" gradientUnits="userSpaceOnUse">
<stop stop-color="#0077FF" stop-opacity="0.998825"/>
<stop offset="1" stop-color="#00B9F9"/>
</linearGradient>
<radialGradient id="paint4_radial_158_337197" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.8623 49.8623) rotate(90) scale(25.4267)">
<stop offset="0.720183" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.9"/>
</radialGradient>
<linearGradient id="paint5_linear_158_337197" x1="61.4355" y1="24.4355" x2="61.4355" y2="75.289" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.1"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint6_radial_158_337197" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.8623 49.8623) rotate(90) scale(42.1132 42.1132)">
<stop offset="0.86112" stop-color="white" stop-opacity="0.75"/>
<stop offset="0.907821" stop-color="white"/>
</radialGradient>
<clipPath id="clip0_158_337197">
<rect width="64" height="64" fill="white" transform="translate(111.043 18.9297) rotate(10)"/>
</clipPath>
<clipPath id="clip1_158_337197">
<rect width="64" height="64" fill="white" transform="translate(0.11377 32) rotate(-10)"/>
</clipPath>
<clipPath id="clip2_158_337197">
<rect width="95.7241" height="95.7241" fill="white" transform="translate(39.0005 2)"/>
</clipPath>
</defs>
</svg>
