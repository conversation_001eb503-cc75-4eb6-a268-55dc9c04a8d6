<svg width="175" height="100" viewBox="0 0 175 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="112.736" y="21.3174" width="60" height="60" rx="12" transform="rotate(10 112.736 21.3174)" fill="white"/>
<path d="M121.778 35.0965C121.595 35.0617 121.406 35.0675 121.224 35.1136C121.043 35.1597 120.874 35.245 120.729 35.3634C120.584 35.4818 120.467 35.6305 120.386 35.7991C120.305 35.9677 120.261 36.152 120.258 36.3392L119.875 69.8315C119.872 70.2416 120.013 70.6397 120.274 70.956C120.534 71.2723 120.898 71.4865 121.301 71.5609L146.996 76.0917C147.298 76.1489 147.61 76.0957 147.876 75.9418C148.142 75.7879 148.344 75.5436 148.445 75.2532L159.616 43.292C159.678 43.1152 159.7 42.9271 159.681 42.7409C159.662 42.5547 159.603 42.3748 159.507 42.214C159.412 42.0532 159.282 41.9153 159.127 41.81C158.973 41.7047 158.797 41.6345 158.613 41.6044L121.778 35.0965ZM140.184 62.597L131.983 61.1509L131.809 49.1522L144.218 51.3403L140.184 62.597Z" fill="#2684FF"/>
<path d="M156.059 53.4279L144.218 51.34L140.184 62.5967L131.983 61.1506L120.269 70.9555C120.529 71.2753 120.894 71.492 121.3 71.5669L147.001 76.0988C147.303 76.156 147.615 76.1028 147.881 75.9489C148.147 75.795 148.349 75.5507 148.45 75.2603L156.059 53.4279Z" fill="url(#paint0_linear_163_339510)"/>
<rect x="113.142" y="21.8966" width="59" height="59" rx="11.5" transform="rotate(10 113.142 21.8966)" stroke="black" stroke-opacity="0.12"/>
<rect width="64" height="64" transform="translate(0.11377 31.999) rotate(-10)" fill="black" fill-opacity="0.01"/>
<g clip-path="url(#clip0_163_339510)">
<path d="M6.13687 43.1225C5.17784 37.6835 8.80952 32.497 14.2485 31.5379L51.6712 24.9393C57.1101 23.9803 62.2967 27.612 63.2557 33.0509L69.8544 70.4736C70.8134 75.9125 67.1817 81.0991 61.7428 82.0581L24.3201 88.6568C18.8811 89.6158 13.6945 85.9841 12.7355 80.5452L6.13687 43.1225Z" fill="url(#paint1_linear_163_339510)"/>
<path d="M6.62928 43.0357C5.7182 37.8687 9.1683 32.9414 14.3353 32.0303L51.758 25.4317C56.925 24.5206 61.8522 27.9707 62.7633 33.1377L69.3619 70.5604C70.273 75.7274 66.8229 80.6547 61.6559 81.5657L24.2332 88.1644C19.0662 89.0755 14.139 85.6254 13.2279 80.4584L6.62928 43.0357Z" stroke="black" stroke-opacity="0.06"/>
<g filter="url(#filter0_i_163_339510)">
<rect x="10.1929" y="37.3301" width="48" height="48" rx="8" transform="rotate(-10 10.1929 37.3301)" fill="black" fill-opacity="0.45"/>
</g>
<rect x="10.7721" y="37.7357" width="47" height="47" rx="7.5" transform="rotate(-10 10.7721 37.7357)" stroke="#35414C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.3539 41.6019C20.7091 40.5776 21.7792 40.077 22.744 40.4839L32.9303 44.7801C33.5439 45.0389 33.994 45.6206 34.116 46.3124C34.238 47.0043 34.014 47.7048 33.5259 48.1579L25.4233 55.6789C24.6558 56.3913 23.4791 56.2869 22.7949 55.4458C22.1108 54.6047 22.1783 53.3454 22.9458 52.633L28.7324 47.2617L21.4577 44.1935C20.4928 43.7866 19.9987 42.6263 20.3539 41.6019Z" fill="#F8F8F9"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.8219 55.8134C37.6301 54.7256 38.3564 53.6883 39.4442 53.4965L51.2619 51.4127C52.3497 51.2209 53.387 51.9472 53.5788 53.035C53.7706 54.1228 53.0443 55.1601 51.9565 55.3519L40.1388 57.4357C39.051 57.6275 38.0137 56.9012 37.8219 55.8134Z" fill="#F8F8F9"/>
<g opacity="0.3" filter="url(#filter1_f_163_339510)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.4013 74.708C26.7701 74.017 26.919 73.202 26.7724 72.3707C26.4243 70.3965 24.5416 69.0782 22.5674 69.4263C20.5931 69.7744 19.2749 71.6571 19.623 73.6313C19.7695 74.4623 20.1878 75.177 20.7704 75.7002C20.6688 74.2509 21.6768 72.9296 23.1393 72.6717C24.6021 72.4138 26.0013 73.311 26.4013 74.708Z" fill="black"/>
</g>
<path opacity="0.3" d="M59.716 76.323L46.9135 78.5804L48.8994 75.184L55.8825 73.9526L59.716 76.323Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6369 90.2789C31.7405 89.379 35.7074 85.8204 37.3645 81.2786L47.3095 79.5258L50.158 75.4689L56.4781 74.3545L60.3321 77.0078C60.3321 77.0078 64.7403 75.2029 64.654 69.7748L57.4983 64.6911L35.625 68.3863C32.591 64.118 27.275 61.7344 21.7748 62.7043C14.1602 64.0469 9.07589 71.3081 10.4185 78.9227C11.7612 86.5372 19.0224 91.6215 26.6369 90.2789ZM24.2058 76.4916C25.8375 76.2039 26.927 74.6479 26.6393 73.0162C26.3516 71.3845 24.7956 70.295 23.164 70.5827C21.5323 70.8704 20.4428 72.4264 20.7305 74.0581C21.0182 75.6898 22.5742 76.7793 24.2058 76.4916Z" fill="url(#paint2_linear_163_339510)"/>
<g filter="url(#filter2_f_163_339510)">
<ellipse cx="24.2059" cy="76.4911" rx="11" ry="11" transform="rotate(-10 24.2059 76.4911)" stroke="#EB7100" stroke-width="0.5"/>
</g>
<path opacity="0.3" d="M34.054 74.7546C35.013 80.1936 31.3813 85.3802 25.9424 86.3392C20.5034 87.2982 15.3169 83.6665 14.3578 78.2276C13.3988 72.7887 17.0305 67.6021 22.4694 66.643C27.9084 65.684 33.0949 69.3157 34.054 74.7546Z" stroke="white" stroke-width="2"/>
<path d="M34.7926 74.6244C35.8235 80.4713 31.9195 86.0468 26.0726 87.0778C20.2258 88.1088 14.6502 84.2047 13.6192 78.3578C12.5883 72.511 16.4923 66.9354 22.3392 65.9044C28.186 64.8735 33.7616 68.7775 34.7926 74.6244Z" stroke="#EB7100" stroke-width="0.5"/>
<g filter="url(#filter3_i_163_339510)">
<path d="M42.9855 73.18L64.1868 69.4416L60.7537 67.0007L42.4646 70.2255C41.6487 70.3694 41.104 71.1474 41.2478 71.9632C41.3917 72.7791 42.1697 73.3238 42.9855 73.18Z" fill="#FFAA05"/>
</g>
</g>
<g clip-path="url(#clip1_163_339510)">
<rect width="95.7241" height="95.7241" transform="translate(39.0005 2)" fill="black" fill-opacity="0.01"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8622 87.8806C107.859 87.8806 124.881 70.8589 124.881 49.8616C124.881 28.8642 107.859 11.8428 86.8622 11.8428C65.865 11.8428 48.8433 28.8642 48.8433 49.8616C48.8433 70.8589 65.865 87.8806 86.8622 87.8806ZM82.9797 31.9135C81.8199 31.9135 80.8797 32.8537 80.8797 34.0134V42.0498C80.8797 42.4099 81.1754 42.6991 81.4422 42.9601C81.4752 42.9924 81.5078 43.0243 81.5393 43.0558L82.7929 44.3095C83.1574 44.674 83.1574 45.2649 82.7929 45.6294L81.5393 46.8831C81.5078 46.9146 81.4752 46.9465 81.4422 46.9788C81.1754 47.2398 80.8797 47.529 80.8797 47.8892V65.71C80.8797 66.8698 81.8199 67.81 82.9797 67.81H90.7452C91.905 67.81 92.8452 66.8698 92.8452 65.71V57.6157C92.8452 57.2577 92.5522 56.9701 92.2881 56.711C92.2577 56.6812 92.2277 56.6517 92.1985 56.6226L90.9449 55.3689C90.5804 55.0044 90.5804 54.4135 90.9449 54.049L92.1985 52.7953C92.2277 52.7662 92.2577 52.7368 92.2881 52.7069C92.5522 52.4478 92.8452 52.1602 92.8452 51.8022V34.0134C92.8452 32.8536 91.905 31.9135 90.7452 31.9135H82.9797Z" fill="url(#paint3_linear_163_339510)"/>
<mask id="mask0_163_339510" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="48" y="11" width="77" height="77">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8622 87.8816C107.859 87.8816 124.881 70.8599 124.881 49.8625C124.881 28.8652 107.859 11.8438 86.8622 11.8438C65.865 11.8438 48.8433 28.8652 48.8433 49.8625C48.8433 70.8599 65.865 87.8816 86.8622 87.8816ZM82.9797 31.9144C81.8199 31.9144 80.8797 32.8546 80.8797 34.0144V42.0507C80.8797 42.4109 81.1754 42.7001 81.4422 42.9611C81.4752 42.9934 81.5078 43.0253 81.5393 43.0568L82.7929 44.3105C83.1574 44.675 83.1574 45.2659 82.7929 45.6304L81.5393 46.8841C81.5078 46.9156 81.4752 46.9475 81.4422 46.9798C81.1754 47.2408 80.8797 47.53 80.8797 47.8901V65.711C80.8797 66.8707 81.8199 67.8109 82.9797 67.8109H90.7452C91.905 67.8109 92.8452 66.8707 92.8452 65.7109V57.6167C92.8452 57.2587 92.5522 56.9711 92.2881 56.712C92.2577 56.6821 92.2277 56.6527 92.1985 56.6236L90.9449 55.3699C90.5804 55.0054 90.5804 54.4145 90.9449 54.05L92.1985 52.7963C92.2277 52.7672 92.2577 52.7377 92.2881 52.7079C92.5522 52.4488 92.8452 52.1612 92.8452 51.8032V34.0144C92.8452 32.8546 91.905 31.9144 90.7452 31.9144H82.9797Z" fill="url(#paint4_linear_163_339510)"/>
</mask>
<g mask="url(#mask0_163_339510)">
<g filter="url(#filter4_ddi_163_339510)">
<circle cx="86.8627" cy="49.8632" r="25.4267" fill="url(#paint5_radial_163_339510)"/>
<circle cx="86.8627" cy="49.8632" r="25.4267" fill="url(#paint6_linear_163_339510)"/>
</g>
<g filter="url(#filter5_dii_163_339510)">
<ellipse cx="86.8633" cy="49.8623" rx="12.751" ry="12.751" fill="black" fill-opacity="0.03"/>
</g>
</g>
<circle cx="86.8622" cy="49.8617" r="41.8793" fill="black" fill-opacity="0.01" stroke="black" stroke-opacity="0.3" stroke-width="2.98333"/>
<g style="mix-blend-mode:multiply" filter="url(#filter6_dd_163_339510)">
<circle cx="86.8622" cy="49.8617" r="41.8793" fill="white"/>
</g>
<ellipse cx="86.8632" cy="49.8618" rx="34.4008" ry="34.4008" fill="black" fill-opacity="0.01" stroke="black" stroke-opacity="0.2" stroke-width="2.98333"/>
<g filter="url(#filter7_iii_163_339510)">
<ellipse cx="86.8632" cy="49.8618" rx="34.4008" ry="34.4008" fill="black" fill-opacity="0.01"/>
</g>
<mask id="mask1_163_339510" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="44" y="7" width="85" height="85">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8622 91.741C109.991 91.741 128.741 72.991 128.741 49.8617C128.741 26.7324 109.991 7.98242 86.8622 7.98242C63.7329 7.98242 44.9829 26.7324 44.9829 49.8617C44.9829 72.991 63.7329 91.741 86.8622 91.741ZM86.8624 84.2628C105.862 84.2628 121.263 68.861 121.263 49.862C121.263 30.8629 105.862 15.4611 86.8624 15.4611C67.8634 15.4611 52.4616 30.8629 52.4616 49.862C52.4616 68.861 67.8634 84.2628 86.8624 84.2628Z" fill="white"/>
</mask>
<g mask="url(#mask1_163_339510)">
<ellipse cx="86.8623" cy="49.8613" rx="42.1132" ry="42.1132" fill="url(#paint7_radial_163_339510)"/>
<g opacity="0.7">
<g filter="url(#filter8_iiiiiii_163_339510)">
<ellipse cx="86.8628" cy="49.8632" rx="42.1132" ry="42.1132" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter9_i_163_339510)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8628 91.9765C110.121 91.9765 128.976 73.1218 128.976 49.8632C128.976 26.6047 110.121 7.75 86.8628 7.75C63.6043 7.75 44.7495 26.6047 44.7495 49.8632C44.7495 73.1218 63.6043 91.9765 86.8628 91.9765ZM86.8626 83.319C105.34 83.319 120.319 68.3399 120.319 49.8624C120.319 31.3848 105.34 16.4057 86.8626 16.4057C68.385 16.4057 53.406 31.3848 53.406 49.8624C53.406 68.3399 68.385 83.319 86.8626 83.319Z" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter10_dd_163_339510)">
<circle cx="86.8624" cy="49.8619" r="33.4566" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter11_f_163_339510)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M107.143 79.7442C116.922 73.1965 123.361 62.0474 123.361 49.3946C123.361 29.2373 107.02 12.8965 86.8624 12.8965C66.705 12.8965 50.3643 29.2373 50.3643 49.3946C50.3643 58.7678 53.8975 67.3157 59.7049 73.7792C54.6044 67.6857 51.534 59.8349 51.534 51.267C51.534 31.8849 67.2463 16.1726 86.6284 16.1726C106.01 16.1726 121.723 31.8849 121.723 51.267C121.723 62.9902 115.974 73.3709 107.143 79.7442Z" fill="black" fill-opacity="0.07"/>
</g>
</g>
</g>
<mask id="mask2_163_339510" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="80" y="31" width="13" height="37">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8799 34.0141C80.8799 32.8543 81.8201 31.9141 82.9799 31.9141H90.7454C91.9052 31.9141 92.8454 32.8543 92.8454 34.0141V51.8028C92.8454 52.202 92.481 52.5137 92.1987 52.796V52.796L90.945 54.0496C90.5806 54.4141 90.5806 55.0051 90.945 55.3695L92.1987 56.6232V56.6232C92.481 56.9055 92.8454 57.2171 92.8454 57.6163V65.7106C92.8454 66.8704 91.9052 67.8106 90.7454 67.8106H82.9799C81.8201 67.8106 80.8799 66.8704 80.8799 65.7106V47.8898C80.8799 47.4851 81.2533 47.1699 81.5394 46.8837V46.8837L82.7931 45.63C83.1576 45.2656 83.1576 44.6746 82.7931 44.3101L81.5394 43.0565V43.0565C81.2533 42.7703 80.8799 42.4551 80.8799 42.0504V34.0141Z" fill="black"/>
</mask>
<g mask="url(#mask2_163_339510)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8799 34.0141C80.8799 32.8543 81.8201 31.9141 82.9799 31.9141H90.7454C91.9052 31.9141 92.8454 32.8543 92.8454 34.0141V51.8028C92.8454 52.202 92.481 52.5137 92.1987 52.796V52.796L90.945 54.0496C90.5806 54.4141 90.5806 55.0051 90.945 55.3695L92.1987 56.6232V56.6232C92.481 56.9055 92.8454 57.2171 92.8454 57.6163V65.7106C92.8454 66.8704 91.9052 67.8106 90.7454 67.8106H82.9799C81.8201 67.8106 80.8799 66.8704 80.8799 65.7106V47.8898C80.8799 47.4851 81.2533 47.1699 81.5394 46.8837V46.8837L82.7931 45.63C83.1576 45.2656 83.1576 44.6746 82.7931 44.3101L81.5394 43.0565V43.0565C81.2533 42.7703 80.8799 42.4551 80.8799 42.0504V34.0141Z" fill="#808080" fill-opacity="0.7"/>
<g opacity="0.8" filter="url(#filter12_i_163_339510)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8799 34.0141C80.8799 32.8543 81.8201 31.9141 82.9799 31.9141H90.7454C91.9052 31.9141 92.8454 32.8543 92.8454 34.0141V51.8028C92.8454 52.202 92.481 52.5137 92.1987 52.796V52.796L90.945 54.0496C90.5806 54.4141 90.5806 55.0051 90.945 55.3695L92.1987 56.6232V56.6232C92.481 56.9055 92.8454 57.2171 92.8454 57.6163V65.7106C92.8454 66.8704 91.9052 67.8106 90.7454 67.8106H82.9799C81.8201 67.8106 80.8799 66.8704 80.8799 65.7106V47.8898C80.8799 47.4851 81.2533 47.1699 81.5394 46.8837V46.8837L82.7931 45.63C83.1576 45.2656 83.1576 44.6746 82.7931 44.3101L81.5394 43.0565V43.0565C81.2533 42.7703 80.8799 42.4551 80.8799 42.0504V34.0141Z" fill="black" fill-opacity="0.01"/>
</g>
<path d="M90.945 54.0496L91.275 54.3796L91.275 54.3796L90.945 54.0496ZM90.945 55.3695L90.6151 55.6995L90.6151 55.6995L90.945 55.3695ZM82.7931 45.63L83.1231 45.96L83.1231 45.96L82.7931 45.63ZM82.7931 44.3101L83.1231 43.9801L83.1231 43.9801L82.7931 44.3101ZM82.9799 31.4474C81.5623 31.4474 80.4132 32.5965 80.4132 34.0141H81.3465C81.3465 33.112 82.0778 32.3807 82.9799 32.3807V31.4474ZM90.7454 31.4474H82.9799V32.3807H90.7454V31.4474ZM93.3121 34.0141C93.3121 32.5965 92.1629 31.4474 90.7454 31.4474V32.3807C91.6475 32.3807 92.3787 33.112 92.3787 34.0141H93.3121ZM93.3121 51.8028V34.0141H92.3787V51.8028H93.3121ZM91.275 54.3796L92.5287 53.1259L91.8687 52.466L90.6151 53.7196L91.275 54.3796ZM91.275 55.0396C91.0928 54.8573 91.0928 54.5618 91.275 54.3796L90.6151 53.7196C90.0683 54.2664 90.0683 55.1528 90.6151 55.6995L91.275 55.0396ZM92.5287 56.2932L91.275 55.0396L90.6151 55.6995L91.8687 56.9532L92.5287 56.2932ZM93.3121 65.7106V57.6163H92.3787V65.7106H93.3121ZM90.7454 68.2772C92.1629 68.2772 93.3121 67.1281 93.3121 65.7106H92.3787C92.3787 66.6126 91.6475 67.3439 90.7454 67.3439V68.2772ZM82.9799 68.2772H90.7454V67.3439H82.9799V68.2772ZM80.4132 65.7106C80.4132 67.1281 81.5623 68.2772 82.9799 68.2772V67.3439C82.0778 67.3439 81.3465 66.6127 81.3465 65.7106H80.4132ZM80.4132 47.8898V65.7106H81.3465V47.8898H80.4132ZM82.4631 45.3001L81.2095 46.5537L81.8694 47.2137L83.1231 45.96L82.4631 45.3001ZM82.4631 44.6401C82.6454 44.8223 82.6454 45.1178 82.4631 45.3001L83.1231 45.96C83.6698 45.4133 83.6698 44.5269 83.1231 43.9801L82.4631 44.6401ZM81.2095 43.3864L82.4631 44.6401L83.1231 43.9801L81.8694 42.7265L81.2095 43.3864ZM80.4132 34.0141V42.0504H81.3465V34.0141H80.4132ZM81.8694 42.7265C81.7044 42.5615 81.5758 42.4458 81.47 42.3104C81.37 42.1825 81.3465 42.1032 81.3465 42.0504H80.4132C80.4132 42.4023 80.5765 42.6829 80.7347 42.8853C80.8871 43.0802 81.0883 43.2653 81.2095 43.3864L81.8694 42.7265ZM81.3465 47.8898C81.3465 47.837 81.37 47.7576 81.47 47.6297C81.5758 47.4943 81.7044 47.3787 81.8694 47.2137L81.2095 46.5537C81.0883 46.6749 80.8871 46.8599 80.7347 47.0549C80.5765 47.2572 80.4132 47.5379 80.4132 47.8898H81.3465ZM91.8687 56.9532C92.0303 57.1147 92.1565 57.2294 92.259 57.3618C92.3561 57.4871 92.3787 57.5647 92.3787 57.6163H93.3121C93.3121 57.2687 93.1525 56.991 92.9968 56.7901C92.8465 56.5962 92.6494 56.4139 92.5287 56.2932L91.8687 56.9532ZM92.3787 51.8028C92.3787 51.8544 92.3561 51.9321 92.259 52.0574C92.1565 52.1897 92.0303 52.3044 91.8687 52.466L92.5287 53.1259C92.6494 53.0052 92.8465 52.823 92.9968 52.6291C93.1524 52.4282 93.3121 52.1504 93.3121 51.8028H92.3787Z" fill="black" fill-opacity="0.5"/>
</g>
</g>
<defs>
<filter id="filter0_i_163_339510" x="11.459" y="30.2617" width="53.0732" height="54.0732" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339510"/>
</filter>
<filter id="filter1_f_163_339510" x="18.5674" y="68.3701" width="9.26074" height="8.33008" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_163_339510"/>
</filter>
<filter id="filter2_f_163_339510" x="10.9541" y="63.2393" width="26.5039" height="26.5039" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_163_339510"/>
</filter>
<filter id="filter3_i_163_339510" x="41.2246" y="67.001" width="22.9619" height="7.20215" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.921569 0 0 0 0 0.443137 0 0 0 0 0 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339510"/>
</filter>
<filter id="filter4_ddi_163_339510" x="59.1985" y="22.9449" width="55.3285" height="56.0743" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.49166"/>
<feGaussianBlur stdDeviation="1.11875"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.49166" operator="dilate" in="SourceAlpha" result="effect2_dropShadow_163_339510"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_163_339510" result="effect2_dropShadow_163_339510"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_163_339510" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feGaussianBlur stdDeviation="0.466665"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_163_339510"/>
</filter>
<filter id="filter5_dii_163_339510" x="73.6456" y="36.878" width="26.4353" height="29.4686" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.233333" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_163_339510"/>
<feOffset dy="0.233333"/>
<feGaussianBlur stdDeviation="0.116666"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.34902 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_163_339510"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_163_339510" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.73332"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.466665"/>
<feGaussianBlur stdDeviation="0.699998"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_163_339510" result="effect3_innerShadow_163_339510"/>
</filter>
<filter id="filter6_dd_163_339510" x="41.9996" y="5.74493" width="89.7254" height="89.7254" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.745831"/>
<feGaussianBlur stdDeviation="1.49166"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.372916"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_163_339510" result="effect2_dropShadow_163_339510"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_163_339510" result="shape"/>
</filter>
<filter id="filter7_iii_163_339510" x="52.4624" y="15.4609" width="68.8018" height="72.5351" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.73332"/>
<feGaussianBlur stdDeviation="4.66665"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feGaussianBlur stdDeviation="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_163_339510" result="effect2_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_163_339510" result="effect3_innerShadow_163_339510"/>
</filter>
<filter id="filter8_iiiiiii_163_339510" x="44.7495" y="7.75" width="84.2266" height="86.3266" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="5.36665" operator="erode" in="SourceAlpha" result="effect1_innerShadow_163_339510"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4.89999" operator="erode" in="SourceAlpha" result="effect2_innerShadow_163_339510"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_163_339510" result="effect2_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3.03332" operator="erode" in="SourceAlpha" result="effect3_innerShadow_163_339510"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_163_339510" result="effect3_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.09999"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_163_339510" result="effect4_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.63333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_163_339510" result="effect5_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.09999"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0196078 0 0 0 0 0.447059 0 0 0 0 0.92549 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect5_innerShadow_163_339510" result="effect6_innerShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.49999"/>
<feGaussianBlur stdDeviation="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect6_innerShadow_163_339510" result="effect7_innerShadow_163_339510"/>
</filter>
<filter id="filter9_i_163_339510" x="44.7495" y="6.11667" width="84.2266" height="85.8599" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.63333"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339510"/>
</filter>
<filter id="filter10_dd_163_339510" x="52.7058" y="15.7053" width="68.3131" height="68.5464" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.699998" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_163_339510"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_163_339510"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.466665"/>
<feGaussianBlur stdDeviation="0.233333"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_163_339510" result="effect2_dropShadow_163_339510"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_163_339510" result="shape"/>
</filter>
<filter id="filter11_f_163_339510" x="49.8976" y="12.4298" width="73.9294" height="67.781" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.233333" result="effect1_foregroundBlur_163_339510"/>
</filter>
<filter id="filter12_i_163_339510" x="80.8799" y="31.9141" width="11.9653" height="43.3631" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.46665"/>
<feGaussianBlur stdDeviation="10.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339510"/>
</filter>
<linearGradient id="paint0_linear_163_339510" x1="158.225" y1="57.1566" x2="135.947" y2="68.9792" gradientUnits="userSpaceOnUse">
<stop offset="0.18" stop-color="#0052CC"/>
<stop offset="1" stop-color="#2684FF"/>
</linearGradient>
<linearGradient id="paint1_linear_163_339510" x1="7.49782" y1="6.94217" x2="31.45" y2="144.276" gradientUnits="userSpaceOnUse">
<stop stop-color="#B9BDC2"/>
<stop offset="0.9999" stop-color="#848D96"/>
</linearGradient>
<linearGradient id="paint2_linear_163_339510" x1="32.362" y1="113.996" x2="24.7987" y2="64.5816" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFAA05"/>
<stop offset="1" stop-color="#FFD480"/>
</linearGradient>
<linearGradient id="paint3_linear_163_339510" x1="48.8433" y1="11.8428" x2="48.8433" y2="87.8806" gradientUnits="userSpaceOnUse">
<stop stop-color="#0077FF" stop-opacity="0.998825"/>
<stop offset="1" stop-color="#00B9F9"/>
</linearGradient>
<linearGradient id="paint4_linear_163_339510" x1="48.8433" y1="11.8437" x2="48.8433" y2="87.8816" gradientUnits="userSpaceOnUse">
<stop stop-color="#0077FF" stop-opacity="0.998825"/>
<stop offset="1" stop-color="#00B9F9"/>
</linearGradient>
<radialGradient id="paint5_radial_163_339510" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.8627 49.8632) rotate(90) scale(25.4267)">
<stop offset="0.720183" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.9"/>
</radialGradient>
<linearGradient id="paint6_linear_163_339510" x1="61.436" y1="24.4365" x2="61.436" y2="75.2899" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.1"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint7_radial_163_339510" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.8623 49.8613) rotate(90) scale(42.1132 42.1132)">
<stop offset="0.86112" stop-color="white" stop-opacity="0.75"/>
<stop offset="0.907821" stop-color="white"/>
</radialGradient>
<clipPath id="clip0_163_339510">
<rect width="64" height="64" fill="white" transform="translate(0.11377 31.999) rotate(-10)"/>
</clipPath>
<clipPath id="clip1_163_339510">
<rect width="95.7241" height="95.7241" fill="white" transform="translate(39.0005 2)"/>
</clipPath>
</defs>
</svg>
