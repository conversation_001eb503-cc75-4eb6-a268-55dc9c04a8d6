<svg width="175" height="100" viewBox="0 0 175 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M169.757 43.4556C170.899 36.9832 166.577 30.8111 160.105 29.6698L124.455 23.3838C117.982 22.2426 111.81 26.5643 110.669 33.0367L104.383 68.6865C103.242 75.1589 107.563 81.331 114.036 82.4723L149.686 88.7583C156.158 89.8996 162.33 85.5778 163.471 79.1054L169.757 43.4556Z" fill="white"/>
<path d="M160.018 30.1622C166.218 31.2556 170.358 37.1683 169.265 43.3688L162.979 79.0186C161.886 85.2191 155.973 89.3592 149.772 88.2659L114.123 81.9799C107.922 80.8866 103.782 74.9738 104.875 68.7734L111.161 33.1235C112.255 26.9231 118.167 22.7829 124.368 23.8762L160.018 30.1622Z" stroke="black" stroke-opacity="0.12"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M133.599 75.7669L134.945 68.1329C143.029 69.5584 150.718 62.6517 149.113 53.5994C148.818 51.9364 148.164 50.3579 147.194 48.9744C146.225 47.5909 144.965 46.4358 143.503 45.5901C135.536 41.0095 126.417 45.8455 124.992 53.926L117.376 52.5831C119.647 39.7045 133.881 31.857 146.648 38.4575C152.218 41.351 156.103 46.8685 156.896 53.0855C158.732 67.3555 146.508 78.0431 133.599 75.7669Z" fill="#0080FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M127.37 66.8199L134.963 68.1588L136.301 60.5698L128.709 59.2311L127.37 66.8199ZM120.488 71.6364L126.338 72.668L127.37 66.8199L121.519 65.789L120.488 71.6364ZM116.627 64.9265L121.519 65.789L122.381 60.8993L117.489 60.0368L116.627 64.9265Z" fill="#0080FF"/>
<rect width="64" height="64" transform="translate(0.11377 31.999) rotate(-10)" fill="black" fill-opacity="0.01"/>
<g clip-path="url(#clip0_163_339509)">
<path d="M6.13687 43.1225C5.17784 37.6835 8.80952 32.497 14.2485 31.5379L51.6712 24.9393C57.1101 23.9803 62.2967 27.612 63.2557 33.0509L69.8544 70.4736C70.8134 75.9125 67.1817 81.0991 61.7428 82.0581L24.3201 88.6568C18.8811 89.6158 13.6945 85.9841 12.7355 80.5452L6.13687 43.1225Z" fill="url(#paint0_linear_163_339509)"/>
<path d="M6.62928 43.0357C5.7182 37.8687 9.1683 32.9414 14.3353 32.0303L51.758 25.4317C56.925 24.5206 61.8522 27.9707 62.7633 33.1377L69.3619 70.5604C70.273 75.7274 66.8229 80.6547 61.6559 81.5657L24.2332 88.1644C19.0662 89.0755 14.139 85.6254 13.2279 80.4584L6.62928 43.0357Z" stroke="black" stroke-opacity="0.06"/>
<g filter="url(#filter0_i_163_339509)">
<rect x="10.1929" y="37.3301" width="48" height="48" rx="8" transform="rotate(-10 10.1929 37.3301)" fill="black" fill-opacity="0.45"/>
</g>
<rect x="10.7721" y="37.7357" width="47" height="47" rx="7.5" transform="rotate(-10 10.7721 37.7357)" stroke="#35414C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.3539 41.6019C20.7091 40.5776 21.7792 40.077 22.744 40.4839L32.9303 44.7801C33.5439 45.0389 33.994 45.6206 34.116 46.3124C34.238 47.0043 34.014 47.7048 33.5259 48.1579L25.4233 55.6789C24.6558 56.3913 23.4791 56.2869 22.7949 55.4458C22.1108 54.6047 22.1783 53.3454 22.9458 52.633L28.7324 47.2617L21.4577 44.1935C20.4928 43.7866 19.9987 42.6263 20.3539 41.6019Z" fill="#F8F8F9"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.8219 55.8134C37.6301 54.7256 38.3564 53.6883 39.4442 53.4965L51.2619 51.4127C52.3497 51.2209 53.387 51.9472 53.5788 53.035C53.7706 54.1228 53.0443 55.1601 51.9565 55.3519L40.1388 57.4357C39.051 57.6275 38.0137 56.9012 37.8219 55.8134Z" fill="#F8F8F9"/>
<g opacity="0.3" filter="url(#filter1_f_163_339509)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.4013 74.708C26.7701 74.017 26.919 73.202 26.7724 72.3707C26.4243 70.3965 24.5416 69.0782 22.5674 69.4263C20.5931 69.7744 19.2749 71.6571 19.623 73.6313C19.7695 74.4623 20.1878 75.177 20.7704 75.7002C20.6688 74.2509 21.6768 72.9296 23.1393 72.6717C24.6021 72.4138 26.0013 73.311 26.4013 74.708Z" fill="black"/>
</g>
<path opacity="0.3" d="M59.716 76.323L46.9135 78.5804L48.8994 75.184L55.8825 73.9526L59.716 76.323Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6369 90.2789C31.7405 89.379 35.7074 85.8204 37.3645 81.2786L47.3095 79.5258L50.158 75.4689L56.4781 74.3545L60.3321 77.0078C60.3321 77.0078 64.7403 75.2029 64.654 69.7748L57.4983 64.6911L35.625 68.3863C32.591 64.118 27.275 61.7344 21.7748 62.7043C14.1602 64.0469 9.07589 71.3081 10.4185 78.9227C11.7612 86.5372 19.0224 91.6215 26.6369 90.2789ZM24.2058 76.4916C25.8375 76.2039 26.927 74.6479 26.6393 73.0162C26.3516 71.3845 24.7956 70.295 23.164 70.5827C21.5323 70.8704 20.4428 72.4264 20.7305 74.0581C21.0182 75.6898 22.5742 76.7793 24.2058 76.4916Z" fill="url(#paint1_linear_163_339509)"/>
<g filter="url(#filter2_f_163_339509)">
<ellipse cx="24.2059" cy="76.4911" rx="11" ry="11" transform="rotate(-10 24.2059 76.4911)" stroke="#EB7100" stroke-width="0.5"/>
</g>
<path opacity="0.3" d="M34.054 74.7546C35.013 80.1936 31.3813 85.3802 25.9424 86.3392C20.5034 87.2982 15.3169 83.6665 14.3578 78.2276C13.3988 72.7887 17.0305 67.6021 22.4694 66.643C27.9084 65.684 33.0949 69.3157 34.054 74.7546Z" stroke="white" stroke-width="2"/>
<path d="M34.7926 74.6244C35.8235 80.4713 31.9195 86.0468 26.0726 87.0778C20.2258 88.1088 14.6502 84.2047 13.6192 78.3578C12.5883 72.511 16.4923 66.9354 22.3392 65.9044C28.186 64.8735 33.7616 68.7775 34.7926 74.6244Z" stroke="#EB7100" stroke-width="0.5"/>
<g filter="url(#filter3_i_163_339509)">
<path d="M42.985 73.18L64.1863 69.4416L60.7532 67.0007L42.4641 70.2255C41.6482 70.3694 41.1035 71.1474 41.2473 71.9632C41.3912 72.7791 42.1692 73.3238 42.985 73.18Z" fill="#FFAA05"/>
</g>
</g>
<g clip-path="url(#clip1_163_339509)">
<rect width="95.7241" height="95.7241" transform="translate(39 2)" fill="black" fill-opacity="0.01"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8617 87.8806C107.859 87.8806 124.881 70.8589 124.881 49.8616C124.881 28.8642 107.859 11.8428 86.8617 11.8428C65.8645 11.8428 48.8428 28.8642 48.8428 49.8616C48.8428 70.8589 65.8645 87.8806 86.8617 87.8806ZM82.9785 31.9135C81.8187 31.9135 80.8785 32.8537 80.8785 34.0134V42.0483C80.8785 42.4087 81.1745 42.6981 81.4415 42.9593C81.4749 42.9919 81.5077 43.024 81.5395 43.0558L82.7932 44.3095C83.1577 44.674 83.1577 45.2649 82.7932 45.6294L81.5395 46.8831C81.5077 46.9149 81.4749 46.947 81.4415 46.9796C81.1745 47.2408 80.8785 47.5302 80.8785 47.8906V65.71C80.8785 66.8698 81.8187 67.81 82.9785 67.81H90.744C91.9038 67.81 92.844 66.8698 92.844 65.71V57.615C92.844 57.2571 92.5511 56.9696 92.2872 56.7106C92.2569 56.6809 92.227 56.6516 92.1981 56.6226L90.9444 55.3689C90.5799 55.0044 90.5799 54.4135 90.9444 54.049L92.1981 52.7953C92.227 52.7664 92.2569 52.737 92.2871 52.7074C92.5511 52.4483 92.844 52.1608 92.844 51.803V34.0134C92.844 32.8536 91.9038 31.9135 90.744 31.9135H82.9785Z" fill="url(#paint2_linear_163_339509)"/>
<mask id="mask0_163_339509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="48" y="11" width="77" height="77">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8627 87.8816C107.86 87.8816 124.882 70.8599 124.882 49.8625C124.882 28.8652 107.86 11.8438 86.8627 11.8438C65.8655 11.8438 48.8438 28.8652 48.8438 49.8625C48.8438 70.8599 65.8655 87.8816 86.8627 87.8816ZM82.9795 31.9144C81.8197 31.9144 80.8795 32.8546 80.8795 34.0144V42.0493C80.8795 42.4097 81.1754 42.6991 81.4425 42.9603C81.4758 42.9929 81.5087 43.025 81.5405 43.0568L82.7942 44.3105C83.1586 44.675 83.1586 45.2659 82.7942 45.6304L81.5405 46.8841C81.5087 46.9159 81.4758 46.948 81.4425 46.9806C81.1754 47.2418 80.8795 47.5312 80.8795 47.8916V65.711C80.8795 66.8707 81.8197 67.8109 82.9795 67.8109H90.745C91.9048 67.8109 92.845 66.8707 92.845 65.7109V57.616C92.845 57.2581 92.5521 56.9706 92.2881 56.7116C92.2579 56.6819 92.228 56.6526 92.199 56.6236L90.9454 55.3699C90.5809 55.0054 90.5809 54.4145 90.9454 54.05L92.199 52.7963C92.228 52.7673 92.2579 52.738 92.2881 52.7083C92.5521 52.4493 92.845 52.1618 92.845 51.8039V34.0144C92.845 32.8546 91.9048 31.9144 90.745 31.9144H82.9795Z" fill="url(#paint3_linear_163_339509)"/>
</mask>
<g mask="url(#mask0_163_339509)">
<g filter="url(#filter4_ddi_163_339509)">
<circle cx="86.8623" cy="49.8632" r="25.4267" fill="url(#paint4_radial_163_339509)"/>
<circle cx="86.8623" cy="49.8632" r="25.4267" fill="url(#paint5_linear_163_339509)"/>
</g>
<g filter="url(#filter5_dii_163_339509)">
<ellipse cx="86.8623" cy="49.8623" rx="12.751" ry="12.751" fill="black" fill-opacity="0.03"/>
</g>
</g>
<circle cx="86.8617" cy="49.8617" r="41.8793" fill="black" fill-opacity="0.01" stroke="black" stroke-opacity="0.3" stroke-width="2.98333"/>
<g style="mix-blend-mode:multiply" filter="url(#filter6_dd_163_339509)">
<circle cx="86.8617" cy="49.8617" r="41.8793" fill="white"/>
</g>
<ellipse cx="86.8618" cy="49.8618" rx="34.4008" ry="34.4008" fill="black" fill-opacity="0.01" stroke="black" stroke-opacity="0.2" stroke-width="2.98333"/>
<g filter="url(#filter7_iii_163_339509)">
<ellipse cx="86.8618" cy="49.8618" rx="34.4008" ry="34.4008" fill="black" fill-opacity="0.01"/>
</g>
<mask id="mask1_163_339509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="44" y="7" width="85" height="85">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8617 91.741C109.991 91.741 128.741 72.991 128.741 49.8617C128.741 26.7324 109.991 7.98242 86.8617 7.98242C63.7324 7.98242 44.9824 26.7324 44.9824 49.8617C44.9824 72.991 63.7324 91.741 86.8617 91.741ZM86.862 84.2628C105.861 84.2628 121.263 68.861 121.263 49.862C121.263 30.8629 105.861 15.4611 86.862 15.4611C67.8629 15.4611 52.4611 30.8629 52.4611 49.862C52.4611 68.861 67.8629 84.2628 86.862 84.2628Z" fill="white"/>
</mask>
<g mask="url(#mask1_163_339509)">
<ellipse cx="86.8623" cy="49.8613" rx="42.1132" ry="42.1132" fill="url(#paint6_radial_163_339509)"/>
<g opacity="0.7">
<g filter="url(#filter8_iiiiiii_163_339509)">
<ellipse cx="86.8632" cy="49.8632" rx="42.1132" ry="42.1132" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter9_i_163_339509)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M86.8632 91.9765C110.122 91.9765 128.976 73.1218 128.976 49.8632C128.976 26.6047 110.122 7.75 86.8632 7.75C63.6047 7.75 44.75 26.6047 44.75 49.8632C44.75 73.1218 63.6047 91.9765 86.8632 91.9765ZM86.8624 83.319C105.34 83.319 120.319 68.3399 120.319 49.8624C120.319 31.3848 105.34 16.4057 86.8624 16.4057C68.3848 16.4057 53.4057 31.3848 53.4057 49.8624C53.4057 68.3399 68.3848 83.319 86.8624 83.319Z" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter10_dd_163_339509)">
<circle cx="86.8619" cy="49.8619" r="33.4566" fill="black" fill-opacity="0.01"/>
</g>
<g filter="url(#filter11_f_163_339509)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M107.143 79.7442C116.922 73.1965 123.361 62.0474 123.361 49.3946C123.361 29.2373 107.02 12.8965 86.8624 12.8965C66.705 12.8965 50.3643 29.2373 50.3643 49.3946C50.3643 58.7678 53.8975 67.3157 59.7049 73.7792C54.6044 67.6857 51.534 59.8349 51.534 51.267C51.534 31.8849 67.2463 16.1726 86.6284 16.1726C106.01 16.1726 121.723 31.8849 121.723 51.267C121.723 62.9902 115.974 73.3709 107.143 79.7442Z" fill="black" fill-opacity="0.07"/>
</g>
</g>
</g>
<mask id="mask2_163_339509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="80" y="31" width="13" height="37">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8789 34.0141C80.8789 32.8543 81.8191 31.9141 82.9789 31.9141H90.7444C91.9042 31.9141 92.8444 32.8543 92.8444 34.0141V51.8036C92.8444 52.2025 92.4805 52.5139 92.1985 52.796V52.796L90.9448 54.0496C90.5803 54.4141 90.5803 55.0051 90.9448 55.3695L92.1985 56.6232V56.6232C92.4805 56.9053 92.8444 57.2167 92.8444 57.6156V65.7106C92.8444 66.8704 91.9042 67.8106 90.7444 67.8106H82.9789C81.8191 67.8106 80.8789 66.8704 80.8789 65.7106V47.8912C80.8789 47.4859 81.2533 47.1703 81.5399 46.8837V46.8837L82.7936 45.63C83.1581 45.2656 83.1581 44.6746 82.7936 44.3101L81.5399 43.0565V43.0565C81.2533 42.7698 80.8789 42.4543 80.8789 42.0489V34.0141Z" fill="black"/>
</mask>
<g mask="url(#mask2_163_339509)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8789 34.0141C80.8789 32.8543 81.8191 31.9141 82.9789 31.9141H90.7444C91.9042 31.9141 92.8444 32.8543 92.8444 34.0141V51.8036C92.8444 52.2025 92.4805 52.5139 92.1985 52.796V52.796L90.9448 54.0496C90.5803 54.4141 90.5803 55.0051 90.9448 55.3695L92.1985 56.6232V56.6232C92.4805 56.9053 92.8444 57.2167 92.8444 57.6156V65.7106C92.8444 66.8704 91.9042 67.8106 90.7444 67.8106H82.9789C81.8191 67.8106 80.8789 66.8704 80.8789 65.7106V47.8912C80.8789 47.4859 81.2533 47.1703 81.5399 46.8837V46.8837L82.7936 45.63C83.1581 45.2656 83.1581 44.6746 82.7936 44.3101L81.5399 43.0565V43.0565C81.2533 42.7698 80.8789 42.4543 80.8789 42.0489V34.0141Z" fill="#808080" fill-opacity="0.7"/>
<g opacity="0.8" filter="url(#filter12_i_163_339509)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8789 34.0141C80.8789 32.8543 81.8191 31.9141 82.9789 31.9141H90.7444C91.9042 31.9141 92.8444 32.8543 92.8444 34.0141V51.8036C92.8444 52.2025 92.4805 52.5139 92.1985 52.796V52.796L90.9448 54.0496C90.5803 54.4141 90.5803 55.0051 90.9448 55.3695L92.1985 56.6232V56.6232C92.4805 56.9053 92.8444 57.2167 92.8444 57.6156V65.7106C92.8444 66.8704 91.9042 67.8106 90.7444 67.8106H82.9789C81.8191 67.8106 80.8789 66.8704 80.8789 65.7106V47.8912C80.8789 47.4859 81.2533 47.1703 81.5399 46.8837V46.8837L82.7936 45.63C83.1581 45.2656 83.1581 44.6746 82.7936 44.3101L81.5399 43.0565V43.0565C81.2533 42.7698 80.8789 42.4543 80.8789 42.0489V34.0141Z" fill="black" fill-opacity="0.01"/>
</g>
<path d="M90.9448 54.0496L91.2748 54.3796L91.2748 54.3796L90.9448 54.0496ZM90.9448 55.3695L90.6148 55.6995L90.6148 55.6995L90.9448 55.3695ZM82.7936 45.63L83.1236 45.96L83.1236 45.96L82.7936 45.63ZM82.7936 44.3101L83.1236 43.9801L83.1236 43.9801L82.7936 44.3101ZM82.9789 31.4474C81.5614 31.4474 80.4122 32.5965 80.4122 34.0141H81.3456C81.3456 33.112 82.0768 32.3807 82.9789 32.3807V31.4474ZM90.7444 31.4474H82.9789V32.3807H90.7444V31.4474ZM93.3111 34.0141C93.3111 32.5965 92.1619 31.4474 90.7444 31.4474V32.3807C91.6465 32.3807 92.3777 33.112 92.3777 34.0141H93.3111ZM93.3111 51.8036V34.0141H92.3777V51.8036H93.3111ZM91.2748 54.3796L92.5284 53.1259L91.8685 52.466L90.6148 53.7196L91.2748 54.3796ZM91.2748 55.0396C91.0925 54.8573 91.0925 54.5618 91.2748 54.3796L90.6148 53.7196C90.0681 54.2664 90.0681 55.1528 90.6148 55.6995L91.2748 55.0396ZM92.5284 56.2932L91.2748 55.0396L90.6148 55.6995L91.8685 56.9532L92.5284 56.2932ZM93.3111 65.7106V57.6156H92.3777V65.7106H93.3111ZM90.7444 68.2772C92.1619 68.2772 93.3111 67.1281 93.3111 65.7106H92.3777C92.3777 66.6126 91.6465 67.3439 90.7444 67.3439V68.2772ZM82.9789 68.2772H90.7444V67.3439H82.9789V68.2772ZM80.4122 65.7106C80.4122 67.1281 81.5614 68.2772 82.9789 68.2772V67.3439C82.0768 67.3439 81.3456 66.6127 81.3456 65.7106H80.4122ZM80.4122 47.8912V65.7106H81.3456V47.8912H80.4122ZM82.4636 45.3001L81.2099 46.5537L81.8699 47.2137L83.1236 45.96L82.4636 45.3001ZM82.4636 44.6401C82.6458 44.8223 82.6458 45.1178 82.4636 45.3001L83.1236 45.96C83.6703 45.4133 83.6703 44.5269 83.1236 43.9801L82.4636 44.6401ZM81.2099 43.3864L82.4636 44.6401L83.1236 43.9801L81.8699 42.7265L81.2099 43.3864ZM80.4122 34.0141V42.0489H81.3456V34.0141H80.4122ZM81.8699 42.7265C81.7045 42.5611 81.5757 42.4453 81.4694 42.3096C81.3691 42.1814 81.3456 42.1019 81.3456 42.0489H80.4122C80.4122 42.4013 80.5759 42.6823 80.7344 42.8848C80.8871 43.0798 81.0887 43.2652 81.2099 43.3864L81.8699 42.7265ZM81.3456 47.8912C81.3456 47.8383 81.3691 47.7588 81.4694 47.6305C81.5757 47.4948 81.7045 47.3791 81.8699 47.2137L81.2099 46.5537C81.0887 46.6749 80.8871 46.8603 80.7344 47.0553C80.5759 47.2579 80.4122 47.5388 80.4122 47.8912H81.3456ZM91.8685 56.9532C92.0298 57.1146 92.1559 57.2292 92.2583 57.3613C92.3552 57.4865 92.3777 57.5641 92.3777 57.6156H93.3111C93.3111 57.2682 93.1517 56.9906 92.9961 56.7898C92.846 56.596 92.6491 56.4139 92.5284 56.2932L91.8685 56.9532ZM92.3777 51.8036C92.3777 51.8551 92.3552 51.9327 92.2583 52.0578C92.1559 52.19 92.0298 52.3046 91.8685 52.466L92.5284 53.1259C92.6491 53.0053 92.846 52.8231 92.9961 52.6293C93.1517 52.4285 93.3111 52.1509 93.3111 51.8036H92.3777Z" fill="black" fill-opacity="0.5"/>
</g>
</g>
<defs>
<filter id="filter0_i_163_339509" x="11.459" y="30.2617" width="53.0732" height="54.0732" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339509"/>
</filter>
<filter id="filter1_f_163_339509" x="18.5674" y="68.3701" width="9.26074" height="8.33008" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_163_339509"/>
</filter>
<filter id="filter2_f_163_339509" x="10.9541" y="63.2393" width="26.5039" height="26.5039" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_163_339509"/>
</filter>
<filter id="filter3_i_163_339509" x="41.2246" y="67.001" width="22.9619" height="7.20215" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.921569 0 0 0 0 0.443137 0 0 0 0 0 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339509"/>
</filter>
<filter id="filter4_ddi_163_339509" x="59.1981" y="22.9449" width="55.3285" height="56.0743" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.49166"/>
<feGaussianBlur stdDeviation="1.11875"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.49166" operator="dilate" in="SourceAlpha" result="effect2_dropShadow_163_339509"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_163_339509" result="effect2_dropShadow_163_339509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_163_339509" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feGaussianBlur stdDeviation="0.466665"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_163_339509"/>
</filter>
<filter id="filter5_dii_163_339509" x="73.6447" y="36.878" width="26.4353" height="29.4686" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.233333" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_163_339509"/>
<feOffset dy="0.233333"/>
<feGaussianBlur stdDeviation="0.116666"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.34902 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_163_339509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_163_339509" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.73332"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.466665"/>
<feGaussianBlur stdDeviation="0.699998"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_163_339509" result="effect3_innerShadow_163_339509"/>
</filter>
<filter id="filter6_dd_163_339509" x="41.9991" y="5.74493" width="89.7254" height="89.7254" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.745831"/>
<feGaussianBlur stdDeviation="1.49166"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.372916"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_163_339509" result="effect2_dropShadow_163_339509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_163_339509" result="shape"/>
</filter>
<filter id="filter7_iii_163_339509" x="52.4609" y="15.4609" width="68.8018" height="72.5351" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.73332"/>
<feGaussianBlur stdDeviation="4.66665"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feGaussianBlur stdDeviation="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_163_339509" result="effect2_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_163_339509" result="effect3_innerShadow_163_339509"/>
</filter>
<filter id="filter8_iiiiiii_163_339509" x="44.75" y="7.75" width="84.2266" height="86.3266" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="5.36665" operator="erode" in="SourceAlpha" result="effect1_innerShadow_163_339509"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4.89999" operator="erode" in="SourceAlpha" result="effect2_innerShadow_163_339509"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_163_339509" result="effect2_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3.03332" operator="erode" in="SourceAlpha" result="effect3_innerShadow_163_339509"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_163_339509" result="effect3_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.09999"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_163_339509" result="effect4_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.63333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_163_339509" result="effect5_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.09999"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0196078 0 0 0 0 0.447059 0 0 0 0 0.92549 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect5_innerShadow_163_339509" result="effect6_innerShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.49999"/>
<feGaussianBlur stdDeviation="0.933331"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect6_innerShadow_163_339509" result="effect7_innerShadow_163_339509"/>
</filter>
<filter id="filter9_i_163_339509" x="44.75" y="6.11667" width="84.2266" height="85.8599" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.63333"/>
<feGaussianBlur stdDeviation="1.86666"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339509"/>
</filter>
<filter id="filter10_dd_163_339509" x="52.7053" y="15.7053" width="68.3131" height="68.5464" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.699998" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_163_339509"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_163_339509"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.466665"/>
<feGaussianBlur stdDeviation="0.233333"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_163_339509" result="effect2_dropShadow_163_339509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_163_339509" result="shape"/>
</filter>
<filter id="filter11_f_163_339509" x="49.8976" y="12.4298" width="73.9294" height="67.781" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.233333" result="effect1_foregroundBlur_163_339509"/>
</filter>
<filter id="filter12_i_163_339509" x="80.8789" y="31.9141" width="11.9658" height="43.3631" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.46665"/>
<feGaussianBlur stdDeviation="10.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_163_339509"/>
</filter>
<linearGradient id="paint0_linear_163_339509" x1="7.49782" y1="6.94217" x2="31.45" y2="144.276" gradientUnits="userSpaceOnUse">
<stop stop-color="#B9BDC2"/>
<stop offset="0.9999" stop-color="#848D96"/>
</linearGradient>
<linearGradient id="paint1_linear_163_339509" x1="32.362" y1="113.996" x2="24.7987" y2="64.5816" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFAA05"/>
<stop offset="1" stop-color="#FFD480"/>
</linearGradient>
<linearGradient id="paint2_linear_163_339509" x1="48.8428" y1="11.8428" x2="48.8428" y2="87.8806" gradientUnits="userSpaceOnUse">
<stop stop-color="#0077FF" stop-opacity="0.998825"/>
<stop offset="1" stop-color="#00B9F9"/>
</linearGradient>
<linearGradient id="paint3_linear_163_339509" x1="48.8438" y1="11.8437" x2="48.8438" y2="87.8816" gradientUnits="userSpaceOnUse">
<stop stop-color="#0077FF" stop-opacity="0.998825"/>
<stop offset="1" stop-color="#00B9F9"/>
</linearGradient>
<radialGradient id="paint4_radial_163_339509" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.8623 49.8632) rotate(90) scale(25.4267)">
<stop offset="0.720183" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.9"/>
</radialGradient>
<linearGradient id="paint5_linear_163_339509" x1="61.4355" y1="24.4365" x2="61.4355" y2="75.2899" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.1"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint6_radial_163_339509" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.8623 49.8613) rotate(90) scale(42.1132 42.1132)">
<stop offset="0.86112" stop-color="white" stop-opacity="0.75"/>
<stop offset="0.907821" stop-color="white"/>
</radialGradient>
<clipPath id="clip0_163_339509">
<rect width="64" height="64" fill="white" transform="translate(0.11377 31.999) rotate(-10)"/>
</clipPath>
<clipPath id="clip1_163_339509">
<rect width="95.7241" height="95.7241" fill="white" transform="translate(39 2)"/>
</clipPath>
</defs>
</svg>
