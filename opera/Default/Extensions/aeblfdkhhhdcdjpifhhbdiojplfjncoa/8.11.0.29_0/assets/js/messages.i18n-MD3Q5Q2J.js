var de = function(n, ord
) {
  var s = String(n).split('.'), v0 = !s[1];
  if (ord) return 'other';
  return (n == 1 && v0) ? 'one' : 'other';
};
var en = function(n, ord
) {
  var s = String(n).split('.'), v0 = !s[1], t0 = Number(s[0]) == n,
      n10 = t0 && s[0].slice(-1), n100 = t0 && s[0].slice(-2);
  if (ord) return (n10 == 1 && n100 != 11) ? 'one'
      : (n10 == 2 && n100 != 12) ? 'two'
      : (n10 == 3 && n100 != 13) ? 'few'
      : 'other';
  return (n == 1 && v0) ? 'one' : 'other';
};
var es = function(n, ord
) {
  if (ord) return 'other';
  return (n == 1) ? 'one' : 'other';
};
var fr = function(n, ord
) {
  if (ord) return (n == 1) ? 'one' : 'other';
  return (n >= 0 && n < 2) ? 'one' : 'other';
};
var it = function(n, ord
) {
  var s = String(n).split('.'), v0 = !s[1];
  if (ord) return ((n == 11 || n == 8 || n == 80
          || n == 800)) ? 'many' : 'other';
  return (n == 1 && v0) ? 'one' : 'other';
};
var ja = function(n, ord
) {
  if (ord) return 'other';
  return 'other';
};
var ko = function(n, ord
) {
  if (ord) return 'other';
  return 'other';
};
var nl = function(n, ord
) {
  var s = String(n).split('.'), v0 = !s[1];
  if (ord) return 'other';
  return (n == 1 && v0) ? 'one' : 'other';
};
var pt = function(n, ord
) {
  var s = String(n).split('.'), i = s[0];
  if (ord) return 'other';
  return ((i == 0
          || i == 1)) ? 'one' : 'other';
};
var ru = function(n, ord
) {
  var s = String(n).split('.'), i = s[0], v0 = !s[1], i10 = i.slice(-1),
      i100 = i.slice(-2);
  if (ord) return 'other';
  return (v0 && i10 == 1 && i100 != 11) ? 'one'
      : (v0 && (i10 >= 2 && i10 <= 4) && (i100 < 12
          || i100 > 14)) ? 'few'
      : (v0 && i10 == 0 || v0 && (i10 >= 5 && i10 <= 9)
          || v0 && (i100 >= 11 && i100 <= 14)) ? 'many'
      : 'other';
};
var number = function (value, name, offset) {
  if (!offset) return value;
  if (isNaN(value)) throw new Error("Can't apply offset:" + offset + ' to argument `' + name + '` with non-numerical value ' + JSON.stringify(value) + '.');
  return value - offset;
};
var plural = function (value, offset, lcfunc, data, isOrdinal) {
  if ({}.hasOwnProperty.call(data, value)) return data[value];
  if (offset) value -= offset;
  var key = lcfunc(value, isOrdinal);
  return key in data ? data[key] : data.other;
};

export default {
  de: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Für die Überprüfung der Sicherheit Ihres Geräts muss 1Password für " + d.accountName + " entsperrt sein."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Für " + d.accountName + " ist es erforderlich, dass Sie Kolide auf diesem Gerät einrichten, bevor Sie auf Arbeitsanwendungen zugreifen können."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, de, { one: "", "1": number(d.count, "count") + " ungelöstes Problem", other: number(d.count, "count") + " ungelöste Probleme" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " in 1Password gespeichert"; },
    "-item--already-saved": function(d) { return d.item + " bereits gespeichert"; },
    "-item--is-linked-to-": function(d) { return d.item + " ist verlinkt mit:"; },
    "-item--saved": function(d) { return d.item + " gespeichert"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " wurde verwendet, um sich anzumelden bei:"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "1Password-Shell-Plug-in für " + d.platformName + " verfügbar"; },
    "1Password-encountered-a-problem-": function(d) { return "1Password hat ein Problem festgestellt."; },
    "1Password-is-locked": function(d) { return "1Password ist gesperrt"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "Durch eine Sicherheitslücke auf dieser Website ist Ihr aktuelles Passwort gefährdet."; },
    "About-Kolide": function(d) { return "Über Kolide"; },
    "Add-account": function(d) { return "Konto hinzufügen"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "Konto zur 1Password-Browser-Erweiterung hinzufügen"; },
    "All-good": function(d) { return "Alles gut"; },
    "All-issues-resolved": function(d) { return "Alle Probleme gelöst"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Es ist ein unerwarteter Fehler aufgetreten. Bitte <NAME_EMAIL>"; },
    "Blocked-Device": function(d) { return "Gesperrtes Gerät"; },
    Cancel: function(d) { return "Abbrechen"; },
    "Change-now": function(d) { return "Jetzt ändern"; },
    "Change-this-compromised-password": function(d) { return "Ändern Sie dieses kompromittierte Passwort"; },
    Close: function(d) { return "Schließen"; },
    "Collapse-all": function(d) { return "Alle ausblenden"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "Stellen Sie eine Verbindung zum Internet her und versuchen Sie es erneut."; },
    "Contact-Support": function(d) { return "Support kontaktieren"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "Bitten Sie den Support um Hilfe und teilen Sie ihm mit, dass es sich um ein Problem mit dem Standardkonto handelt."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "Bitten Sie den Support um Hilfe und teilen Sie ihm mit, dass es sich um ein Problem mit dem generierten Passworttresor handelt."; },
    Dismiss: function(d) { return "Verwerfen"; },
    "Don-t-ask-again": function(d) { return "Nicht erneut fragen"; },
    "Download-1Password-8": function(d) { return "1Password 8 herunterladen"; },
    Edit: function(d) { return "Bearbeiten"; },
    "Existing-items": function(d) { return "Bestehende Objekte"; },
    "Failed-to-load-item-": function(d) { return "Objekt konnte nicht geladen werden."; },
    "Failed-to-save-item-": function(d) { return "Objekt konnte nicht gespeichert werden."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "Lassen Sie dieses Objekt bei der nächsten Anmeldung mit 1Password ausfüllen, um Zeit zu sparen."; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "Beheben Sie " + plural(d.count, 0, de, { one: "", "1": number(d.count, "count") + " Problem", other: number(d.count, "count") + " Probleme" }) + ", um den Zugriff auf " + d.appName + " zu erhalten"; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "Beheben Sie " + plural(d.count, 0, de, { one: "", "1": number(d.count, "count") + " Problem", other: number(d.count, "count") + " Probleme" }) + ", um " + d.appName + " verwenden zu können."; },
    "Fix-1-issue-to-access--appName-": function(d) { return "Beheben Sie 1 Problem, um auf " + d.appName + " zuzugreifen"; },
    "Fix-issue---issue-": function(d) { return "Fehler beheben: " + d.issue; },
    "Fix-later": function(d) { return "Später beheben"; },
    "Invalid-one-time-password-secret-": function(d) { return "Ungültiger Einmalpasswort-Code."; },
    Item: function(d) { return "Objekt"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide fehlt oder ist nicht registriert"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Kolide wurde nicht gefunden. Versuchen Sie es erneut oder wenden Sie sich an Ihren Administrator, wenn das Problem weiterhin besteht."; },
    "Learn-more-": function(d) { return "Mehr erfahren …"; },
    Lock: function(d) { return "Sperren"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "Stellen Sie sicher, dass 1Password-Konten, die Logins für diese Website enthalten, entsperrt sind."; },
    "Managed-by-Trelica": function(d) { return "Verwaltet von Trelica"; },
    "New-Item": function(d) { return "Neues Objekt"; },
    "New-Notification": function(d) { return "Neue Benachrichtigung"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "Nächster Schritt: SSH-Agenten einrichten"; },
    "No-items-found-to-sign-in-with-": function(d) { return "Keine Objekte für die Anmeldung gefunden."; },
    "No-logins-found": function(d) { return "Keine Logins gefunden"; },
    "No-passkeys-found": function(d) { return "Keine Passkeys gefunden"; },
    Notifications: function(d) { return "Benachrichtigungen"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Nach dem Speichern merken wir es uns für Sie."; },
    Overwrite: function(d) { return "Überschreiben"; },
    "Overwrite-one-time-password-": function(d) { return "Einmal-Passwort überschreiben?"; },
    "Passkey-saved": function(d) { return "Passkey gespeichert"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "Bitte melden Sie sich bei 1Password an, um sich online zu schützen und Ihre Passwörter ganz einfach zu speichern und einzusetzen."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "Bitte versuchen Sie erneut zu entsperren, bevor Sie dieses Problem beheben"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Drücken Sie zum Entsperren auf das 1Password-Symbol in der Symbolleiste Ihres Browsers."; },
    Recheck: function(d) { return "Erneut prüfen"; },
    "Recheck-device": function(d) { return "Gerät erneut überprüfen"; },
    "Resolved-issue---issue-": function(d) { return "Gelöstes Problem: " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "SSH-Schlüssel erstellt. Um diesen Schlüssel lokal zu verwenden, richten Sie den 1Password-SSH-Agenten ein, der in 1Password 8 verfügbar ist."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "SSH-Schlüssel erstellt. Um diesen Schlüssel lokal zu verwenden, richten Sie den 1Password-SSH-Agenten ein."; },
    Save: function(d) { return "Speichern"; },
    "Save--item-": function(d) { return d.item + " speichern"; },
    "Save-in-1Password-": function(d) { return "In 1Password speichern?"; },
    "Save-item": function(d) { return "Objekt speichern"; },
    "Save-one-time-password": function(d) { return "Einmal-Passwort speichern"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "Einmal-Passwörter speichern, die bei der Anmeldung automatisch ausgefüllt werden."; },
    "Save-passkey": function(d) { return "Passkey speichern"; },
    "Saved-one-time-password": function(d) { return "Gespeichertes Einmal-Passwort"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Wählen Sie zum Entsperren das 1Password-Symbol in der Symbolleiste Ihres Browsers aus."; },
    "Session-expired-for-tab-": function(d) { return "Sitzung für den Tab abgelaufen."; },
    "Set-up-SSH-Agent": function(d) { return "SSH-Agenten einrichten"; },
    "Sign-in": function(d) { return "Anmelden"; },
    "Sign-in-to-1Password": function(d) { return "Bei 1Password anmelden"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "Melden Sie sich mit einem Klick bei Websites an, speichern Sie Ihre Daten ganz einfach und generieren Sie neue Passwörter mit der Browser-Erweiterung."; },
    "Sign-in-using--title----subtitle-": function(d) { return "Melden Sie sich an mit " + d.title + ": " + d.subtitle; },
    "Sign-in-with-": function(d) { return "Anmelden mit …"; },
    "Sign-in-with-a-passkey": function(d) { return "Mit einem Passkey anmelden"; },
    Snooze: function(d) { return "Schlummern"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "Etwas stimmt nicht. Bitte versuchen Sie es erneut. Wenn das Problem weiterhin besteht, wenden Sie sich bitte an Ihren Administrator"; },
    "Something-went-wrong": function(d) { return "Etwas ist schiefgelaufen"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Schritt 3: Prüfen oder bearbeiten Sie Ihr neues Login-Objekt und speichern Sie es anschließend."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "Speichern Sie Ihren " + d.credentialName + " in 1Password und authentifizieren Sie " + d.platformName + " CLI mit " + d.authType + "."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "Das Einmal-Passwort in diesem Objekt wird ersetzt."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "Zeitüberschreitung bei der Anfrage zur Erstellung eines Passkeys."; },
    "To-save--item---unlock-1Password-first-": function(d) { return "Um " + d.item + " zu speichern, müssen Sie zuerst 1Password entsperren."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "Um einen Passkey zu speichern, müssen Sie zuerst 1Password entsperren."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "Um Ihren Passkey zu verwenden, müssen Sie zuerst 1Password entsperren."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID oder Apple Watch"; },
    "Try-Again": function(d) { return "Erneut versuchen"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "Versuchen Sie, 1Password zu sperren und zu entsperren, um es erneut auszufüllen."; },
    "Unable-to-fill-password": function(d) { return "Passwort kann nicht ausgefüllt werden."; },
    "Unlock-1Password": function(d) { return "1Password entsperren"; },
    "Unlock-to-Save": function(d) { return "Zum Speichern entsperren"; },
    "Unlock-to-save": function(d) { return "Zum Speichern entsperren"; },
    "Unlocking-1Password-": function(d) { return "1Password wird entsperrt …"; },
    Update: function(d) { return "Aktualisieren"; },
    "Update-Existing": function(d) { return "Bestehende aktualisieren"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "1Password für die Zwei-Faktor-Authentifizierung verwenden"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "Verwenden Sie 1Password, um " + d.platformName + " CLI mit " + d.authType + " zu authentifizieren."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "Sicherheitsschlüssel oder anderen Passkey verwenden"; },
    "View-all": function(d) { return "Alle anzeigen"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "Das vorgeschlagene Passwort kann nicht verwendet werden, da ein unerwarteter Fehler aufgetreten ist."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "Wird in " + plural(d.daysBlocked, 0, de, { one: "", "1": number(d.daysBlocked, "daysBlocked") + " Tag", other: number(d.daysBlocked, "daysBlocked") + " Tagen" }) + " blockiert"; },
    "Will-block-today": function(d) { return "Wird heute blockieren"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "Sie können Ihre Konten in den Einstellungen verwalten."; },
    "You-have-recieved-a-new-notification-": function(d) { return "Sie haben eine neue Benachrichtigung erhalten!"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "Ihr " + d.accountName + "-Konto muss entsperrt werden, um dieses Passwort auszufüllen. Entsperren Sie es und versuchen Sie es erneut."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Für die Überprüfung der Sicherheit Ihres Geräts muss 1Password für Ihren Administrator entsperrt sein."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Ihr Administrator fordert, dass Sie Kolide auf diesem Gerät einrichten, bevor Sie auf Arbeitsanwendungen zugreifen."; },
    "a--item-": function(d) { return "ein " + d.item; },
    "an--item-": function(d) { return "ein " + d.item; },
    "an-Item": function(d) { return "ein Objekt"; },
    "one-time-password": function(d) { return "Einmaliges Passwort"; },
    "system-authentication": function(d) { return "Systemauthentifizierung"; },
    "autosave-save-type-login": function(d) { return "Login"; },
    "autosave-save-type-credit-card": function(d) { return "Kreditkarte"; },
    "autosave-save-type-identity": function(d) { return "Identität"; }
  },
  en: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " needs 1Password unlocked to check your device's security."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " requires that you set up Kolide on this device before accessing work apps."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, en, { "1": number(d.count, "count") + " issue remains", other: number(d.count, "count") + " issues remain" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " saved in 1Password"; },
    "-item--already-saved": function(d) { return d.item + " already saved"; },
    "-item--is-linked-to-": function(d) { return d.item + " is linked to:"; },
    "-item--saved": function(d) { return d.item + " saved"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " was used to sign in to:"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "1Password Shell Plugin available for " + d.platformName; },
    "1Password-encountered-a-problem-": function(d) { return "1Password encountered a problem."; },
    "1Password-is-locked": function(d) { return "1Password is locked"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "A security breach on this website has put your current password at risk."; },
    "About-Kolide": function(d) { return "About Kolide"; },
    "Add-account": function(d) { return "Add account"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "Add account to 1Password browser extension"; },
    "All-good": function(d) { return "All good"; },
    "All-issues-resolved": function(d) { return "All issues resolved"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "An unexpected error occurred. <NAME_EMAIL>"; },
    "Blocked-Device": function(d) { return "Blocked Device"; },
    Cancel: function(d) { return "Cancel"; },
    "Change-now": function(d) { return "Change now"; },
    "Change-this-compromised-password": function(d) { return "Change this compromised password"; },
    Close: function(d) { return "Close"; },
    "Collapse-all": function(d) { return "Collapse all"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "Connect to the internet and then try again."; },
    "Contact-Support": function(d) { return "Contact Support"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "Contact support for help, and tell them it's a problem with the default account."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "Contact support for help, and tell them it's a problem with the generated password vault."; },
    Dismiss: function(d) { return "Dismiss"; },
    "Don-t-ask-again": function(d) { return "Don’t ask again"; },
    "Download-1Password-8": function(d) { return "Download 1Password 8"; },
    Edit: function(d) { return "Edit"; },
    "Existing-items": function(d) { return "Existing items"; },
    "Failed-to-load-item-": function(d) { return "Failed to load item."; },
    "Failed-to-save-item-": function(d) { return "Failed to save item."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "Fill this item with 1Password next time you log in to save time"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "Fix " + plural(d.count, 0, en, { "1": number(d.count, "count") + " issue", other: number(d.count, "count") + " issues" }) + " to keep " + d.appName + " access"; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "Fix " + plural(d.count, 0, en, { "1": number(d.count, "count") + " issue", other: number(d.count, "count") + " issues" }) + " to use " + d.appName; },
    "Fix-1-issue-to-access--appName-": function(d) { return "Fix 1 issue to access " + d.appName; },
    "Fix-issue---issue-": function(d) { return "Fix issue: " + d.issue; },
    "Fix-later": function(d) { return "Fix later"; },
    "Invalid-one-time-password-secret-": function(d) { return "Invalid one-time password secret."; },
    Item: function(d) { return "Item"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide is missing or not registered"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Kolide wasn't found. Try again, or contact your administrator if the problem persists."; },
    "Learn-more-": function(d) { return "Learn more…"; },
    Lock: function(d) { return "Lock"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "Make sure 1Password accounts containing logins for this site are unlocked."; },
    "Managed-by-Trelica": function(d) { return "Managed by Trelica"; },
    "New-Item": function(d) { return "New Item"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "Next step: Set up SSH Agent"; },
    "No-items-found-to-sign-in-with-": function(d) { return "No items found to sign in with."; },
    "No-logins-found": function(d) { return "No logins found"; },
    "No-passkeys-found": function(d) { return "No passkeys found"; },
    Notifications: function(d) { return "Notifications"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Once saved, we'll remember it for you."; },
    Overwrite: function(d) { return "Overwrite"; },
    "Overwrite-one-time-password-": function(d) { return "Overwrite one-time password?"; },
    "Passkey-saved": function(d) { return "Passkey saved"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "Please sign in to 1Password to keep yourself safe online and make it easy to save and fill your passwords."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "Please try unlocking again before fixing this issue"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Press the 1Password icon in your browser's toolbar to unlock."; },
    Recheck: function(d) { return "Recheck"; },
    "Recheck-device": function(d) { return "Recheck device"; },
    "Resolved-issue---issue-": function(d) { return "Resolved issue: " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "SSH key created. To use this key locally, set up the 1Password SSH Agent, available on 1Password 8."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "SSH key created. To use this key locally, set up the 1Password SSH agent."; },
    Save: function(d) { return "Save"; },
    "Save--item-": function(d) { return "Save " + d.item; },
    "Save-in-1Password-": function(d) { return "Save in 1Password?"; },
    "Save-item": function(d) { return "Save item"; },
    "Save-one-time-password": function(d) { return "Save one-time password"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "Save one-time passwords that will be automatically filled during sign-in."; },
    "Save-passkey": function(d) { return "Save passkey"; },
    "Saved-one-time-password": function(d) { return "Saved one-time password"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Select the 1Password icon in your browser's toolbar to unlock."; },
    "Session-expired-for-tab-": function(d) { return "Session expired for tab."; },
    "Set-up-SSH-Agent": function(d) { return "Set up SSH Agent"; },
    "Sign-in": function(d) { return "Sign in"; },
    "Sign-in-to-1Password": function(d) { return "Sign in to 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "Sign in to websites with one click, easily save your information, and generate new passwords with the browser extension."; },
    "Sign-in-using--title----subtitle-": function(d) { return "Sign in using " + d.title + ": " + d.subtitle; },
    "Sign-in-with-": function(d) { return "Sign in with…"; },
    "Sign-in-with-a-passkey": function(d) { return "Sign in with a passkey"; },
    Snooze: function(d) { return "Snooze"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "Something isn't right. Please try again. If the problem persists, contact your administrator"; },
    "Something-went-wrong": function(d) { return "Something went wrong"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Step 3: Review or edit your new login item, then save it."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "Store your " + d.credentialName + " in 1Password and authenticate " + d.platformName + " CLI with " + d.authType + "."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "The one-time password in this item will be replaced."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "The request to create a passkey timed out."; },
    "To-save--item---unlock-1Password-first-": function(d) { return "To save " + d.item + ", unlock 1Password first."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "To save a passkey, unlock 1Password first."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "To use your passkey, unlock 1Password first."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID or Apple Watch"; },
    "Try-Again": function(d) { return "Try Again"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "Try locking and then unlocking 1Password to fill again."; },
    "Unable-to-fill-password": function(d) { return "Unable to fill password"; },
    "Unlock-1Password": function(d) { return "Unlock 1Password"; },
    "Unlock-to-Save": function(d) { return "Unlock to Save"; },
    "Unlock-to-save": function(d) { return "Unlock to save"; },
    "Unlocking-1Password-": function(d) { return "Unlocking 1Password…"; },
    Update: function(d) { return "Update"; },
    "Update-Existing": function(d) { return "Update Existing"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "Use 1Password for two-factor authentication"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "Use 1Password to authenticate " + d.platformName + " CLI with " + d.authType + "."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "Use a security key or another passkey"; },
    "View-all": function(d) { return "View all"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "We're unable to use the suggested password as an unexpected error occurred."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "Will block in " + plural(d.daysBlocked, 0, en, { "1": number(d.daysBlocked, "daysBlocked") + " day", other: number(d.daysBlocked, "daysBlocked") + " days" }); },
    "Will-block-today": function(d) { return "Will block today"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "You can manage your accounts in Settings."; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "Your " + d.accountName + " account needs to be unlocked to fill this password. Unlock it and try again."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Your admin needs 1Password unlocked to check your device's security."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Your admin requires that you set up Kolide on this device before accessing work apps."; },
    "a--item-": function(d) { return "a " + d.item; },
    "an--item-": function(d) { return "an " + d.item; },
    "an-Item": function(d) { return "an Item"; },
    "one-time-password": function(d) { return "one-time password"; },
    "system-authentication": function(d) { return "system authentication"; },
    "autosave-save-type-login": function(d) { return "Login"; },
    "autosave-save-type-credit-card": function(d) { return "Credit card"; },
    "autosave-save-type-identity": function(d) { return "Identity"; }
  },
  es: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " necesita desbloquear 1Password para comprobar la seguridad del dispositivo."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " requiere que configures Kolide en este dispositivo antes de acceder a las aplicaciones profesionales."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, es, { one: "", "1": number(d.count, "count") + " problema restante", other: number(d.count, "count") + " problemas restantes" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " guardado en 1Password"; },
    "-item--already-saved": function(d) { return d.item + " ya se ha guardado"; },
    "-item--is-linked-to-": function(d) { return d.item + " se ha vinculado a:"; },
    "-item--saved": function(d) { return d.item + " guardado"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " se ha utilizado para iniciar sesión en:"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "Plugin del shell de 1Password disponible para " + d.platformName; },
    "1Password-encountered-a-problem-": function(d) { return "1Password ha detectado un problema."; },
    "1Password-is-locked": function(d) { return "1Password está bloqueado"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "Un fallo de seguridad en este sitio web ha puesto en riesgo tu contraseña actual."; },
    "About-Kolide": function(d) { return "Acerca de Kolide"; },
    "Add-account": function(d) { return "Añadir cuenta"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "Añadir cuenta a la extensión de navegador de 1Password"; },
    "All-good": function(d) { return "Todo bien"; },
    "All-issues-resolved": function(d) { return "Todos los problemas resueltos"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Se ha producido un error inesperado. Ponte en <NAME_EMAIL>"; },
    "Blocked-Device": function(d) { return "Dispositivo bloqueado"; },
    Cancel: function(d) { return "Cancelar"; },
    "Change-now": function(d) { return "Cambiar ahora"; },
    "Change-this-compromised-password": function(d) { return "Cambiar esta contraseña comprometida"; },
    Close: function(d) { return "Cerrar"; },
    "Collapse-all": function(d) { return "Ocultar todo"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "Conéctate a internet y vuelve a intentarlo."; },
    "Contact-Support": function(d) { return "Contactar con el equipo de asistencia"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "Contacta con el equipo de asistencia para recibir ayuda y contarles si hay algún problema con la cuenta predeterminada."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "Contacta con el equipo de asistencia para recibir ayuda y contarles si hay algún problema con la bóveda generada con contraseña."; },
    Dismiss: function(d) { return "Descartar"; },
    "Don-t-ask-again": function(d) { return "No preguntar de nuevo"; },
    "Download-1Password-8": function(d) { return "Descargar 1Password 8"; },
    Edit: function(d) { return "Editar"; },
    "Existing-items": function(d) { return "Elementos existentes"; },
    "Failed-to-load-item-": function(d) { return "Error al cargar el elemento."; },
    "Failed-to-save-item-": function(d) { return "Error al guardar el elemento."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "Cumplimenta este elemento con 1Password la próxima vez que inicies sesión para guardar el elemento"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "Resuelve " + plural(d.count, 0, es, { one: "", "1": number(d.count, "count") + " problema", other: number(d.count, "count") + " problemas" }) + " para mantener el acceso de " + d.appName; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "Resuelve " + plural(d.count, 0, es, { one: "", "1": number(d.count, "count") + " problema", other: number(d.count, "count") + " problemas" }) + " para usar " + d.appName; },
    "Fix-1-issue-to-access--appName-": function(d) { return "Resuelve 1 problema para acceder a " + d.appName; },
    "Fix-issue---issue-": function(d) { return "Resolver problema: " + d.issue; },
    "Fix-later": function(d) { return "Resolver más tarde"; },
    "Invalid-one-time-password-secret-": function(d) { return "Contraseña secreta de uso único no válida."; },
    Item: function(d) { return "Elemento"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Falta Kolide o no está registrado"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "No se ha encontrado Kolida. Inténtalo de nuevo o ponte en contacto con tu administrador si el problema persiste."; },
    "Learn-more-": function(d) { return "Más información…"; },
    Lock: function(d) { return "Bloquear"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "Asegúrate de que las cuentas de 1Password que contienen credenciales de inicio de sesión para este sitio están bloqueadas."; },
    "Managed-by-Trelica": function(d) { return "Gestionado por Trelica"; },
    "New-Item": function(d) { return "Nuevo elemento"; },
    "New-Notification": function(d) { return "Notificación nueva"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "Paso siguiente: configurar el agente SSH"; },
    "No-items-found-to-sign-in-with-": function(d) { return "No se han encontrado elementos con los que iniciar sesión."; },
    "No-logins-found": function(d) { return "No se han encontrado inicios de sesión"; },
    "No-passkeys-found": function(d) { return "No se han encontrado claves"; },
    Notifications: function(d) { return "Notificaciones"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Una vez guardado, lo recordaremos por ti."; },
    Overwrite: function(d) { return "Sobrescribir"; },
    "Overwrite-one-time-password-": function(d) { return "¿Sobrescribir contraseña de un solo uso?"; },
    "Passkey-saved": function(d) { return "Clave guardada"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "Inicia sesión en 1Password para navegar de forma segura y para que te resulte más fácil guardar y recordar tus contraseñas."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "Intenta desbloquearlo de nuevo antes de resolver el problema"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Pulsa el icono de 1Password en la barra de herramientas de tu navegador para desbloquearlo."; },
    Recheck: function(d) { return "Volver a comprobar"; },
    "Recheck-device": function(d) { return "Volver a comprobar el dispositivo"; },
    "Resolved-issue---issue-": function(d) { return "Resolver problema: " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "Clave SSH creada. Para usar esta clave de forma local, configura el agente SSH de 1Password, disponible en 1Password 8."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "Clave SSH creada. Para usar esta clave de forma local, configura el agente SSH de 1Password."; },
    Save: function(d) { return "Guardar"; },
    "Save--item-": function(d) { return "Guardar " + d.item; },
    "Save-in-1Password-": function(d) { return "¿Guardar en 1Password?"; },
    "Save-item": function(d) { return "Guardar elemento"; },
    "Save-one-time-password": function(d) { return "Guardar la contraseña de un solo uso"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "Guardar la contraseña de un solo uso que se cumplimentará automáticamente durante el inicio de sesión."; },
    "Save-passkey": function(d) { return "Guardar clave"; },
    "Saved-one-time-password": function(d) { return "Contraseña de un solo uso guardada"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Selecciona el icono de 1Password en la barra de herramientas de tu navegador para desbloquearlo."; },
    "Session-expired-for-tab-": function(d) { return "Sesión caducada para la pestaña."; },
    "Set-up-SSH-Agent": function(d) { return "Configurar el agente SSH"; },
    "Sign-in": function(d) { return "Iniciar sesión"; },
    "Sign-in-to-1Password": function(d) { return "Iniciar sesión en 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "Inicia sesión en sitios web con tan solo un clic, guarda tu información fácilmente y genera nuevas contraseñas con la extensión de navegador."; },
    "Sign-in-using--title----subtitle-": function(d) { return "Iniciar sesión usando " + d.title + ": " + d.subtitle; },
    "Sign-in-with-": function(d) { return "Iniciar sesión con…"; },
    "Sign-in-with-a-passkey": function(d) { return "Iniciar sesión con una clave"; },
    Snooze: function(d) { return "Posponer"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "Algo ha salido mal. Inténtalo de nuevo. Si el problema persiste, ponte en contacto con el administrador"; },
    "Something-went-wrong": function(d) { return "Algo ha salido mal"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Paso 3: revisa o edita el nuevo elemento de inicio de sesión y guárdalo."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "Guarda tu " + d.credentialName + " en 1Password y autentica el CLI de " + d.platformName + " con " + d.authType + "."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "La contraseña de un solo uso en este elemento se sustituirá."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "Se ha agotado el tiempo de espera de la solicitud para crear una clave."; },
    "To-save--item---unlock-1Password-first-": function(d) { return "Para guardar " + d.item + ", desbloquea primero 1Password."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "Para guardar la clave, desbloquea primero 1Password."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "Para usar la clave, desbloquea primero 1Password."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID y Apple Watch"; },
    "Try-Again": function(d) { return "Inténtalo de nuevo"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "Intenta bloquear y desbloquear 1Password para volver a cumplimentar."; },
    "Unable-to-fill-password": function(d) { return "No se ha podido cumplimentar la contraseña"; },
    "Unlock-1Password": function(d) { return "Desbloquear 1Password"; },
    "Unlock-to-Save": function(d) { return "Desbloquear para guardar"; },
    "Unlock-to-save": function(d) { return "Desbloquear para guardar"; },
    "Unlocking-1Password-": function(d) { return "Desbloquear 1Password…"; },
    Update: function(d) { return "Actualizar"; },
    "Update-Existing": function(d) { return "Actualizar existente"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "Utiliza 1Password para la autenticación de dos factores"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "Use 1Password para autenticar el CLI de " + d.platformName + " con " + d.authType + "."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "Utilizar una clave de seguridad u otra clave"; },
    "View-all": function(d) { return "Ver todo"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "No podemos usar la contraseña sugerida porque se ha producido un error inesperado."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "Se bloqueará en " + plural(d.daysBlocked, 0, es, { one: "", "1": number(d.daysBlocked, "daysBlocked") + " día", other: number(d.daysBlocked, "daysBlocked") + " días" }); },
    "Will-block-today": function(d) { return "Se bloqueará hoy"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "Puedes gestionar tus cuentas desde Ajustes."; },
    "You-have-recieved-a-new-notification-": function(d) { return "¡Has recibido una nueva notificación!"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "Tu cuenta " + d.accountName + " se tiene que desbloquear para cumplimentar esta contraseña. Desbloquéala y vuelve a intentarlo de nuevo."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Tu administrados necesita desbloquear 1Password para comprobar la seguridad del dispositivo."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Tu administrador requiere que configures Kolide en este dispositivo antes de acceder a las aplicaciones profesionales."; },
    "a--item-": function(d) { return "un/a " + d.item; },
    "an--item-": function(d) { return "un/a " + d.item; },
    "an-Item": function(d) { return "un elemento"; },
    "one-time-password": function(d) { return "contraseña de un solo uso"; },
    "system-authentication": function(d) { return "autenticación del sistema"; },
    "autosave-save-type-login": function(d) { return "Inicio de sesión"; },
    "autosave-save-type-credit-card": function(d) { return "Tarjeta de crédito"; },
    "autosave-save-type-identity": function(d) { return "Identidad"; }
  },
  fr: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " nécessite que 1Password soit déverrouillé pour vérifier la sécurité de votre appareil."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " vous demande de configurer Kolide sur cet appareil avant d'accéder aux applications professionnelles."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, fr, { "1": "Il reste " + number(d.count, "count") + " problème", other: "Il reste " + number(d.count, "count") + " problèmes" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " enregistré dans 1Password"; },
    "-item--already-saved": function(d) { return d.item + " déjà enregistré"; },
    "-item--is-linked-to-": function(d) { return d.item + " est associé à :"; },
    "-item--saved": function(d) { return d.item + " enregistré"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " a été utilisé pour se connecter :"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "Plug-in du shell 1Password disponible pour " + d.platformName; },
    "1Password-encountered-a-problem-": function(d) { return "1Password a rencontré un problème."; },
    "1Password-is-locked": function(d) { return "1Password est verrouillé"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "Une faille de sécurité sur ce site web a compromis votre mot de passe actuel."; },
    "About-Kolide": function(d) { return "À propos de Kolide"; },
    "Add-account": function(d) { return "Ajouter un compte"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "Ajouter un compte à l'extension de navigateur 1Password"; },
    "All-good": function(d) { return "Terminé"; },
    "All-issues-resolved": function(d) { return "Tous les problèmes sont résolus"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Une erreur inattendue s'est produite. <NAME_EMAIL>"; },
    "Blocked-Device": function(d) { return "Appareil bloqué"; },
    Cancel: function(d) { return "Annuler"; },
    "Change-now": function(d) { return "Modifier maintenant"; },
    "Change-this-compromised-password": function(d) { return "Modifier le mot de passe compromis"; },
    Close: function(d) { return "Fermer"; },
    "Collapse-all": function(d) { return "Tout réduire"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "Connectez-vous à Internet et réessayez."; },
    "Contact-Support": function(d) { return "Contacter l'assistance"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "Contactez le service d'assistance pour obtenir de l'aide, et précisez qu'il s'agit d'un problème avec le compte par défaut."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "Contactez le service d'assistance pour obtenir de l'aide, et précisez qu'il s'agit d'un problème avec le coffre du mot de passe généré."; },
    Dismiss: function(d) { return "Ignorer"; },
    "Don-t-ask-again": function(d) { return "Ne pas demander à nouveau"; },
    "Download-1Password-8": function(d) { return "Télécharger 1Password 8"; },
    Edit: function(d) { return "Modifier"; },
    "Existing-items": function(d) { return "Éléments existants"; },
    "Failed-to-load-item-": function(d) { return "Impossible de charger l'élément."; },
    "Failed-to-save-item-": function(d) { return "Impossible d'enregistrer l'élément."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "Remplissez cet élément avec 1Password la prochaine fois que vous vous connecterez pour économiser du temps"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "Corriger " + plural(d.count, 0, fr, { "1": number(d.count, "count") + " problème", other: number(d.count, "count") + " problèmes" }) + " pour conserver l'accès à " + d.appName; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "Corriger " + plural(d.count, 0, fr, { "1": number(d.count, "count") + " problème", other: number(d.count, "count") + " problèmes" }) + " pour utiliser " + d.appName; },
    "Fix-1-issue-to-access--appName-": function(d) { return "Corriger 1 problème pour accéder à " + d.appName; },
    "Fix-issue---issue-": function(d) { return "Corriger le problème : " + d.issue; },
    "Fix-later": function(d) { return "Corriger plus tard"; },
    "Invalid-one-time-password-secret-": function(d) { return "Secret du mot de passe à usage unique non valide."; },
    Item: function(d) { return "Élément"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide est manquant ou n'est pas enregistré"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Kolide n'a pas été trouvé. Réessayez ou contactez votre administrateur si le problème persiste."; },
    "Learn-more-": function(d) { return "En savoir plus…"; },
    Lock: function(d) { return "Verrouiller"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "Assurez-vous que les comptes 1Password contenant les identifiants pour ce site sont déverrouillés."; },
    "Managed-by-Trelica": function(d) { return "Géré par Trelica"; },
    "New-Item": function(d) { return "Nouvel élément"; },
    "New-Notification": function(d) { return "Nouvelle notification"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "Étape suivante : Configurer l'agent SSH"; },
    "No-items-found-to-sign-in-with-": function(d) { return "Aucun élément trouvé pour se connecter."; },
    "No-logins-found": function(d) { return "Aucun identifiant trouvé"; },
    "No-passkeys-found": function(d) { return "Aucune clé d'accès trouvée"; },
    Notifications: function(d) { return "Notifications"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Une fois enregistré, nous le garderons en mémoire pour vous."; },
    Overwrite: function(d) { return "Remplacer"; },
    "Overwrite-one-time-password-": function(d) { return "Remplacer le mot de passe à usage unique ?"; },
    "Passkey-saved": function(d) { return "Clé d'accès enregistrée"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "Connectez-vous à 1Password pour assurer votre sécurité en ligne et vous permettre d'enregistrer et de remplir vos mots de passe en toute simplicité."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "Veuillez essayer de déverrouiller à nouveau avant de résoudre ce problème."; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Appuyez sur l'icône 1Password dans la barre d'outils de votre navigateur pour déverrouiller."; },
    Recheck: function(d) { return "Revérifier"; },
    "Recheck-device": function(d) { return "Revérifier l'appareil"; },
    "Resolved-issue---issue-": function(d) { return "Problème résolu : " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "Clé SSH créée. Pour utiliser cette clé localement, configurez l'agent SSH 1Password, disponible sur 1Password 8."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "Clé SSH créée. Pour utiliser cette clé localement, configurez l'agent SSH 1Password."; },
    Save: function(d) { return "Enregistrer"; },
    "Save--item-": function(d) { return "Enregistrer " + d.item; },
    "Save-in-1Password-": function(d) { return "Enregistrer dans 1Password ?"; },
    "Save-item": function(d) { return "Enregistrer l'élément"; },
    "Save-one-time-password": function(d) { return "Enregistrer le mot de passe à usage unique"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "Enregistrez les mots de passe à usage unique qui seront automatiquement remplis lors de la connexion."; },
    "Save-passkey": function(d) { return "Enregistrer la clé d'accès"; },
    "Saved-one-time-password": function(d) { return "Mot de passe à usage unique enregistré"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Sélectionnez l'icône 1Password dans la barre d'outils de votre navigateur pour déverrouiller."; },
    "Session-expired-for-tab-": function(d) { return "Session expirée pour l'onglet."; },
    "Set-up-SSH-Agent": function(d) { return "Configurer l'agent SSH"; },
    "Sign-in": function(d) { return "Connexion"; },
    "Sign-in-to-1Password": function(d) { return "Connexion à 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "Connectez-vous à des sites web en un clic, enregistrez facilement vos informations et générez de nouveaux mots de passe grâce à l'extension de navigateur."; },
    "Sign-in-using--title----subtitle-": function(d) { return "Se connecter en utilisant " + d.title + " : " + d.subtitle; },
    "Sign-in-with-": function(d) { return "Se connecter avec…"; },
    "Sign-in-with-a-passkey": function(d) { return "Se connecter avec une clé d'accès"; },
    Snooze: function(d) { return "Mettre en attente"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "Un problème est survenu. Veuillez réessayer. Si le problème persiste, contactez votre administrateur"; },
    "Something-went-wrong": function(d) { return "Un problème est survenu"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Étape 3 : Consultez ou modifiez votre nouvel identifiant de connexion, puis enregistrez-le."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "Stockez votre " + d.credentialName + " dans 1Password et authentifiez l'interface CLI de " + d.platformName + " avec " + d.authType + "."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "Le mot de passe à usage unique de cet élément sera remplacé."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "La demande de création d'un mot de passe a dépassé le temps imparti."; },
    "To-save--item---unlock-1Password-first-": function(d) { return "Pour enregistrer " + d.item + ", commencez par déverrouiller 1Password."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "Pour enregistrer une clé d'accès, déverrouillez d'abord 1Password."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "Pour utiliser votre clé d'accès, déverrouillez d'abord 1Password."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID ou Apple Watch"; },
    "Try-Again": function(d) { return "Réessayer"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "Essayez de verrouiller puis de déverrouiller 1Password pour le remplir à nouveau."; },
    "Unable-to-fill-password": function(d) { return "Impossible de remplir le mot de passe"; },
    "Unlock-1Password": function(d) { return "Déverrouiller 1Password"; },
    "Unlock-to-Save": function(d) { return "Déverrouiller pour enregistrer"; },
    "Unlock-to-save": function(d) { return "Déverrouiller pour enregistrer"; },
    "Unlocking-1Password-": function(d) { return "Déverrouillage de 1Password…"; },
    Update: function(d) { return "Mettre à jour"; },
    "Update-Existing": function(d) { return "Mettre à jour l'élément existant"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "Utiliser 1Password pour l'authentification à deux facteurs"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "Utilisez 1Password pour authentifier l'interface CLI de " + d.platformName + " avec " + d.authType + "."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "Utiliser une clé de sécurité ou une autre clé d'accès"; },
    "View-all": function(d) { return "Tout afficher"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "Nous ne pouvons pas utiliser le mot de passe suggéré car une erreur inattendue s'est produite."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "Bloquera dans " + plural(d.daysBlocked, 0, fr, { "1": number(d.daysBlocked, "daysBlocked") + " jour", other: number(d.daysBlocked, "daysBlocked") + " jours" }); },
    "Will-block-today": function(d) { return "Bloquera aujourd'hui"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "Vous pouvez gérer vos comptes dans les Paramètres."; },
    "You-have-recieved-a-new-notification-": function(d) { return "Vous avez reçu une nouvelle notification !"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "Votre compte " + d.accountName + " doit être déverrouillé pour remplir ce mot de passe. Déverrouillez-le et réessayez."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Votre administrateur doit déverrouiller 1Password pour vérifier la sécurité de votre appareil."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Votre administrateur vous demande de configurer Kolide sur cet appareil avant d'accéder aux applications professionnelles."; },
    "a--item-": function(d) { return "un " + d.item; },
    "an--item-": function(d) { return "un " + d.item; },
    "an-Item": function(d) { return "un élément"; },
    "one-time-password": function(d) { return "mot de passe à usage unique"; },
    "system-authentication": function(d) { return "authentification système"; },
    "autosave-save-type-login": function(d) { return "Connexion"; },
    "autosave-save-type-credit-card": function(d) { return "Carte de crédit"; },
    "autosave-save-type-identity": function(d) { return "Identité"; }
  },
  it: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " ha bisogno che 1Password sia sbloccato per controllare la sicurezza del tuo dispositivo."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " ti chiede di configurare Kolide su questo dispositivo prima di poter utilizzare le app di lavoro."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, it, { one: "", "1": number(d.count, "count") + " problema rimanente", other: number(d.count, "count") + " problemi rimanenti" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " salvato in 1Password"; },
    "-item--already-saved": function(d) { return d.item + " già salvato"; },
    "-item--is-linked-to-": function(d) { return d.item + " è collegato a:"; },
    "-item--saved": function(d) { return d.item + " salvato"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " è stato utilizzato per accedere a:"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "Plugin della shell 1Password disponibile per " + d.platformName; },
    "1Password-encountered-a-problem-": function(d) { return "1Password ha riscontrato un problema."; },
    "1Password-is-locked": function(d) { return "1Password è bloccato"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "Una violazione di sicurezza su questo sito web ha messo a rischio la tua password attuale."; },
    "About-Kolide": function(d) { return "Informazioni su Kolide"; },
    "Add-account": function(d) { return "Aggiungi account"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "Aggiungi l’account all’estensione del browser di 1Password"; },
    "All-good": function(d) { return "Tutto perfetto"; },
    "All-issues-resolved": function(d) { return "Tutti i problemi risolti"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Si è verificato un errore imprevisto. Contatta <EMAIL>"; },
    "Blocked-Device": function(d) { return "Dispositivo bloccato"; },
    Cancel: function(d) { return "Annulla"; },
    "Change-now": function(d) { return "Cambia ora"; },
    "Change-this-compromised-password": function(d) { return "Cambia questa password compromessa"; },
    Close: function(d) { return "Chiudi"; },
    "Collapse-all": function(d) { return "Comprimi tutto"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "Collegati a internet e riprova."; },
    "Contact-Support": function(d) { return "Contatta il supporto"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "Contatta il supporto per chiedere assistenza e spiega che si tratta di un problema con l’account predefinito."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "Contatta il supporto per chiedere assistenza e spiega che si tratta di un problema con la cassaforte delle password generate."; },
    Dismiss: function(d) { return "Ignora"; },
    "Don-t-ask-again": function(d) { return "Non chiedermelo più"; },
    "Download-1Password-8": function(d) { return "Scarica 1Password 8"; },
    Edit: function(d) { return "Modifica"; },
    "Existing-items": function(d) { return "Elementi esistenti"; },
    "Failed-to-load-item-": function(d) { return "Impossibile caricare l’elemento."; },
    "Failed-to-save-item-": function(d) { return "Impossibile salvare l’elemento."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "Compila questo elemento con 1Password la prossima volta che effettui l’accesso per risparmiare tempo"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "Risolvi " + plural(d.count, 0, it, { one: "", "1": number(d.count, "count") + " problema", other: number(d.count, "count") + " problemi" }) + " per mantenere l’accesso a " + d.appName; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "Risolvi " + plural(d.count, 0, it, { one: "", "1": number(d.count, "count") + " problema", other: number(d.count, "count") + " problemi" }) + " per utilizzare " + d.appName; },
    "Fix-1-issue-to-access--appName-": function(d) { return "Risolvi 1 problema per accedere a " + d.appName; },
    "Fix-issue---issue-": function(d) { return "Risolvi il problema: " + d.issue; },
    "Fix-later": function(d) { return "Risolvi più tardi"; },
    "Invalid-one-time-password-secret-": function(d) { return "Segreto della password monouso non valido."; },
    Item: function(d) { return "Elemento"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide non presente o non registrato"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Impossibile trovare Kolide. Riprova o, se il problema persiste, contatta l’amministratore."; },
    "Learn-more-": function(d) { return "Ulteriori informazioni…"; },
    Lock: function(d) { return "Blocca"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "Assicurati che gli account 1Password che contengono le credenziali per questo sito siano sbloccati."; },
    "Managed-by-Trelica": function(d) { return "Gestito da Trelica"; },
    "New-Item": function(d) { return "Nuovo elemento"; },
    "New-Notification": function(d) { return "Nuova notifica"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "Passaggio successivo: configura l’Agente SSH"; },
    "No-items-found-to-sign-in-with-": function(d) { return "Nessun elemento trovato con cui effettuare l’accesso."; },
    "No-logins-found": function(d) { return "Nessuna credenziale trovata"; },
    "No-passkeys-found": function(d) { return "Nessuna chiave trovata"; },
    Notifications: function(d) { return "Notifiche"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Una volta salvato, lo ricorderemo per te."; },
    Overwrite: function(d) { return "Sovrascrivi"; },
    "Overwrite-one-time-password-": function(d) { return "Sovrascrivere la password monouso?"; },
    "Passkey-saved": function(d) { return "Chiave salvata"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "Accedi a 1Password per preservare la tua sicurezza online e per facilitare il salvataggio e l’inserimento delle password."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "Prova a sbloccare di nuovo prima di risolvere questo problema"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Premi l’icona di 1Password nella barra degli strumenti del browser per sbloccare."; },
    Recheck: function(d) { return "Controlla di nuovo"; },
    "Recheck-device": function(d) { return "Controlla nuovamente il dispositivo"; },
    "Resolved-issue---issue-": function(d) { return "Problema risolto: " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "Chiave SSH creata. Per utilizzare questa chiave in locale, configura l’agente SSH di 1Password, disponibile su 1Password 8."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "Chiave SSH creata. Per utilizzare questa chiave in locale, configura l’Agente SSH di 1Password."; },
    Save: function(d) { return "Salva"; },
    "Save--item-": function(d) { return "Salva " + d.item; },
    "Save-in-1Password-": function(d) { return "Salvare in 1Password?"; },
    "Save-item": function(d) { return "Salva l’elemento"; },
    "Save-one-time-password": function(d) { return "Salva la password monouso"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "Salva le password monouso che saranno compilate automaticamente durante l’accesso."; },
    "Save-passkey": function(d) { return "Salva la chiave"; },
    "Saved-one-time-password": function(d) { return "Password monouso salvata"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Seleziona l’icona di 1Password nella barra degli strumenti del browser per sbloccare."; },
    "Session-expired-for-tab-": function(d) { return "Sessione scaduta per la scheda."; },
    "Set-up-SSH-Agent": function(d) { return "Configura l’Agente SSH"; },
    "Sign-in": function(d) { return "Accedi"; },
    "Sign-in-to-1Password": function(d) { return "Accedi a 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "Accedi ai siti web con un semplice click, salva facilmente le informazioni e genera nuove password con l’estensione del browser."; },
    "Sign-in-using--title----subtitle-": function(d) { return "Accedi utilizzando " + d.title + ": " + d.subtitle; },
    "Sign-in-with-": function(d) { return "Accedi con…"; },
    "Sign-in-with-a-passkey": function(d) { return "Accedi con una chiave"; },
    Snooze: function(d) { return "Posticipa"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "C’è qualcosa che non va. Riprova. Se il problema persiste, contatta il tuo amministratore"; },
    "Something-went-wrong": function(d) { return "Qualcosa è andato storto"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Passaggio 3: rivedi o modifica il tuo nuovo elemento di accesso, quindi salvalo."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "Memorizza le tue credenziali di " + d.credentialName + " in 1Password e autentica la CLI di " + d.platformName + " con " + d.authType + "."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "Le password monouso in questo elemento verranno sostituite."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "La richiesta di creazione di una chiave è scaduta."; },
    "To-save--item---unlock-1Password-first-": function(d) { return "Per salvare " + d.item + ", sblocca prima 1Password."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "Per salvare una chiave di accesso, sblocca prima 1Password."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "Per utilizzare la tua chiave di accesso, sblocca prima 1Password."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID o Apple Watch"; },
    "Try-Again": function(d) { return "Riprova"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "Prova a bloccare e poi a sbloccare 1Password per riempire di nuovo il campo."; },
    "Unable-to-fill-password": function(d) { return "Impossibile compilare la password"; },
    "Unlock-1Password": function(d) { return "Sblocca 1Password"; },
    "Unlock-to-Save": function(d) { return "Sblocca per salvare"; },
    "Unlock-to-save": function(d) { return "Sblocca per salvare"; },
    "Unlocking-1Password-": function(d) { return "Sblocco di 1Password in corso…"; },
    Update: function(d) { return "Aggiorna"; },
    "Update-Existing": function(d) { return "Aggiorna esistente"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "Usa 1Password per l’autenticazione a due fattori"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "Utilizza 1Password per autenticare la CLI di " + d.platformName + " con " + d.authType + "."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "Utilizza una chiave di sicurezza o un’altra chiave"; },
    "View-all": function(d) { return "Visualizza tutto"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "Non possiamo usare la password suggerita perché si è verificato un errore imprevisto."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "Si bloccherà tra " + plural(d.daysBlocked, 0, it, { "1": number(d.daysBlocked, "daysBlocked") + " giorno", other: number(d.daysBlocked, "daysBlocked") + " giorni" }); },
    "Will-block-today": function(d) { return "Si bloccherà oggi"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "Puoi gestire i tuoi account nelle Impostazioni."; },
    "You-have-recieved-a-new-notification-": function(d) { return "Hai ricevuto una nuova notifica!"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "Il tuo account " + d.accountName + " deve essere sbloccato per inserire questa password. Sbloccalo e riprova."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Il tuo amministratore ha bisogno che 1Password sia sbloccato per controllare la sicurezza del tuo dispositivo."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Il tuo amministratore ti chiede di configurare Kolide su questo dispositivo prima di poter utilizzare le app di lavoro."; },
    "a--item-": function(d) { return "un " + d.item; },
    "an--item-": function(d) { return "un " + d.item; },
    "an-Item": function(d) { return "un elemento"; },
    "one-time-password": function(d) { return "password monouso"; },
    "system-authentication": function(d) { return "autenticazione di sistema"; },
    "autosave-save-type-login": function(d) { return "Accesso"; },
    "autosave-save-type-credit-card": function(d) { return "Carta di credito"; },
    "autosave-save-type-identity": function(d) { return "Identità"; }
  },
  ja: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + "は、デバイスのセキュリティを確認するために1Passwordのロックを解除する必要があります。"; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + "は、仕事用アプリにアクセスする前に、このデバイスでKolideを設定する必要があります。"; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, ja, { "1": number(d.count, "count") + "件の問題が残っています", other: number(d.count, "count") + "件の問題が残っています" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + "が1Passwordに保存されました"; },
    "-item--already-saved": function(d) { return d.item + "はすでに保存されています"; },
    "-item--is-linked-to-": function(d) { return d.item + "はリンクされています："; },
    "-item--saved": function(d) { return d.item + "が保存されました"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + "はサインインするために使用されました："; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "1Password Shell Pluginは" + d.platformName + "で利用できます"; },
    "1Password-encountered-a-problem-": function(d) { return "1Passwordで問題が発生しました。"; },
    "1Password-is-locked": function(d) { return "1Passwordはロックされています"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "このウェブサイトのセキュリティ侵害により、お客さまの現在のパスワードが危険にさらされています。"; },
    "About-Kolide": function(d) { return "Kolideについて"; },
    "Add-account": function(d) { return "アカウントを追加"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "1Passwordブラウザ拡張機能にアカウントを追加する"; },
    "All-good": function(d) { return "全て良い"; },
    "All-issues-resolved": function(d) { return "すべての問題が解決されました"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "予期せぬエラーが発生しました。*********************までお問い合わせください"; },
    "Blocked-Device": function(d) { return "ブロックされたデバイス"; },
    Cancel: function(d) { return "キャンセル"; },
    "Change-now": function(d) { return "今すぐ変更する"; },
    "Change-this-compromised-password": function(d) { return "この漏洩したパスワードを変更する"; },
    Close: function(d) { return "閉じる"; },
    "Collapse-all": function(d) { return "すべて折りたたむ"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "インターネットに接続してから、もう一度お試しください。"; },
    "Contact-Support": function(d) { return "サポートにお問い合わせ"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "サポートに連絡し、デフォルトアカウントの問題であることを伝えてください。"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "サポートに連絡し、生成されたパスワード保管庫の問題であることを伝えてください。"; },
    Dismiss: function(d) { return "無視する"; },
    "Don-t-ask-again": function(d) { return "今後は確認しない"; },
    "Download-1Password-8": function(d) { return "1Password 8をダウンロード"; },
    Edit: function(d) { return "編集"; },
    "Existing-items": function(d) { return "既存のアイテム"; },
    "Failed-to-load-item-": function(d) { return "アイテムを読み込めませんでした。"; },
    "Failed-to-save-item-": function(d) { return "アイテムを保存できませんでした。"; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "次回ログイン時にこのアイテムを1Passwordで入力すると、時間を節約できます。"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return d.appName + "へのアクセスを維持するために" + plural(d.count, 0, ja, { "1": number(d.count, "count") + "個の問題", other: number(d.count, "count") + "個の問題" }) + "を修復する"; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return d.appName + "を使用するために" + plural(d.count, 0, ja, { "1": number(d.count, "count") + "個の問題", other: number(d.count, "count") + "個の問題" }) + "を修復する"; },
    "Fix-1-issue-to-access--appName-": function(d) { return d.appName + "にアクセスするために1個の問題を修復する"; },
    "Fix-issue---issue-": function(d) { return "問題の修復：" + d.issue; },
    "Fix-later": function(d) { return "あとで修復する"; },
    "Invalid-one-time-password-secret-": function(d) { return "ワンタイムパスワードのシークレットが無効です。"; },
    Item: function(d) { return "アイテム"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolideが見つからないか、登録されていません"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Kolideが見つかりません。もう一度試してみるか、問題が解決しない場合は管理者に連絡してください。"; },
    "Learn-more-": function(d) { return "詳しく見る……"; },
    Lock: function(d) { return "ロック"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "このサイトのログインを含む1Passwordアカウントがロック解除されていることを確認してください。"; },
    "Managed-by-Trelica": function(d) { return "Trelicaが運営"; },
    "New-Item": function(d) { return "新規アイテム"; },
    "New-Notification": function(d) { return "新規通知"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "次のステップ：SSH エージェントの設定"; },
    "No-items-found-to-sign-in-with-": function(d) { return "サインインするアイテムは見つかりませんでした。"; },
    "No-logins-found": function(d) { return "ログインは見つかりませんでした"; },
    "No-passkeys-found": function(d) { return "パスキーが見つかりませんでした"; },
    Notifications: function(d) { return "通知"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "一度保存すれば、代わりに記憶します。"; },
    Overwrite: function(d) { return "上書き"; },
    "Overwrite-one-time-password-": function(d) { return "ワンタイムパスワードを上書きしますか？"; },
    "Passkey-saved": function(d) { return "パスキーが保存されました"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "1Passwordにサインインして、オンラインでの安全を確保し、パスワードの保存と入力を簡単にすることができます。"; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "この問題を解決する前に、もう一度ロック解除をお試しください"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "ブラウザのツールバーにある1Passwordのアイコンを押して、ロックを解除します。"; },
    Recheck: function(d) { return "再チェック"; },
    "Recheck-device": function(d) { return "デバイスの再チェック"; },
    "Resolved-issue---issue-": function(d) { return "解決済みの問題：" + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "SSHキーが作成されました。このキーをローカルで使用するには、1Password 8で利用できる1Password SSHエージェントを設定してください。"; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "SSHキーが作成されました。このキーをローカルで使用するには、1Password SSHエージェントを設定してください。"; },
    Save: function(d) { return "保存"; },
    "Save--item-": function(d) { return d.item + "を保存する"; },
    "Save-in-1Password-": function(d) { return "1Passwordに保存しますか？"; },
    "Save-item": function(d) { return "アイテムを保存"; },
    "Save-one-time-password": function(d) { return "ワンタイムパスワードを保存する"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "サインイン時に自動入力されるワンタイムパスワードを保存する。"; },
    "Save-passkey": function(d) { return "パスキーを保存"; },
    "Saved-one-time-password": function(d) { return "ワンタイムパスワードを保存しました"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "ブラウザのツールバーにある1Passwordのアイコンを選択して、ロックを解除します。"; },
    "Session-expired-for-tab-": function(d) { return "タブのセッションが切れました。"; },
    "Set-up-SSH-Agent": function(d) { return "SSHエージェントの設定"; },
    "Sign-in": function(d) { return "サインイン"; },
    "Sign-in-to-1Password": function(d) { return "1Passwordにサインイン"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "ブラウザの拡張機能を使うと、ワンクリックでウェブサイトにサインインし、情報を簡単に保存し、新しいパスワードを生成することができます。"; },
    "Sign-in-using--title----subtitle-": function(d) { return d.title + "を使用してサインイン：" + d.subtitle; },
    "Sign-in-with-": function(d) { return "サインインする……"; },
    "Sign-in-with-a-passkey": function(d) { return "パスキーでサインイン"; },
    Snooze: function(d) { return "スヌーズ"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "問題があります。もう一度お試しください。問題が解決しない場合は、管理者に連絡してください。"; },
    "Something-went-wrong": function(d) { return "問題が発生しました"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "ステップ3：新しいログインアイテムを確認または編集してから、保存します。"; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return d.credentialName + "を1Passwordに保存して、" + d.authType + "で" + d.platformName + " CLIを認証します。"; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "このアイテムのワンタイムパスワードは置き換えられます。"; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "パスキーの作成リクエストがタイムアウトしました。"; },
    "To-save--item---unlock-1Password-first-": function(d) { return d.item + "を保存するには、まず1Passwordのロックを解除してください。"; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "パスキーを保存するには、まず1Passwordのロックを解除してください。"; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "パスキーを使用するには、まず1Passwordのロックを解除してください。"; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch IDまたはApple Watch"; },
    "Try-Again": function(d) { return "やり直す"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "1Passwordをロックしてから解除して、もう一度入力してみてください。"; },
    "Unable-to-fill-password": function(d) { return "パスワードを入力できません"; },
    "Unlock-1Password": function(d) { return "1Passwordをロック解除する"; },
    "Unlock-to-Save": function(d) { return "ロック解除して保存"; },
    "Unlock-to-save": function(d) { return "ロック解除して保存"; },
    "Unlocking-1Password-": function(d) { return "1Passwordをロック解除しています…"; },
    Update: function(d) { return "アップデート"; },
    "Update-Existing": function(d) { return "既存のものをアップデート"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "2要素認証に1Passwordを使用する"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "1Passwordを使用して、" + d.authType + "で" + d.platformName + "CLIを認証する。"; },
    "Use-a-security-key-or-another-passkey": function(d) { return "セキュリティキーまたは別のパスキーを使用する"; },
    "View-all": function(d) { return "すべて表示"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "予期せぬエラーが発生したため、提案されたパスワードを使用できません。"; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return plural(d.daysBlocked, 0, ja, { "1": number(d.daysBlocked, "daysBlocked") + "日", other: number(d.daysBlocked, "daysBlocked") + "日" }) + "後にブロックします"; },
    "Will-block-today": function(d) { return "本日ブロックします"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "アカウントは設定で管理できます。"; },
    "You-have-recieved-a-new-notification-": function(d) { return "新しい通知を受け取りました！"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "このパスワードを入力するには、" + d.accountName + "アカウントのロックを解除する必要があります。ロックを解除してもう一度試してください。"; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "管理者は、デバイスのセキュリティを確認するために1Passwordのロックを解除する必要があります。"; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "管理者は、仕事用アプリにアクセスする前に、このデバイスでKolideを設定する必要があります。"; },
    "a--item-": function(d) { return d.item; },
    "an--item-": function(d) { return d.item; },
    "an-Item": function(d) { return "アイテム"; },
    "one-time-password": function(d) { return "ワンタイムパスワード"; },
    "system-authentication": function(d) { return "システム認証"; },
    "autosave-save-type-login": function(d) { return "ログイン"; },
    "autosave-save-type-credit-card": function(d) { return "クレジットカード"; },
    "autosave-save-type-identity": function(d) { return "個人情報"; }
  },
  ko: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " 님이 회원님 장치의 보안을 확인하려면 1Password를 잠금 해제해야 합니다."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " 님의 요구에 따라 업무용 앱에 액세스하기 전에 이 장치에서 Kolide 설정해야 합니다."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, ko, { "1": number(d.count, "count") + "개 문제 남음", other: number(d.count, "count") + "개 문제 남음" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + "이(가) 1Password에 저장됨"; },
    "-item--already-saved": function(d) { return d.item + " 이미 저장됨"; },
    "-item--is-linked-to-": function(d) { return d.item + "의 연결 대상:"; },
    "-item--saved": function(d) { return d.item + " 저장됨"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + "이(가) 다음에 로그인하는 데 사용됨:"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return d.platformName + "용 1Password 셸 플러그인 사용 가능"; },
    "1Password-encountered-a-problem-": function(d) { return "1Password에 문제가 발생했습니다."; },
    "1Password-is-locked": function(d) { return "1Password가 잠겨 있습니다"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "이 웹사이트의 보안 침해로 인하여 회원님의 현재 비밀번호가 위험에 처했습니다."; },
    "About-Kolide": function(d) { return "Kolide에 관하여"; },
    "Add-account": function(d) { return "계정 추가"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "1Password 브라우저 확장 프로그램에 계정 추가"; },
    "All-good": function(d) { return "다 되었습니다"; },
    "All-issues-resolved": function(d) { return "모든 문제가 해결됨"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "예기치 않은 오류가 발생했습니다. *********************으로 문의해 주세요"; },
    "Blocked-Device": function(d) { return "차단된 장치"; },
    Cancel: function(d) { return "취소"; },
    "Change-now": function(d) { return "지금 변경"; },
    "Change-this-compromised-password": function(d) { return "보안이 침해된 이 비밀번호 변경"; },
    Close: function(d) { return "닫기"; },
    "Collapse-all": function(d) { return "모두 축소"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "인터넷에 연결한 다음, 다시 시도하세요."; },
    "Contact-Support": function(d) { return "고객 지원 문의"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "지원팀에 연락하여 도움을 요청하고, 기본 계정에 문제가 있다고 말씀해 주세요."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "지원팀에 연락하여 도움을 요청하고, 생성된 비밀번호 금고에 문제가 있다고 말씀해 주세요."; },
    Dismiss: function(d) { return "취소"; },
    "Don-t-ask-again": function(d) { return "다시 묻지 않기"; },
    "Download-1Password-8": function(d) { return "1Password 8 다운로드"; },
    Edit: function(d) { return "편집"; },
    "Existing-items": function(d) { return "기존 항목"; },
    "Failed-to-load-item-": function(d) { return "항목을 불러오지 못했습니다."; },
    "Failed-to-save-item-": function(d) { return "항목을 저장하지 못했습니다."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "다음 번에 로그인할 때 1Password를 사용하여 이 항목을 채우고 시간을 절약하세요"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return d.appName + "에 대한 액세스 권한을 유지하려면 " + plural(d.count, 0, ko, { "1": number(d.count, "count") + "개 문제", other: number(d.count, "count") + "개 문제" }) + "를 해결하세요"; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return d.appName + "을(를) 사용하려면 " + plural(d.count, 0, ko, { "1": number(d.count, "count") + "개 문제", other: number(d.count, "count") + "개 문제" }) + "를 해결하세요"; },
    "Fix-1-issue-to-access--appName-": function(d) { return d.appName + "에 액세스하려면 1개 문제를 해결하세요"; },
    "Fix-issue---issue-": function(d) { return "문제 해결: " + d.issue; },
    "Fix-later": function(d) { return "나중에 해결"; },
    "Invalid-one-time-password-secret-": function(d) { return "일회용 비밀번호가 잘못되었습니다."; },
    Item: function(d) { return "항목"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide가 없거나 등록되지 않았습니다"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Kolide를 찾을 수 없습니다. 다시 시도하거나 문제가 지속될 경우 관리자에게 문의하세요."; },
    "Learn-more-": function(d) { return "자세히 보기…"; },
    Lock: function(d) { return "잠금"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "이 사이트의 로그인 정보가 포함된 1Password 계정이 잠겨 있지 않은지 확인하세요."; },
    "Managed-by-Trelica": function(d) { return "Trelica가 관리함"; },
    "New-Item": function(d) { return "새 항목"; },
    "New-Notification": function(d) { return "새로운 알림"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "다음 단계: SSH 에이전트 설정"; },
    "No-items-found-to-sign-in-with-": function(d) { return "로그인할 항목을 찾을 수 없습니다."; },
    "No-logins-found": function(d) { return "로그인 정보를 찾을 수 없습니다"; },
    "No-passkeys-found": function(d) { return "패스키를 찾을 수 없습니다"; },
    Notifications: function(d) { return "알림"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "저장하면 이 정보가 기억됩니다."; },
    Overwrite: function(d) { return "덮어쓰기"; },
    "Overwrite-one-time-password-": function(d) { return "일회용 비밀번호를 덮어쓸까요?"; },
    "Passkey-saved": function(d) { return "패스키 저장 완료"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "1Password에 로그인하여 온라인에서 보안을 유지하고 간편하게 비밀번호를 저장하고 자동으로 입력하세요."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "이 문제를 해결하기 전에 다시 잠금 해제해 보세요"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "브라우저의 도구 모음에 있는 1Password 아이콘을 눌러서 잠금 해제합니다."; },
    Recheck: function(d) { return "다시 검사"; },
    "Recheck-device": function(d) { return "장치 다시 검사"; },
    "Resolved-issue---issue-": function(d) { return "해결된 문제: " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "SSH 키가 생성되었습니다. 로컬에서 이 키를 사용하려면 1Password 8에서 제공되는 1Password SSH 에이전트를 설정하세요."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "SSH 키가 생성되었습니다. 로컬에서 이 키를 사용하려면 1Password SSH 에이전트를 설정하세요."; },
    Save: function(d) { return "저장"; },
    "Save--item-": function(d) { return d.item + " 저장"; },
    "Save-in-1Password-": function(d) { return "1Password에 저장할까요?"; },
    "Save-item": function(d) { return "항목 저장"; },
    "Save-one-time-password": function(d) { return "일회용 비밀번호 저장"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "로그인 과정에서 자동으로 입력되는 일회용 비밀번호를 저장합니다."; },
    "Save-passkey": function(d) { return "패스키 저장"; },
    "Saved-one-time-password": function(d) { return "일회용 비밀번호 저장 완료"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "브라우저의 도구 모음에 있는 1Password 아이콘을 선택하여 잠금 해제합니다."; },
    "Session-expired-for-tab-": function(d) { return "탭에 대한 세션이 만료되었습니다."; },
    "Set-up-SSH-Agent": function(d) { return "SSH 에이전트 설정"; },
    "Sign-in": function(d) { return "로그인"; },
    "Sign-in-to-1Password": function(d) { return "1Password에 로그인"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "브라우저 확장 프로그램을 사용하여 한 번 클릭으로 웹사이트에 로그인하고, 간편하게 정보를 저장하며, 새로운 비밀번호를 생성하세요."; },
    "Sign-in-using--title----subtitle-": function(d) { return d.title + ": " + d.subtitle + "을(를) 통하여 로그인"; },
    "Sign-in-with-": function(d) { return "로그인 방법…"; },
    "Sign-in-with-a-passkey": function(d) { return "패스키로 로그인"; },
    Snooze: function(d) { return "다시 알림"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "문제가 발생했습니다. 다시 시도하세요. 문제가 지속될 경우 관리자에게 문의하세요"; },
    "Something-went-wrong": function(d) { return "문제가 발생했습니다"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "3단계: 새로운 로그인 항목을 검토 또는 편집한 다음, 저장합니다."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "회원님의 " + d.credentialName + "을(를) 1Password에 저장하고 " + d.authType + "을(를) 통하여 " + d.platformName + " CLI를 인증합니다."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "이 항목의 일회용 비밀번호가 대체됩니다."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "패스키 생성 요청 시간을 초과했습니다."; },
    "To-save--item---unlock-1Password-first-": function(d) { return d.item + "을(를) 저장하려면 먼저 1Password를 잠금 해제하세요."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "패스키를 저장하려면 먼저 1Password를 잠금 해제하세요."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "패스키를 사용하려면 먼저 1Password를 잠금 해제하세요."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID 또는 Apple Watch"; },
    "Try-Again": function(d) { return "다시 시도"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "1Password를 잠근 다음, 잠금을 해제하고 다시 채워 보세요."; },
    "Unable-to-fill-password": function(d) { return "비밀번호를 채울 수 없습니다"; },
    "Unlock-1Password": function(d) { return "1Password 잠금 해제"; },
    "Unlock-to-Save": function(d) { return "잠금 해제하고 저장"; },
    "Unlock-to-save": function(d) { return "잠금 해제하고 저장"; },
    "Unlocking-1Password-": function(d) { return "1Password를 잠금 해제하는 중…"; },
    Update: function(d) { return "업데이트"; },
    "Update-Existing": function(d) { return "기존 정보 업데이트"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "2단계 인증에 1Password 사용"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "1Password를 사용하여 " + d.authType + "을(를) 통해 " + d.platformName + " CLI를 인증합니다."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "보안 키 또는 다른 패스키 사용"; },
    "View-all": function(d) { return "모두 보기"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "예기치 않은 오류가 발생하여 제안된 비밀번호를 사용할 수 없습니다."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return plural(d.daysBlocked, 0, ko, { "1": number(d.daysBlocked, "daysBlocked") + "일", other: number(d.daysBlocked, "daysBlocked") + "일" }) + " 후 차단됨"; },
    "Will-block-today": function(d) { return "오늘 차단됨"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "설정에서 계정을 관리할 수 있습니다."; },
    "You-have-recieved-a-new-notification-": function(d) { return "새로운 알림을 받았습니다!"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "이 비밀번호를 채우려면 회원님의 " + d.accountName + " 계정을 잠금 해제해야 합니다. 잠금을 해제하고 다시 시도하세요."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "관리자가 회원님 장치의 보안을 확인하려면 1Password를 잠금 해제해야 합니다."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "관리자의 요구에 따라 업무용 앱에 액세스하기 전에 이 장치에서 Kolide 설정해야 합니다."; },
    "a--item-": function(d) { return "a " + d.item; },
    "an--item-": function(d) { return "an " + d.item; },
    "an-Item": function(d) { return "항목"; },
    "one-time-password": function(d) { return "일회용 비밀번호"; },
    "system-authentication": function(d) { return "시스템 인증"; },
    "autosave-save-type-login": function(d) { return "로그인 정보"; },
    "autosave-save-type-credit-card": function(d) { return "신용 카드"; },
    "autosave-save-type-identity": function(d) { return "신원 정보"; }
  },
  nl: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " moet 1Password ontgrendeld hebben om de beveiliging van je apparaat na te gaan."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " vereist dat je Kolide op dit apparaat installeert voordat je werkapps opent."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, nl, { one: "", "1": "nog " + number(d.count, "count") + " probleem", other: "nog " + number(d.count, "count") + " problemen" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " opgeslagen in 1Password"; },
    "-item--already-saved": function(d) { return d.item + " al opgeslagen"; },
    "-item--is-linked-to-": function(d) { return d.item + " is gekoppeld aan:"; },
    "-item--saved": function(d) { return d.item + " opgeslagen"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return "Er is met " + d.provider + " ingelogd bij:"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "Shell-plugin voor " + d.platformName + " beschikbaar voor 1Password"; },
    "1Password-encountered-a-problem-": function(d) { return "Er is een fout opgetreden in 1Password."; },
    "1Password-is-locked": function(d) { return "1Password is vergrendeld"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "Door een beveiligingslek op deze website is je huidige wachtwoord in gevaar gekomen."; },
    "About-Kolide": function(d) { return "Over Kolide"; },
    "Add-account": function(d) { return "Account toevoegen"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "Account toevoegen aan de browser-extensie van 1Password"; },
    "All-good": function(d) { return "Alles in orde"; },
    "All-issues-resolved": function(d) { return "Alle problemen opgelost"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Er is een onverwachte fout opgetreden. Neem contact <NAME_EMAIL>"; },
    "Blocked-Device": function(d) { return "Geblokkeerd apparaat"; },
    Cancel: function(d) { return "Annuleren"; },
    "Change-now": function(d) { return "Nu wijzigen"; },
    "Change-this-compromised-password": function(d) { return "Wijzig dit gecomprimitteerde wachtwoord"; },
    Close: function(d) { return "Sluiten"; },
    "Collapse-all": function(d) { return "Alles inklappen"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "Zorg voor een werkende internetverbinding en probeer het opnieuw."; },
    "Contact-Support": function(d) { return "Contact met de klantenservice"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "Neem contact op met de klantenservice en vertel hen dat er een probleem is met het standaardaccount."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "Neem contact op met de klantenservice en vertel hen dat er een probleem is met de gegenereerde wachtwoordkluis."; },
    Dismiss: function(d) { return "Negeren"; },
    "Don-t-ask-again": function(d) { return "Niet meer vragen"; },
    "Download-1Password-8": function(d) { return "1Password 8 downloaden"; },
    Edit: function(d) { return "Aanpassen"; },
    "Existing-items": function(d) { return "Bestaande items"; },
    "Failed-to-load-item-": function(d) { return "Laden van item mislukt."; },
    "Failed-to-save-item-": function(d) { return "Bewaren van item mislukt."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "Vul dit item de volgende keer met 1Password in zodat je tijd bespaart met inloggen"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "Los " + plural(d.count, 0, nl, { one: "", "1": number(d.count, "count") + " probleem", other: number(d.count, "count") + " problemen" }) + " op om " + d.appName + " te blijven gebruiken"; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "Los " + plural(d.count, 0, nl, { one: "", "1": number(d.count, "count") + " probleem", other: number(d.count, "count") + " problemen" }) + " op om " + d.appName + " te gebruiken"; },
    "Fix-1-issue-to-access--appName-": function(d) { return "Los 1 probleem op om " + d.appName + " te openen"; },
    "Fix-issue---issue-": function(d) { return "Los probleem op: " + d.issue; },
    "Fix-later": function(d) { return "Later oplossen"; },
    "Invalid-one-time-password-secret-": function(d) { return "Ongeldig éénmalig wachtwoord."; },
    Item: function(d) { return "Item"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide ontbreekt of is niet geregistreerd"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Kolide niet gevonden. Probeer het opnieuw of neem contact op met je beheerder als het probleem zich blijft voordoen."; },
    "Learn-more-": function(d) { return "Kom meer te weten..."; },
    Lock: function(d) { return "Vergrendelen"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "Zorg ervoor dat 1Password-accounts met inloggegevens voor deze website zijn ontgrendeld."; },
    "Managed-by-Trelica": function(d) { return "Beheerd door Trelica"; },
    "New-Item": function(d) { return "Nieuw item"; },
    "New-Notification": function(d) { return "Nieuwe melding"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "Volgende stap: SSH Agent instellen"; },
    "No-items-found-to-sign-in-with-": function(d) { return "Geen items gevonden om mee in te loggen."; },
    "No-logins-found": function(d) { return "Geen inloggegevens gevonden"; },
    "No-passkeys-found": function(d) { return "Geen passkeys gevonden"; },
    Notifications: function(d) { return "Meldingen"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Sla het op en wij onthouden het voortaan voor je."; },
    Overwrite: function(d) { return "Overschrijven"; },
    "Overwrite-one-time-password-": function(d) { return "Eenmalig wachtwoord overschrijven?"; },
    "Passkey-saved": function(d) { return "Passkey opgeslagen"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "Log in bij 1Password om veiliger online te gaan en het opslaan en invullen van je wachtwoorden makkelijker te maken."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "Probeer opnieuw te ontgrendelen voordat je dit probleem oplost"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Druk op het 1Password-icoon in de werkbalk van je browser om te ontgrendelen."; },
    Recheck: function(d) { return "Opnieuw controleren"; },
    "Recheck-device": function(d) { return "Apparaat opnieuw controleren"; },
    "Resolved-issue---issue-": function(d) { return "Verholpen probleem: " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "SSH-key aangemaakt. Om deze sleutel lokaal te gebruiken, stel je SSH Agent van 1Password in (beschikbaar op 1Password 8)."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "SSH-key aangemaakt. Om deze sleutel lokaal te gebruiken, stel je eerst de SSH Agent van 1Password in."; },
    Save: function(d) { return "Opslaan"; },
    "Save--item-": function(d) { return d.item + " opslaan"; },
    "Save-in-1Password-": function(d) { return "Opslaan in 1Password?"; },
    "Save-item": function(d) { return "Item opslaan"; },
    "Save-one-time-password": function(d) { return "Eenmalig wachtwoord opslaan"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "Eenmalige wachtwoorden opslaan die automatisch worden ingevuld tijdens het inloggen."; },
    "Save-passkey": function(d) { return "Passkey opslaan"; },
    "Saved-one-time-password": function(d) { return "Eenmalige wachtwoord opgeslagen"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Selecteer het 1Password-icoon in de werkbalk van je browser om te ontgrendelen."; },
    "Session-expired-for-tab-": function(d) { return "Sessie verlopen voor tabblad."; },
    "Set-up-SSH-Agent": function(d) { return "SSH Agent instellen"; },
    "Sign-in": function(d) { return "Inloggen"; },
    "Sign-in-to-1Password": function(d) { return "Log in bij 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "Log dankzij de browser-extensie met één klik in op websites, sla eenvoudig je informatie op, en maak nieuwe wachtwoorden aan."; },
    "Sign-in-using--title----subtitle-": function(d) { return "Inloggen met " + d.title + ": " + d.subtitle; },
    "Sign-in-with-": function(d) { return "Inloggen met…"; },
    "Sign-in-with-a-passkey": function(d) { return "Inloggen met een passkey"; },
    Snooze: function(d) { return "Sluimeren"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "Er is iets misgegaan, probeer het opnieuw. Blijft het probleem zich voordoen, neem dan contact op met je beheerder"; },
    "Something-went-wrong": function(d) { return "Er ging iets mis"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Stap 3: bekijk en wijzig eventueel je nieuwe inlogitem. Sla het vervolgens op."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "Sla je " + d.credentialName + " op in 1Password en verifieer de CLI van " + d.platformName + " met " + d.authType + "."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "Het eenmalige wachtwoord in dit item wordt vervangen."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "Time-out van het verzoek om een passkey aan te maken."; },
    "To-save--item---unlock-1Password-first-": function(d) { return "Om " + d.item + " op te slaan, moet je eerst 1Password ontgrendelen."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "Ontgrendel eerst 1Password om een passkey op te slaan."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "Ontgrendel eerst 1Password om je passkey te gebruiken."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID of Apple Watch"; },
    "Try-Again": function(d) { return "Opnieuw proberen"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "Vergrendel en ontgrendel 1Password en vul daarna opnieuw aan."; },
    "Unable-to-fill-password": function(d) { return "Wachtwoord automatisch aanvullen mislukt"; },
    "Unlock-1Password": function(d) { return "1Password ontgrendelen"; },
    "Unlock-to-Save": function(d) { return "Ontgrendel om op te slaan"; },
    "Unlock-to-save": function(d) { return "Ontgrendel om op te slaan"; },
    "Unlocking-1Password-": function(d) { return "1Password wordt ontgrendeld…"; },
    Update: function(d) { return "Bijwerken"; },
    "Update-Existing": function(d) { return "Bestaande bijwerken"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "Gebruik 1Password voor tweefactorauthenticatie"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "Gebruik 1Password om de CLI van " + d.platformName + " te verifiëren met " + d.authType + "."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "Gebruik een beveiligingssleutel of een andere passkey"; },
    "View-all": function(d) { return "Alles bekijken"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "Door een onverwachte fout konden we het voorgestelde wachtwoord niet gebruiken."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "Wordt geblokkeerd over " + plural(d.daysBlocked, 0, nl, { one: "", "1": number(d.daysBlocked, "daysBlocked") + " dag", other: number(d.daysBlocked, "daysBlocked") + " dagen" }); },
    "Will-block-today": function(d) { return "Wordt vandaag geblokkeerd"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "Je kunt je accounts beheren onder Instellingen."; },
    "You-have-recieved-a-new-notification-": function(d) { return "Je hebt een nieuwe melding!"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "Je " + d.accountName + "-account moet worden ontgrendeld om dit wachtwoord in te vullen. Ontgrendel je account en probeer het opnieuw."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Je beheerder moet 1Password ontgrendeld hebben om de beveiliging van je apparaat na te gaan."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Je beheerder vereist dat je Kolide op dit apparaat installeert voordat je werkapps opent."; },
    "a--item-": function(d) { return "een " + d.item; },
    "an--item-": function(d) { return "een " + d.item; },
    "an-Item": function(d) { return "een item"; },
    "one-time-password": function(d) { return "eenmalig wachtwoord"; },
    "system-authentication": function(d) { return "systeemverificatie"; },
    "autosave-save-type-login": function(d) { return "Inloggegevens"; },
    "autosave-save-type-credit-card": function(d) { return "Creditcard"; },
    "autosave-save-type-identity": function(d) { return "Identiteit"; }
  },
  pt: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " precisa do 1Password desbloqueado para verificar a segurança do seu dispositivo."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " requer que você configure o Kolide neste dispositivo antes de acessar os aplicativos de trabalho."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, pt, { "1": number(d.count, "count") + " problema permanece", other: number(d.count, "count") + " problemas permanecem" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " salvo no 1Password"; },
    "-item--already-saved": function(d) { return d.item + " já salvo"; },
    "-item--is-linked-to-": function(d) { return d.item + " está vinculado a:"; },
    "-item--saved": function(d) { return d.item + " salvo"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " foi usado para fazer login em:"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "Plugin de shell do 1Password disponível para " + d.platformName; },
    "1Password-encountered-a-problem-": function(d) { return "O 1Password encontrou um problema."; },
    "1Password-is-locked": function(d) { return "O 1Password está bloqueado"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "Uma violação de segurança neste site colocou sua senha atual em risco."; },
    "About-Kolide": function(d) { return "Sobre Kolide"; },
    "Add-account": function(d) { return "Adicionar conta"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "Adicionar conta à extensão do navegador 1Password"; },
    "All-good": function(d) { return "Tudo certo"; },
    "All-issues-resolved": function(d) { return "Todos os problemas resolvidos"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Ocorreu um erro inesperado. Entre em <NAME_EMAIL>"; },
    "Blocked-Device": function(d) { return "Dispositivo bloqueado"; },
    Cancel: function(d) { return "Cancelar"; },
    "Change-now": function(d) { return "Mudar agora"; },
    "Change-this-compromised-password": function(d) { return "Alterar esta senha comprometida"; },
    Close: function(d) { return "Fechar"; },
    "Collapse-all": function(d) { return "Recolher tudo"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "Conecte-se à internet e tente novamente."; },
    "Contact-Support": function(d) { return "Entre em contato com o suporte"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "Entre em contato com o suporte para obter ajuda e diga que o problema é com a conta padrão."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "Entre em contato com o suporte para obter ajuda e informe que há um problema com o cofre de senhas gerado."; },
    Dismiss: function(d) { return "Liberar"; },
    "Don-t-ask-again": function(d) { return "Não pergunte novamente"; },
    "Download-1Password-8": function(d) { return "Baixe o 1Password 8"; },
    Edit: function(d) { return "Editar"; },
    "Existing-items": function(d) { return "Itens existentes"; },
    "Failed-to-load-item-": function(d) { return "Falha ao carregar o item."; },
    "Failed-to-save-item-": function(d) { return "Falha ao salvar o item."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "Preencha este item com 1Password na próxima vez que fizer login para economizar tempo"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "Corrigir " + plural(d.count, 0, pt, { "1": number(d.count, "count") + " problema", other: number(d.count, "count") + " problemas" }) + " para manter o acesso de " + d.appName; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "Corrigir " + plural(d.count, 0, pt, { "1": number(d.count, "count") + " problema", other: number(d.count, "count") + " problemas" }) + " para manter o acesso de " + d.appName; },
    "Fix-1-issue-to-access--appName-": function(d) { return "Corrigir 1 problema para acessar " + d.appName; },
    "Fix-issue---issue-": function(d) { return "Corrigir problema: " + d.issue; },
    "Fix-later": function(d) { return "Corrigir mais tarde"; },
    "Invalid-one-time-password-secret-": function(d) { return "Senha secreta de uso único inválida."; },
    Item: function(d) { return "Item"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide está faltando ou não está registrado"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Kolide não foi encontrado. Tente novamente ou entre em contato com o administrador se o problema persistir."; },
    "Learn-more-": function(d) { return "Saber mais…"; },
    Lock: function(d) { return "Bloquear"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "Certifique-se de que as contas do 1Password que contêm logins para este site estejam desbloqueadas."; },
    "Managed-by-Trelica": function(d) { return "Gerenciado por Trelica"; },
    "New-Item": function(d) { return "Novo Item"; },
    "New-Notification": function(d) { return "Nova notificação"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "Próximo passo: Configurar o agente SSH"; },
    "No-items-found-to-sign-in-with-": function(d) { return "Nenhum item encontrado para fazer login."; },
    "No-logins-found": function(d) { return "Nenhum login encontrado"; },
    "No-passkeys-found": function(d) { return "Nenhuma senha encontrada"; },
    Notifications: function(d) { return "Notificações"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Depois de salvo, nós o lembraremos para você."; },
    Overwrite: function(d) { return "Sobrescrever"; },
    "Overwrite-one-time-password-": function(d) { return "Substituir senha de uso único?"; },
    "Passkey-saved": function(d) { return "Senha salva"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "Entre no 1Password para se manter seguro on-line e facilitar o salvamento e o preenchimento de suas senhas."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "Tente desbloquear novamente antes de corrigir este problema"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Pressione o ícone 1Password na barra de ferramentas do seu navegador para desbloquear."; },
    Recheck: function(d) { return "Verifique novamente"; },
    "Recheck-device": function(d) { return "Verifique novamente o dispositivo"; },
    "Resolved-issue---issue-": function(d) { return "Problema resolvido: " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "Chave SSH criada. Para usar essa chave localmente, configure o 1Password SSH Agent, disponível no 1Password 8."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "Chave SSH criada. Para usar essa chave localmente, configure o agente 1Password SSH."; },
    Save: function(d) { return "Salvar"; },
    "Save--item-": function(d) { return "Salvar " + d.item; },
    "Save-in-1Password-": function(d) { return "Salvar no 1Password?"; },
    "Save-item": function(d) { return "Salvar item"; },
    "Save-one-time-password": function(d) { return "Salvar senha de uso único"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "Salve senhas de uso único que serão preenchidas automaticamente durante o login."; },
    "Save-passkey": function(d) { return "Salvar senha"; },
    "Saved-one-time-password": function(d) { return "Senha de uso único salva"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Selecione o ícone 1Password na barra de ferramentas do seu navegador para desbloquear."; },
    "Session-expired-for-tab-": function(d) { return "Sessão expirada para a guia."; },
    "Set-up-SSH-Agent": function(d) { return "Configurar agente SSH"; },
    "Sign-in": function(d) { return "Entrar"; },
    "Sign-in-to-1Password": function(d) { return "Entrar no 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "Entre em sites com um clique, salve facilmente suas informações e gere novas senhas com a extensão do navegador."; },
    "Sign-in-using--title----subtitle-": function(d) { return "Entre usando " + d.title + ": " + d.subtitle; },
    "Sign-in-with-": function(d) { return "Entrar com…"; },
    "Sign-in-with-a-passkey": function(d) { return "Entre com uma senha"; },
    Snooze: function(d) { return "Soneca"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "Algo não está certo. Tente novamente. Se o problema persistir, entre em contato com seu administrador"; },
    "Something-went-wrong": function(d) { return "Algo deu errado"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Etapa 3: revise ou edite seu novo item de login e salve-o."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "Armazene sua " + d.credentialName + " no 1Password e autentique a CLI " + d.platformName + " com " + d.authType + "."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "A senha de uso único neste item será substituída."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "A solicitação para criar uma chave de acesso expirou."; },
    "To-save--item---unlock-1Password-first-": function(d) { return "Para salvar " + d.item + ", desbloqueie o 1Password primeiro."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "Para salvar uma senha, desbloqueie o 1Password primeiro."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "Para usar sua senha, desbloqueie o 1Password primeiro."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID ou Apple Watch"; },
    "Try-Again": function(d) { return "Tentar novamente"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "Tente bloquear e desbloquear o 1Password para preencher novamente."; },
    "Unable-to-fill-password": function(d) { return "Não é possível preencher a senha"; },
    "Unlock-1Password": function(d) { return "Desbloquear o 1Password"; },
    "Unlock-to-Save": function(d) { return "Desbloqueie para salvar"; },
    "Unlock-to-save": function(d) { return "Desbloqueie para salvar"; },
    "Unlocking-1Password-": function(d) { return "Desbloqueando o 1Password…"; },
    Update: function(d) { return "Atualizar"; },
    "Update-Existing": function(d) { return "Atualizar existente"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "Use o 1Password para autenticação de dois fatores"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "Use o 1Password para autenticar a CLI " + d.platformName + " com " + d.authType + "."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "Use uma chave de segurança ou outra chave de acesso"; },
    "View-all": function(d) { return "Ver tudo"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "Não podemos usar a senha sugerida porque ocorreu um erro inesperado."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "Bloqueará em " + plural(d.daysBlocked, 0, pt, { "1": number(d.daysBlocked, "daysBlocked") + " dia", other: number(d.daysBlocked, "daysBlocked") + " dias" }); },
    "Will-block-today": function(d) { return "Bloqueará hoje"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "Você pode gerenciar suas contas em Configurações."; },
    "You-have-recieved-a-new-notification-": function(d) { return "Você recebeu uma nova notificação!"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "Sua conta " + d.accountName + " precisa ser desbloqueada para preencher esta senha. Desbloqueie-a e tente novamente."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Seu administrador precisa desbloquear o 1Password para verificar a segurança do seu dispositivo."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Seu administrador exige que você configure o Kolide neste dispositivo antes de acessar os aplicativos de trabalho."; },
    "a--item-": function(d) { return "um " + d.item; },
    "an--item-": function(d) { return "um " + d.item; },
    "an-Item": function(d) { return "um item"; },
    "one-time-password": function(d) { return "senha de uso único"; },
    "system-authentication": function(d) { return "autenticação do sistema"; },
    "autosave-save-type-login": function(d) { return "Login"; },
    "autosave-save-type-credit-card": function(d) { return "Cartão de crédito"; },
    "autosave-save-type-identity": function(d) { return "Identidade"; }
  },
  ru: {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Для аккаунта " + d.accountName + " нужно, чтобы 1Password был разблокирован для проверки настроек безопасности вашего устройства."; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Пользователь " + d.accountName + " требует, чтобы вы настроили Kolide на этом устройстве перед тем, как получить доступ к рабочим приложениям."; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, ru, { "1": number(d.count, "count") + " ошибка остается", other: number(d.count, "count") + " ошибки(-ок) остаются" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + ": сохранено в 1Password"; },
    "-item--already-saved": function(d) { return d.item + ": уже сохранено"; },
    "-item--is-linked-to-": function(d) { return "Элемент " + d.item + " привязан к:"; },
    "-item--saved": function(d) { return d.item + ": сохранено"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " был использован для входа в аккаунт:"; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "Плагин 1Password Shell для " + d.platformName; },
    "1Password-encountered-a-problem-": function(d) { return "В 1Password возникла проблема."; },
    "1Password-is-locked": function(d) { return "1Password заблокирован"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "Нарушение безопасности на этом веб-сайте скомпрометировало ваш текущий пароль."; },
    "About-Kolide": function(d) { return "О Kolide"; },
    "Add-account": function(d) { return "Добавить аккаунт"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "Добавить аккаунт в расширение 1Password для браузера"; },
    "All-good": function(d) { return "Все хорошо"; },
    "All-issues-resolved": function(d) { return "Все ошибки исправлены"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Возникла непредвиденная ошибка. Пожалуйста, свяжитесь с нами: <EMAIL>"; },
    "Blocked-Device": function(d) { return "Заблокированное устройство"; },
    Cancel: function(d) { return "Отменить"; },
    "Change-now": function(d) { return "Изменить сейчас"; },
    "Change-this-compromised-password": function(d) { return "Изменить этот скомпрометированный пароль"; },
    Close: function(d) { return "Закрыть"; },
    "Collapse-all": function(d) { return "Свернуть все"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "Подключитесь к Интернету и попробуйте снова."; },
    "Contact-Support": function(d) { return "Обратиться в службу поддержки"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "Обратитесь в службу поддержки за помощью и сообщите им, что проблема связана с аккаунтом по умолчанию."; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "Обратитесь в службу поддержки за помощью и сообщите им, что проблема связана с сейфом, для которого был сгенерирован пароль."; },
    Dismiss: function(d) { return "Скрыть"; },
    "Don-t-ask-again": function(d) { return "Больше не спрашивать"; },
    "Download-1Password-8": function(d) { return "Скачать 1Password 8"; },
    Edit: function(d) { return "Изменить"; },
    "Existing-items": function(d) { return "Существующие элементы"; },
    "Failed-to-load-item-": function(d) { return "Не удалось загрузить элемент."; },
    "Failed-to-save-item-": function(d) { return "Не удалось сохранить элемент."; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "Заполните этот элемент с помощью 1Password при следующем входе в аккаунт, чтобы сэкономить время"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "Исправить " + plural(d.count, 0, ru, { "1": number(d.count, "count") + " ошибку", other: number(d.count, "count") + " ошибки(-ок)" }) + ", чтобы сохранить доступ к " + d.appName; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "Исправить " + plural(d.count, 0, ru, { "1": number(d.count, "count") + " ошибку", other: number(d.count, "count") + " ошибки (ошибок)" }) + ", чтобы использовать " + d.appName; },
    "Fix-1-issue-to-access--appName-": function(d) { return "Исправить 1 ошибку для получения доступа к " + d.appName; },
    "Fix-issue---issue-": function(d) { return "Исправить ошибку: " + d.issue; },
    "Fix-later": function(d) { return "Исправить позже"; },
    "Invalid-one-time-password-secret-": function(d) { return "Недействительный одноразовый пароль."; },
    Item: function(d) { return "Элемент"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide отсутствует или еще не был зарегистрирован"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "Kolide не был найден. Попробуйте еще раз или обратитесь к администратору, если проблема не решается."; },
    "Learn-more-": function(d) { return "Подробнее…"; },
    Lock: function(d) { return "Заблокировать"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "Убедитесь, что разблокированы аккаунты 1Password с данными для входа на этом сайте."; },
    "Managed-by-Trelica": function(d) { return "Управляется Trelica"; },
    "New-Item": function(d) { return "Новый элемент"; },
    "New-Notification": function(d) { return "Новое уведомление"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "Следующий шаг: установите SSH Agent"; },
    "No-items-found-to-sign-in-with-": function(d) { return "Элементов для входа в аккаунт не найдено."; },
    "No-logins-found": function(d) { return "Данные для входа не найдены"; },
    "No-passkeys-found": function(d) { return "Passkey не найдены"; },
    Notifications: function(d) { return "Уведомления"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "После сохранения мы запомним его за вас."; },
    Overwrite: function(d) { return "Переписать"; },
    "Overwrite-one-time-password-": function(d) { return "Переписать одноразовый пароль?"; },
    "Passkey-saved": function(d) { return "Passkey сохранен"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "Пожалуйста, войдите в аккаунт 1Password, чтобы оставаться в безопасности онлайн и легко сохранять и заполнять свои пароли."; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "Попробуйте разблокировать еще раз перед тем, как исправить эту ошибку"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Нажмите на значок 1Password на панели инструментов своего браузера, чтобы разблокировать."; },
    Recheck: function(d) { return "Проверить заново"; },
    "Recheck-device": function(d) { return "Заново проверить устройство"; },
    "Resolved-issue---issue-": function(d) { return "Ошибка исправлена: " + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "SSH-ключ создан. Чтобы локально использовать этот ключ, установите 1Password SSH Agent, доступный для 1Password 8."; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "SSH-ключ создан. Чтобы локально использовать этот ключ, установите 1Password SSH Agent."; },
    Save: function(d) { return "Сохранить"; },
    "Save--item-": function(d) { return "Сохранить элемент " + d.item; },
    "Save-in-1Password-": function(d) { return "Сохранить в 1Password?"; },
    "Save-item": function(d) { return "Сохранить элемент"; },
    "Save-one-time-password": function(d) { return "Сохранить одноразовый пароль"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "Сохраняйте одноразовые пароли, которые будут автоматически заполнены для входа в аккаунты."; },
    "Save-passkey": function(d) { return "Сохранить passkey"; },
    "Saved-one-time-password": function(d) { return "Одноразовый пароль сохранен"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "Выберите значок 1Password на панели инструментов своего браузера, чтобы разблокировать."; },
    "Session-expired-for-tab-": function(d) { return "Срок сеанса для вкладки истек."; },
    "Set-up-SSH-Agent": function(d) { return "Установить SSH Agent"; },
    "Sign-in": function(d) { return "Войти в аккаунт"; },
    "Sign-in-to-1Password": function(d) { return "Войти в 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "Входите в аккаунты на веб-сайтах в один клик, легко сохраняйте свои данные и создавайте новые пароли с помощью расширения для браузера."; },
    "Sign-in-using--title----subtitle-": function(d) { return "Войдите в аккаунт, используя " + d.title + ": " + d.subtitle; },
    "Sign-in-with-": function(d) { return "Войти в аккаунт с…"; },
    "Sign-in-with-a-passkey": function(d) { return "Войти в аккаунт с passkey"; },
    Snooze: function(d) { return "Напомнить позже"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "Что-то пошло не так. Пожалуйста, попробуйте еще раз. Если проблема не разрешается, обратитесь к своему администратору"; },
    "Something-went-wrong": function(d) { return "Произошла ошибка"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Шаг 3: Просмотрите или измените новый элемент для входа в аккаунт, а затем сохраните его."; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "Храните свои данные " + d.credentialName + " в 1Password для аутентификации " + d.platformName + " CLI с помощью " + d.authType + "."; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "Будет заменен одноразовый пароль в этом элементе."; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "Время ожидания запроса на создание Passkey истекло."; },
    "To-save--item---unlock-1Password-first-": function(d) { return "Чтобы сохранить " + d.item + ", сначала разблокируйте 1Password."; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "Чтобы сохранить passkey, сначала разблокируйте 1Password."; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "Чтобы использовать passkey, сначала разблокируйте 1Password."; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID или Apple Watch"; },
    "Try-Again": function(d) { return "Повторить"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "Чтобы заново заполнить, попробуйте заблокировать, а затем разблокировать 1Password."; },
    "Unable-to-fill-password": function(d) { return "Невозможно заполнить пароль"; },
    "Unlock-1Password": function(d) { return "Разблокировать 1Password"; },
    "Unlock-to-Save": function(d) { return "Разблокировать, чтобы сохранить"; },
    "Unlock-to-save": function(d) { return "Разблокировать, чтобы сохранить"; },
    "Unlocking-1Password-": function(d) { return "Происходит разблокировка 1Password…"; },
    Update: function(d) { return "Обновить"; },
    "Update-Existing": function(d) { return "Изменить существующие"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "Используйте 1Password для двухфакторной аутентификации"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "Используйте 1Password для аутентификации " + d.platformName + " CLI с помощью " + d.authType + "."; },
    "Use-a-security-key-or-another-passkey": function(d) { return "Используйте ключ безопасности или другой passkey"; },
    "View-all": function(d) { return "Смотреть все"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "Мы не можем использовать предложенный пароль, потому что произошла непредвиденная ошибка."; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "Будет заблокировано через " + plural(d.daysBlocked, 0, ru, { "1": number(d.daysBlocked, "daysBlocked") + " день", other: number(d.daysBlocked, "daysBlocked") + " дня (дней)" }); },
    "Will-block-today": function(d) { return "Будет заблокировано сегодня"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "Вы можете управлять своими аккаунтами в Настройках."; },
    "You-have-recieved-a-new-notification-": function(d) { return "Вы получили новое уведомление!"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "Чтобы заполнить этот пароль, ваш аккаунт " + d.accountName + " должен быть разблокирован. Разблокируйте его и попробуйте еще раз."; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "Вашему администратору нужно, чтобы 1Password был разблокирован для проверки настроек безопасности вашего устройства."; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "Ваш администратор требует, чтобы вы настроили Kolide на этом устройстве перед тем, как получить доступ к рабочим приложениям."; },
    "a--item-": function(d) { return d.item; },
    "an--item-": function(d) { return d.item; },
    "an-Item": function(d) { return "элемент"; },
    "one-time-password": function(d) { return "одноразовый пароль"; },
    "system-authentication": function(d) { return "аутентификация системы"; },
    "autosave-save-type-login": function(d) { return "Данные для входа"; },
    "autosave-save-type-credit-card": function(d) { return "Банковская карта"; },
    "autosave-save-type-identity": function(d) { return "Личность"; }
  },
  "zh-CN": {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " 需要解锁 1Password 以检查设备的安全性。"; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " 要求您在此设备上设置 Kolide，才能访问工作应用。"; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, en, { "1": "剩余 " + number(d.count, "count") + " 个问题", other: "剩余 " + number(d.count, "count") + " 个问题" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " 已保存在 1Password 中"; },
    "-item--already-saved": function(d) { return d.item + " 已保存"; },
    "-item--is-linked-to-": function(d) { return d.item + " 已链接到："; },
    "-item--saved": function(d) { return d.item + " 已保存"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " 被用于登录："; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "适用于 " + d.platformName + " 的 1Password 外壳插件可用"; },
    "1Password-encountered-a-problem-": function(d) { return "1Password 遇到问题。"; },
    "1Password-is-locked": function(d) { return "1Password 被锁定"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "该网站的安全漏洞已使您的当前密码面临风险。"; },
    "About-Kolide": function(d) { return "关于 Kolide"; },
    "Add-account": function(d) { return "添加帐户"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "添加帐户至 1Password 浏览器扩展"; },
    "All-good": function(d) { return "一切正常"; },
    "All-issues-resolved": function(d) { return "全部问题已解决"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "发生意外错误。请联系 <EMAIL>"; },
    "Blocked-Device": function(d) { return "阻止的设备"; },
    Cancel: function(d) { return "取消"; },
    "Change-now": function(d) { return "立即更改"; },
    "Change-this-compromised-password": function(d) { return "更改有泄露风险的密码"; },
    Close: function(d) { return "关闭"; },
    "Collapse-all": function(d) { return "全部收起"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "连接到互联网并再试一次。"; },
    "Contact-Support": function(d) { return "联系支持"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "联系支持人员寻求帮助，并告诉对方遇到了默认帐户的问题。"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "联系支持人员寻求帮助，并告诉对方遇到了生成的密码保险库的问题。"; },
    Dismiss: function(d) { return "关闭"; },
    "Don-t-ask-again": function(d) { return "不再询问"; },
    "Download-1Password-8": function(d) { return "下载 1Password 8"; },
    Edit: function(d) { return "编辑"; },
    "Existing-items": function(d) { return "现有的项目"; },
    "Failed-to-load-item-": function(d) { return "加载项目失败。"; },
    "Failed-to-save-item-": function(d) { return "保存项目失败。"; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "下次登录时使用 1Password 填充此项目，以节省时间。"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "修复 " + plural(d.count, 0, en, { "1": number(d.count, "count") + " 个问题", other: number(d.count, "count") + " 个问题" }) + "以保持 " + d.appName + " 访问"; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "修复 " + plural(d.count, 0, en, { "1": number(d.count, "count") + " 个问题", other: number(d.count, "count") + " 个问题" }) + "以使用 " + d.appName; },
    "Fix-1-issue-to-access--appName-": function(d) { return "修复 1 个问题以访问 " + d.appName; },
    "Fix-issue---issue-": function(d) { return "修复问题：" + d.issue; },
    "Fix-later": function(d) { return "稍后修复"; },
    "Invalid-one-time-password-secret-": function(d) { return "无效的一次性密码。"; },
    Item: function(d) { return "项目"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide 未安装或未注册"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "未找到 Kolide。请重试，若问题仍然存在，请联系您的管理员。"; },
    "Learn-more-": function(d) { return "了解更多…"; },
    Lock: function(d) { return "锁定"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "确保包含此站点登录信息的 1Password 帐户已解锁。"; },
    "Managed-by-Trelica": function(d) { return "由 Trelica 管理"; },
    "New-Item": function(d) { return "新建项目"; },
    "New-Notification": function(d) { return "新通知"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "下一步：设置 SSH Agent"; },
    "No-items-found-to-sign-in-with-": function(d) { return "未找到用于登录的项目。"; },
    "No-logins-found": function(d) { return "未找到登录信息"; },
    "No-passkeys-found": function(d) { return "未找到通行密钥"; },
    Notifications: function(d) { return "通知"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "保存之后，我们会为你记住。"; },
    Overwrite: function(d) { return "覆盖"; },
    "Overwrite-one-time-password-": function(d) { return "覆盖一次性密码？"; },
    "Passkey-saved": function(d) { return "通行密钥已保存"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "请登录 1Password 以保护网上安全，并让保存和填充密码变得简单。"; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "请在修复此问题前再次尝试解锁"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "按下浏览器工具栏上的 1Password 图标以解锁。"; },
    Recheck: function(d) { return "再次检查"; },
    "Recheck-device": function(d) { return "重新检查设备"; },
    "Resolved-issue---issue-": function(d) { return "已解决问题：" + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "SSH 密钥已创建。要在本地使用此密钥，请设置 1Password 的 SSH Agent，在 1Password 8 中提供。"; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "SSH 密钥已创建。要在本地使用此密钥，请设置 1Password 的 SSH Agent。"; },
    Save: function(d) { return "保存"; },
    "Save--item-": function(d) { return "保存 " + d.item; },
    "Save-in-1Password-": function(d) { return "在 1Password 中保存？"; },
    "Save-item": function(d) { return "保存项目"; },
    "Save-one-time-password": function(d) { return "保存一次性密码"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "保存登录时自动填写的一次性密码。"; },
    "Save-passkey": function(d) { return "保存通行密钥"; },
    "Saved-one-time-password": function(d) { return "已保存的一次性密码"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "选择浏览器工具栏上的 1Password 图标以解锁。"; },
    "Session-expired-for-tab-": function(d) { return "标签会话过期。"; },
    "Set-up-SSH-Agent": function(d) { return "设置 SSH Agent"; },
    "Sign-in": function(d) { return "登录"; },
    "Sign-in-to-1Password": function(d) { return "登录 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "一键登录网站，轻松保存您的信息，并使用浏览器扩展生成新密码。"; },
    "Sign-in-using--title----subtitle-": function(d) { return "通过 " + d.title + ": " + d.subtitle + " 登录"; },
    "Sign-in-with-": function(d) { return "登录方式为…"; },
    "Sign-in-with-a-passkey": function(d) { return "通过通行密钥登录"; },
    Snooze: function(d) { return "稍后提醒"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "出错了。请再试一次。如果问题仍然存在，请与管理员联络"; },
    "Something-went-wrong": function(d) { return "出错了"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "第 3 步：检查或编辑新的登录项目，然后保存。"; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "将 " + d.credentialName + " 存储在 1Password 中并通过 " + d.authType + " 验证 " + d.platformName + " CLI。"; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "此项目中的一次性密码将被替换。"; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "创建通行密钥的请求已超时。"; },
    "To-save--item---unlock-1Password-first-": function(d) { return "要保存 " + d.item + "，需先解锁 1Password。"; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "要保存通行密钥，需先解锁 1Password。"; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "要使用通行密钥，需先解锁 1Password。"; },
    "Touch-ID-or-Apple-Watch": function(d) { return "触控 ID 或 Apple Watch"; },
    "Try-Again": function(d) { return "再试一次"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "尝试锁定再解锁 1Password 以再次填充。"; },
    "Unable-to-fill-password": function(d) { return "无法填充密码"; },
    "Unlock-1Password": function(d) { return "解锁 1Password"; },
    "Unlock-to-Save": function(d) { return "解锁以保存"; },
    "Unlock-to-save": function(d) { return "解锁以保存"; },
    "Unlocking-1Password-": function(d) { return "正在解锁 1Password…"; },
    Update: function(d) { return "更新"; },
    "Update-Existing": function(d) { return "更新现有"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "使用 1Password 作为两步验证工具"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "使用 1Password 通过 " + d.authType + " 验证 " + d.platformName + " CLI。"; },
    "Use-a-security-key-or-another-passkey": function(d) { return "使用安全密钥或其他通行密钥"; },
    "View-all": function(d) { return "查看全部"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "因为发生意外错误，我们无法使用建议的密码。"; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "将在 " + plural(d.daysBlocked, 0, en, { "1": number(d.daysBlocked, "daysBlocked") + " 天", other: number(d.daysBlocked, "daysBlocked") + " 天" }) + "后阻止"; },
    "Will-block-today": function(d) { return "将于今天阻止"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "您可以在“设置”中管理您的帐户。"; },
    "You-have-recieved-a-new-notification-": function(d) { return "你收到了一则新通知！"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "您的 " + d.accountName + " 帐户需要解锁才能填充此密码。解锁并重试。"; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "你的管理员需要解锁 1Password 以检查设备的安全性。"; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "你的管理员要求您在此设备上设置 Kolide，才能访问工作应用。"; },
    "a--item-": function(d) { return "一个 " + d.item; },
    "an--item-": function(d) { return "一个 " + d.item; },
    "an-Item": function(d) { return "一个项目"; },
    "one-time-password": function(d) { return "一次性密码"; },
    "system-authentication": function(d) { return "系统验证"; },
    "autosave-save-type-login": function(d) { return "登录"; },
    "autosave-save-type-credit-card": function(d) { return "信用卡"; },
    "autosave-save-type-identity": function(d) { return "身份标识"; }
  },
  "zh-TW": {
    "-accountName--needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return d.accountName + " 需要解鎖 1Password 以檢查裝置的安全性。"; },
    "-accountName--requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return d.accountName + " 要求您在此裝置上設定 Kolide，才能存取工作應用程式。"; },
    "-count--plural---1---issue-remains--other---issues-remain--": function(d) { return plural(d.count, 0, en, { "1": "剩餘 " + number(d.count, "count") + " 個問題", other: "剩餘 " + number(d.count, "count") + " 個問題" }); },
    "-credentialName--saved-in-1Password": function(d) { return d.credentialName + " 已儲存在 1Password 中"; },
    "-item--already-saved": function(d) { return d.item + " 已儲存"; },
    "-item--is-linked-to-": function(d) { return d.item + " 已連結到："; },
    "-item--saved": function(d) { return d.item + " 已儲存"; },
    "-provider--was-used-to-sign-in-to-": function(d) { return d.provider + " 被用於登入："; },
    "1Password-Shell-Plugin-available-for--platformName-": function(d) { return "適用於 " + d.platformName + " 的 1Password 殼層外掛程式可用"; },
    "1Password-encountered-a-problem-": function(d) { return "1Password 遇到問題。"; },
    "1Password-is-locked": function(d) { return "1Password 已上鎖"; },
    "A-security-breach-on-this-website-has-put-your-current-password-at-risk-": function(d) { return "該網站的安全漏洞已使您的當前密碼面臨風險"; },
    "About-Kolide": function(d) { return "關於 Kolide"; },
    "Add-account": function(d) { return "新增帳號"; },
    "Add-account-to-1Password-browser-extension": function(d) { return "新增帳號至 1Password 瀏覽器擴充元件"; },
    "All-good": function(d) { return "一切正常"; },
    "All-issues-resolved": function(d) { return "全部問題已解決"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "發生意外錯誤。請聯繫 <EMAIL>"; },
    "Blocked-Device": function(d) { return "阻止的裝置"; },
    Cancel: function(d) { return "取消"; },
    "Change-now": function(d) { return "立即變更"; },
    "Change-this-compromised-password": function(d) { return "變更有洩漏風險的密碼"; },
    Close: function(d) { return "關閉"; },
    "Collapse-all": function(d) { return "全部收起"; },
    "Connect-to-the-internet-and-then-try-again-": function(d) { return "連接到網路並再試一次。"; },
    "Contact-Support": function(d) { return "聯絡支援"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-default-account-": function(d) { return "聯絡支援人員尋求協助，並告訴對方遇到了預設帳號的問題。"; },
    "Contact-support-for-help--and-tell-them-it-s-a-problem-with-the-generated-password-vault-": function(d) { return "聯絡支援人員尋求協助，並告訴對方遇到了產生的密碼保險庫的問題。"; },
    Dismiss: function(d) { return "關閉"; },
    "Don-t-ask-again": function(d) { return "不再詢問"; },
    "Download-1Password-8": function(d) { return "下載 1Password 8"; },
    Edit: function(d) { return "編輯"; },
    "Existing-items": function(d) { return "現有項目"; },
    "Failed-to-load-item-": function(d) { return "載入項目失敗。"; },
    "Failed-to-save-item-": function(d) { return "儲存項目失敗。"; },
    "Fill-this-item-with-1Password-next-time-you-log-in-to-save-time": function(d) { return "下次登入時使用 1Password 填入此項目，以節省時間。"; },
    "Fix--count--plural---1----issue--other----issues---to-keep--appName--access": function(d) { return "修復 " + plural(d.count, 0, en, { "1": number(d.count, "count") + " 個問題", other: number(d.count, "count") + " 個問題" }) + "以保持 " + d.appName + " 存取"; },
    "Fix--count--plural---1----issue--other----issues---to-use--appName-": function(d) { return "修復 " + plural(d.count, 0, en, { "1": number(d.count, "count") + " 個問題", other: number(d.count, "count") + " 個問題" }) + "以使用 " + d.appName; },
    "Fix-1-issue-to-access--appName-": function(d) { return "修復 1 個問題以存取 " + d.appName; },
    "Fix-issue---issue-": function(d) { return "修復問題：" + d.issue; },
    "Fix-later": function(d) { return "稍後修復"; },
    "Invalid-one-time-password-secret-": function(d) { return "無效的一次性密碼。"; },
    Item: function(d) { return "項目"; },
    "Kolide-is-missing-or-not-registered": function(d) { return "Kolide 未安裝或未註冊"; },
    "Kolide-wasn-t-found--Try-again--or-contact-your-administrator-if-the-problem-persists-": function(d) { return "未找到 Kolide。請重試，若問題仍然存在，請聯絡您的管理員。"; },
    "Learn-more-": function(d) { return "進一步瞭解…"; },
    Lock: function(d) { return "上鎖"; },
    "Make-sure-1Password-accounts-containing-logins-for-this-site-are-unlocked-": function(d) { return "確保包含此網站登入資訊的 1Password 帳號已解鎖。"; },
    "Managed-by-Trelica": function(d) { return "由 Trelica 管理"; },
    "New-Item": function(d) { return "新增項目"; },
    "New-Notification": function(d) { return "新通知"; },
    "Next-step--Set-up-SSH-Agent": function(d) { return "下一步：設定 SSH Agent"; },
    "No-items-found-to-sign-in-with-": function(d) { return "未找到用於登入的項目。"; },
    "No-logins-found": function(d) { return "沒有找到登入資料"; },
    "No-passkeys-found": function(d) { return "未找到通行密鑰"; },
    Notifications: function(d) { return "通知"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "儲存之後，我們會為你記住。"; },
    Overwrite: function(d) { return "覆寫"; },
    "Overwrite-one-time-password-": function(d) { return "覆寫一次性密碼？"; },
    "Passkey-saved": function(d) { return "通行密鑰已儲存"; },
    "Please-sign-in-to-1Password-to-keep-yourself-safe-online-and-make-it-easy-to-save-and-fill-your-passwords-": function(d) { return "請登入 1Password 以保護網路安全，並讓儲存和填入密碼變得簡單。"; },
    "Please-try-unlocking-again-before-fixing-this-issue": function(d) { return "請在修復此問題前再次嘗試解鎖"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "按下瀏覽器工具列上的 1Password 圖示以解鎖。"; },
    Recheck: function(d) { return "再次檢查"; },
    "Recheck-device": function(d) { return "重新檢查裝置"; },
    "Resolved-issue---issue-": function(d) { return "已解決問題：" + d.issue; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-Agent--available-on-1Password-8-": function(d) { return "SSH 金鑰已建立。要在本機使用此金鑰，請設定 1Password 的 SSH Agent，在 1Password 8 中提供。"; },
    "SSH-key-created--To-use-this-key-locally--set-up-the-1Password-SSH-agent-": function(d) { return "SSH 金鑰已建立。要在本機使用此金鑰，請設定 1Password 的 SSH Agent。"; },
    Save: function(d) { return "儲存"; },
    "Save--item-": function(d) { return "儲存 " + d.item; },
    "Save-in-1Password-": function(d) { return "在 1Password 中儲存？"; },
    "Save-item": function(d) { return "儲存項目"; },
    "Save-one-time-password": function(d) { return "儲存一次性密碼"; },
    "Save-one-time-passwords-that-will-be-automatically-filled-during-sign-in-": function(d) { return "儲存登入時自動填寫的一次性密碼。"; },
    "Save-passkey": function(d) { return "儲存通行密鑰"; },
    "Saved-one-time-password": function(d) { return "已儲存的一次性密碼"; },
    "Select-the-1Password-icon-in-your-browser-s-toolbar-to-unlock-": function(d) { return "選擇瀏覽器工具列上的 1Password 圖示以解鎖。"; },
    "Session-expired-for-tab-": function(d) { return "標籤工作階段過期"; },
    "Set-up-SSH-Agent": function(d) { return "設定 SSH Agent"; },
    "Sign-in": function(d) { return "登入"; },
    "Sign-in-to-1Password": function(d) { return "登入 1Password"; },
    "Sign-in-to-websites-with-one-click--easily-save-your-information--and-generate-new-passwords-with-the-browser-extension-": function(d) { return "一鍵登入網站，輕鬆保存您的資訊，並使用瀏覽器擴充元件產生新密碼。"; },
    "Sign-in-using--title----subtitle-": function(d) { return "透過 " + d.title + ": " + d.subtitle + " 登入"; },
    "Sign-in-with-": function(d) { return "登入方式為…"; },
    "Sign-in-with-a-passkey": function(d) { return "透過通行密鑰登入"; },
    Snooze: function(d) { return "稍後提醒"; },
    "Something-isn-t-right--Please-try-again--If-the-problem-persists--contact-your-administrator": function(d) { return "出錯了。請再試一次。如果問題仍然存在，請與管理員聯絡"; },
    "Something-went-wrong": function(d) { return "出錯了"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "第 3 步：檢查或編輯新的登入項目，然後儲存。"; },
    "Store-your--credentialName--in-1Password-and-authenticate--platformName--CLI-with--authType--": function(d) { return "將 " + d.credentialName + " 存儲在 1Password 中並透過 " + d.authType + " 驗證 " + d.platformName + " CLI。"; },
    "The-one-time-password-in-this-item-will-be-replaced-": function(d) { return "此項目中的一次性密碼將被取代。"; },
    "The-request-to-create-a-passkey-timed-out-": function(d) { return "建立通行密鑰已超時。"; },
    "To-save--item---unlock-1Password-first-": function(d) { return "若要儲存 " + d.item + "，需先解鎖 1Password。"; },
    "To-save-a-passkey--unlock-1Password-first-": function(d) { return "若要儲存通行密鑰，需先解鎖 1Password。"; },
    "To-use-your-passkey--unlock-1Password-first-": function(d) { return "若要使用通行密鑰，需先解鎖 1Password。"; },
    "Touch-ID-or-Apple-Watch": function(d) { return "Touch ID 或 Apple Watch"; },
    "Try-Again": function(d) { return "再試一次"; },
    "Try-locking-and-then-unlocking-1Password-to-fill-again-": function(d) { return "嘗試上鎖再解鎖 1Password 以再次填入。"; },
    "Unable-to-fill-password": function(d) { return "無法填入密碼"; },
    "Unlock-1Password": function(d) { return "解鎖 1Password"; },
    "Unlock-to-Save": function(d) { return "解鎖以儲存"; },
    "Unlock-to-save": function(d) { return "解鎖以儲存"; },
    "Unlocking-1Password-": function(d) { return "解鎖 1Password…"; },
    Update: function(d) { return "更新"; },
    "Update-Existing": function(d) { return "更新現有"; },
    "Use-1Password-for-two-factor-authentication": function(d) { return "使用 1Password 作為雙因素驗證工具"; },
    "Use-1Password-to-authenticate--platformName--CLI-with--authType--": function(d) { return "使用 1Password 透過 " + d.authType + " 驗證 " + d.platformName + " CLI。"; },
    "Use-a-security-key-or-another-passkey": function(d) { return "使用安全金鑰或其他通行密鑰"; },
    "View-all": function(d) { return "檢視全部"; },
    "We-re-unable-to-use-the-suggested-password-as-an-unexpected-error-occurred-": function(d) { return "因為發生意外錯誤，我們無法使用建議的密碼。"; },
    "Will-block-in--daysBlocked--plural---1----day--other----days--": function(d) { return "將在 " + plural(d.daysBlocked, 0, en, { "1": number(d.daysBlocked, "daysBlocked") + " 天", other: number(d.daysBlocked, "daysBlocked") + " 天" }) + "後阻止"; },
    "Will-block-today": function(d) { return "將於今天阻止"; },
    "Windows-Hello": function(d) { return "Windows Hello"; },
    "You-can-manage-your-accounts-in-Settings-": function(d) { return "您可以在「設定」中管理您的帳號。"; },
    "You-have-recieved-a-new-notification-": function(d) { return "你收到了一則新通知！"; },
    "Your--accountName--account-needs-to-be-unlocked-to-fill-this-password--Unlock-it-and-try-again-": function(d) { return "您的 " + d.accountName + " 帳號需要解鎖才能填入此密碼。解鎖並重試。"; },
    "Your-admin-needs-1Password-unlocked-to-check-your-device-s-security-": function(d) { return "你的管理員需要解鎖 1Password 以檢查裝置的安全性。"; },
    "Your-admin-requires-that-you-set-up-Kolide-on-this-device-before-accessing-work-apps-": function(d) { return "你的管理員要求您在此裝置上設定 Kolide，才能存取工作應用程式。"; },
    "a--item-": function(d) { return "一個 " + d.item; },
    "an--item-": function(d) { return "一個 " + d.item; },
    "an-Item": function(d) { return "一個項目"; },
    "one-time-password": function(d) { return "一次性密碼"; },
    "system-authentication": function(d) { return "系統驗證"; },
    "autosave-save-type-login": function(d) { return "登入"; },
    "autosave-save-type-credit-card": function(d) { return "信用卡"; },
    "autosave-save-type-identity": function(d) { return "身份認證"; }
  }
}