
export default {
  de: {
    "1Password-Tutorial-": function(d) { return "1Password-Tutorial: "; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password hat Ihren Benutzernamen und Ihr Passwort ausgefüllt, so dass Sie nur noch auf Anmelden klicken müssen."; },
    "Any-Website": function(d) { return "Jede Website"; },
    "Confirm-Password": function(d) { return "Passwort bestätigen"; },
    "Create-Account": function(d) { return "Konto erstellen"; },
    "Exit-Tutorial": function(d) { return "Tutorial verlassen"; },
    "Finish-tutorial": function(d) { return "Tutorial beenden"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "So wie Sie sich normalerweise bei einer Website anmelden."; },
    Next: function(d) { return "Weiter"; },
    "Next--learn-to-sign-in": function(d) { return "Als Nächstes lernen Sie, sich anzumelden"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "Die von uns vorgeschlagenen Passwörter sind für jede Anmeldung einmalig und sehr sicher. "; },
    Password: function(d) { return "Passwort"; },
    "Practice-by-creating-a-fake-account": function(d) { return "Üben Sie, indem Sie ein fiktives Konto erstellen"; },
    "Practice-how-to-save---fill": function(d) { return "Üben Sie, wie man speichert und ausfüllt"; },
    "Saved-in-1Password-": function(d) { return "In 1Password gespeichert!"; },
    "Sign-In": function(d) { return "Anmelden"; },
    "Step-1--Click-the-password-field": function(d) { return "Schritt 1: Klicken Sie in das Passwortfeld"; },
    "Step-1--Select-the-username-field-": function(d) { return "Schritt 1: Wählen Sie das Feld „Benutzername“ aus."; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "Schritt 2: Füllen Sie das Feld mit einem Klick aus!"; },
    "Step-2--Use-the-suggested-password": function(d) { return "Schritt 2: Verwenden Sie das vorgeschlagene Passwort"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "Wenn Sie sich das nächste Mal auf dieser Website anmelden, kann 1Password die Felder für Sie ausfüllen."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "Wählen Sie dann aus den Vorschlägen Ihren Benutzernamen und Ihr Passwort, um diese auszufüllen."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "Dies ist nur ein Beispiel-Webformular für Übungszwecke."; },
    Username: function(d) { return "Benutzername"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "Wir haben ein Beispiel für Sie eingetragen."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "Wenn Sie ein neues Konto erstellen, geben Sie Ihren Benutzernamen ein."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "Wenn Sie in einem Webformular in ein Passwortfeld klicken, wird ein 1Password-Vorschlag angezeigt."; },
    "You-re-done-": function(d) { return "Sie sind fertig!"; }
  },
  en: {
    "1Password-Tutorial-": function(d) { return "1Password Tutorial: "; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password filled your username and password, so all that's left for you is to click sign in."; },
    "Any-Website": function(d) { return "Any Website"; },
    "Confirm-Password": function(d) { return "Confirm Password"; },
    "Create-Account": function(d) { return "Create Account"; },
    "Exit-Tutorial": function(d) { return "Exit Tutorial"; },
    "Finish-tutorial": function(d) { return "Finish tutorial"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "Just like you normally would when you log in on a website."; },
    Next: function(d) { return "Next"; },
    "Next--learn-to-sign-in": function(d) { return "Next, learn to sign in"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "Our suggested passwords are unique for every login and very strong. "; },
    Password: function(d) { return "Password"; },
    "Practice-by-creating-a-fake-account": function(d) { return "Practice by creating a fake account"; },
    "Practice-how-to-save---fill": function(d) { return "Practice how to save & fill"; },
    "Saved-in-1Password-": function(d) { return "Saved in 1Password!"; },
    "Sign-In": function(d) { return "Sign In"; },
    "Step-1--Click-the-password-field": function(d) { return "Step 1: Click the password field"; },
    "Step-1--Select-the-username-field-": function(d) { return "Step 1: Select the username field."; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "Step 2: Fill in with one click!"; },
    "Step-2--Use-the-suggested-password": function(d) { return "Step 2: Use the suggested password"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "The next time you log in to this website, 1Password can fill in the details for you."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "Then select your login from the suggestions to fill in your username and password."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "This is just a demo web form for practice."; },
    Username: function(d) { return "Username"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "We've entered one in for you as an example."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "When creating a new account, you enter your username."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "When you click a password field in a web form, a 1Password suggestion will appear."; },
    "You-re-done-": function(d) { return "You’re done!"; }
  },
  es: {
    "1Password-Tutorial-": function(d) { return "Tutorial de 1Password: "; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password ha cumplimentado tu nombre de usuario y contraseñas, así que lo único que tienes que hacer es pulsar en iniciar sesión."; },
    "Any-Website": function(d) { return "Cualquier sitio web"; },
    "Confirm-Password": function(d) { return "Confirmar contraseña"; },
    "Create-Account": function(d) { return "Crear cuenta"; },
    "Exit-Tutorial": function(d) { return "Salir del tutorial"; },
    "Finish-tutorial": function(d) { return "Finalizar tutorial"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "Al igual que harías normalmente al iniciar sesión en un sitio web."; },
    Next: function(d) { return "Siguiente"; },
    "Next--learn-to-sign-in": function(d) { return "A continuación, descubrir cómo iniciar sesión"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "Las contraseñas que sugerimos son únicas para cada inicio de sesión y muy seguras. "; },
    Password: function(d) { return "Contraseña"; },
    "Practice-by-creating-a-fake-account": function(d) { return "Practica creando una cuenta de prueba"; },
    "Practice-how-to-save---fill": function(d) { return "Práctica cómo guardar y cumplimentar"; },
    "Saved-in-1Password-": function(d) { return "¡Guardado en 1Password!"; },
    "Sign-In": function(d) { return "Iniciar sesión"; },
    "Step-1--Click-the-password-field": function(d) { return "Paso 1: Pulsa en el campo de contraseña"; },
    "Step-1--Select-the-username-field-": function(d) { return "Paso 1: Selecciona el campo de nombre de usuario."; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "Paso 2: ¡Cumplimenta con un solo clic!"; },
    "Step-2--Use-the-suggested-password": function(d) { return "Paso 2: Utiliza la contraseña sugerida"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "La próxima vez que inicies sesión en este sitio web, 1Password podrá cumplimentar los datos por ti."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "A continuación, selecciona tus credenciales de inicio de sesión entre las sugerencias para cumplimentar tu nombre de usuario y contraseña."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "Esto es solo un formulario web de demostración para practicar."; },
    Username: function(d) { return "Nombre de usuario"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "Hemos introducido uno por ti a modo de ejemplo."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "Al crear una cuenta nueva, introduces tu nombre de usuario."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "Cuando pulsas en el campo de contraseña en un formulario web, aparecerá una sugerencia de 1Password."; },
    "You-re-done-": function(d) { return "¡Todo listo!"; }
  },
  fr: {
    "1Password-Tutorial-": function(d) { return "Tutoriel 1Password : "; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password a rempli votre nom d'utilisateur et votre mot de passe, il ne vous reste donc plus qu'à cliquer pour vous connecter."; },
    "Any-Website": function(d) { return "Tout site web"; },
    "Confirm-Password": function(d) { return "Confirmer le mot de passe"; },
    "Create-Account": function(d) { return "Créer un compte"; },
    "Exit-Tutorial": function(d) { return "Quitter le tutoriel"; },
    "Finish-tutorial": function(d) { return "Terminer le tutoriel"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "Comme vous le feriez normalement lorsque vous vous connectez à un site web."; },
    Next: function(d) { return "Suivant"; },
    "Next--learn-to-sign-in": function(d) { return "Ensuite, apprenez à vous connecter"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "Les mots de passe que nous suggérons sont uniques pour chaque connexion et très sécurisés. "; },
    Password: function(d) { return "Mot de passe"; },
    "Practice-by-creating-a-fake-account": function(d) { return "Testez en créant un faux compte"; },
    "Practice-how-to-save---fill": function(d) { return "Apprenez à enregistrer et à remplir"; },
    "Saved-in-1Password-": function(d) { return "Enregistré dans 1Password !"; },
    "Sign-In": function(d) { return "Connexion"; },
    "Step-1--Click-the-password-field": function(d) { return "Étape 1 : Cliquez sur le champ du mot de passe"; },
    "Step-1--Select-the-username-field-": function(d) { return "Étape 1 : Sélectionnez le champ du nom d'utilisateur"; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "Étape 2 : Remplissez en un clic !"; },
    "Step-2--Use-the-suggested-password": function(d) { return "Étape 2 : Utilisez le mot de passe suggéré"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "La prochaine fois que vous vous connecterez à ce site web, 1Password pourra remplir les informations pour vous."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "Sélectionnez ensuite votre identifiant dans les suggestions pour remplir votre nom d'utilisateur et votre mot de passe."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "Il s'agit d'un formulaire web de démonstration pour vous entraîner."; },
    Username: function(d) { return "Nom d'utilisateur"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "Nous en avons saisi un pour vous à titre d'exemple."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "Lors de la création d'un nouveau compte, il vous faut saisir votre nom d'utilisateur."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "Lorsque vous cliquez sur un champ de mot de passe dans un formulaire web, une suggestion de 1Password s'affiche."; },
    "You-re-done-": function(d) { return "Et voilà !"; }
  },
  it: {
    "1Password-Tutorial-": function(d) { return "Tutorial di 1Password: "; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password ha compilato il tuo nome utente e la relativa password, quindi non ti resta che cliccare per accedere."; },
    "Any-Website": function(d) { return "Qualsiasi sito web"; },
    "Confirm-Password": function(d) { return "Conferma password"; },
    "Create-Account": function(d) { return "Crea account"; },
    "Exit-Tutorial": function(d) { return "Esci dal tutorial"; },
    "Finish-tutorial": function(d) { return "Completa il tutorial"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "Proprio come fai normalmente quando accedi a un sito web."; },
    Next: function(d) { return "Successivo"; },
    "Next--learn-to-sign-in": function(d) { return "Successivamente, impara ad accedere"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "Le password suggerite sono uniche per ogni accesso e molto sicure. "; },
    Password: function(d) { return "Password"; },
    "Practice-by-creating-a-fake-account": function(d) { return "Practice by creating a fake account"; },
    "Practice-how-to-save---fill": function(d) { return "Esercitati a salvare e compilare"; },
    "Saved-in-1Password-": function(d) { return "Salvato in 1Password!"; },
    "Sign-In": function(d) { return "Accedi"; },
    "Step-1--Click-the-password-field": function(d) { return "Passaggio 1: clicca sul campo della password"; },
    "Step-1--Select-the-username-field-": function(d) { return "Passaggio 1: seleziona il campo del nome utente."; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "Passaggio 2: compila con un semplice click!"; },
    "Step-2--Use-the-suggested-password": function(d) { return "Passaggio 2: utilizza la password suggerita"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "La prossima volta che accederai a questo sito, 1Password potrà compilare i dati per te."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "Poi seleziona le credenziali di accesso dai suggerimenti per inserire il tuo nome utente e la relativa password."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "Questo è solo un modulo web dimostrativo per esercitarti."; },
    Username: function(d) { return "Nome utente"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "Ne abbiamo inserito uno a titolo di esempio."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "Quando crei un nuovo account, inserisci il tuo nome utente."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "Quando clicchi su un campo password in un modulo web, appare un suggerimento di 1Password."; },
    "You-re-done-": function(d) { return "Ecco fatto!"; }
  },
  ja: {
    "1Password-Tutorial-": function(d) { return "1Passwordチュートリアル："; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Passwordはあなたのユーザー名とパスワードを入力したので、あとはサインインをクリックするだけです。"; },
    "Any-Website": function(d) { return "あらゆるウェブサイト"; },
    "Confirm-Password": function(d) { return "パスワードを確認"; },
    "Create-Account": function(d) { return "アカウントを作成"; },
    "Exit-Tutorial": function(d) { return "チュートリアルを終了する"; },
    "Finish-tutorial": function(d) { return "チュートリアルを完了する"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "通常のウェブサイトにログインするときのように。"; },
    Next: function(d) { return "次へ"; },
    "Next--learn-to-sign-in": function(d) { return "次に、サインインの方法を学ぶ"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "当社が推奨するパスワードは、ログインごとに一意であり、非常に強力なものです。"; },
    Password: function(d) { return "パスワード"; },
    "Practice-by-creating-a-fake-account": function(d) { return "偽アカウントを作って練習する"; },
    "Practice-how-to-save---fill": function(d) { return "保存と入力方法を練習する"; },
    "Saved-in-1Password-": function(d) { return "1Passwordに保存する！"; },
    "Sign-In": function(d) { return "サインイン"; },
    "Step-1--Click-the-password-field": function(d) { return "ステップ1：パスワードフィールドをクリックする"; },
    "Step-1--Select-the-username-field-": function(d) { return "ステップ1：ユーザー名フィールドを選択する。"; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "ステップ2：ワンクリックで入力！"; },
    "Step-2--Use-the-suggested-password": function(d) { return "ステップ2：推奨パスワードを使用する"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "次回このウェブサイトにログインする際、1Passwordがあなたの代わりに情報を入力します。"; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "次に、サジェストからログインを選択し、ユーザー名とパスワードを入力します。"; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "これは練習用のデモウェブフォームです。"; },
    Username: function(d) { return "ユーザー名"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "例として1つ入力しています。"; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "新しいアカウントを作成する際に、ユーザー名を入力します。"; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "ウェブフォームのパスワードフィールドをクリックすると、1Passwordの候補が表示されます。"; },
    "You-re-done-": function(d) { return "完了です！"; }
  },
  ko: {
    "1Password-Tutorial-": function(d) { return "1Password 튜토리얼: "; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password가 회원님의 사용자 이름과 비밀번호를 입력했습니다. 이제 '로그인'만 클릭하시면 됩니다."; },
    "Any-Website": function(d) { return "모든 웹사이트"; },
    "Confirm-Password": function(d) { return "비밀번호 확인"; },
    "Create-Account": function(d) { return "계정 만들기"; },
    "Exit-Tutorial": function(d) { return "튜토리얼 나가기"; },
    "Finish-tutorial": function(d) { return "튜토리얼 종료"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "평소처럼 웹사이트에 로그인하시는 것처럼."; },
    Next: function(d) { return "다음"; },
    "Next--learn-to-sign-in": function(d) { return "다음에는 로그인하는 법을 배웁니다"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "저희가 제안드린 비밀번호는 모든 로그인 항목에 대해 고유하며 아주 강력합니다. "; },
    Password: function(d) { return "비밀번호"; },
    "Practice-by-creating-a-fake-account": function(d) { return "가짜 계정을 만들어 연습합니다"; },
    "Practice-how-to-save---fill": function(d) { return "저장 및 자동 입력 방법을 연습합니다"; },
    "Saved-in-1Password-": function(d) { return "1Password에 저장되었습니다!"; },
    "Sign-In": function(d) { return "로그인"; },
    "Step-1--Click-the-password-field": function(d) { return "단계 1: 비밀번호 입력란을 클릭합니다"; },
    "Step-1--Select-the-username-field-": function(d) { return "단계 1: 사용자 이름 필드를 선택합니다."; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "단계 2: 한 번 클릭하여 입력합니다!"; },
    "Step-2--Use-the-suggested-password": function(d) { return "단계 2: 제안된 비밀번호를 사용합니다"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "다음 번에 이 웹사이트에 로그인할 때 1Password가 회원님을 대신하여 세부 정보를 입력할 수 있습니다."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "그런 다음, 제안 내용 중에서 로그인 정보를 선택하고 사용자 이름과 비밀번호를 입력합니다."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "이것은 연습용 데모 웹 양식입니다."; },
    Username: function(d) { return "사용자 이름"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "저희가 예시를 위해 하나를 입력해 드렸습니다."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "새로운 계정을 만들 때 회원님이 사용자 이름을 입력합니다."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "웹 양식에서 비밀번호 입력란을 클릭하면 1Password가 제안한 내용이 표시됩니다."; },
    "You-re-done-": function(d) { return "모두 완료되었습니다!"; }
  },
  nl: {
    "1Password-Tutorial-": function(d) { return "1Password-tutorial: "; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password heeft je gebruikersnaam en wachtwoord ingevuld, dus je hoeft alleen nog op inloggen te klikken."; },
    "Any-Website": function(d) { return "Alle websites"; },
    "Confirm-Password": function(d) { return "Wachtwoord bevestigen"; },
    "Create-Account": function(d) { return "Account aanmaken"; },
    "Exit-Tutorial": function(d) { return "Tutorial verlaten"; },
    "Finish-tutorial": function(d) { return "Tutorial afronden"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "Net zoals je normaal bij een website inlogt."; },
    Next: function(d) { return "Volgende"; },
    "Next--learn-to-sign-in": function(d) { return "Ontdek nu hoe je inlogt"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "Onze voorgestelde wachtwoorden zijn zeer sterk en uniek voor elke inlog. "; },
    Password: function(d) { return "Wachtwoord"; },
    "Practice-by-creating-a-fake-account": function(d) { return "Oefen door een nepaccount aan te maken"; },
    "Practice-how-to-save---fill": function(d) { return "Oefen met hoe je opslaat en aanvult"; },
    "Saved-in-1Password-": function(d) { return "Opgeslagen in 1Password!"; },
    "Sign-In": function(d) { return "Inloggen"; },
    "Step-1--Click-the-password-field": function(d) { return "Stap 1: klik in het wachtwoordveld"; },
    "Step-1--Select-the-username-field-": function(d) { return "Stap 1: selecteer het veld voor de gebruikersnaam"; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "Stap 2: vul aan met één klik!"; },
    "Step-2--Use-the-suggested-password": function(d) { return "Stap 2: gebruik het voorgestelde wachtword"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "Als je in het vervolg op deze website inlogt, kan 1Password de gegevens voor je aanvullen."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "Selecteer dan je inloggegevens vanuit de suggesties zodat je gebruikersnaam en wachtwoord aangevuld kunnen worden."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "Dit is een voorbeeldomgeving om in te oefenen."; },
    Username: function(d) { return "Gebruikersnaam"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "We hebben er eentje als voorbeeld voor je ingevuld."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "Voor het aanmaken van een nieuwe account voer je je wachtwoord in."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "Klik je op het wachtwoordveld in een webformulier, dan verschijnt er een suggestie van 1Password."; },
    "You-re-done-": function(d) { return "Je bent er klaar voor!"; }
  },
  pt: {
    "1Password-Tutorial-": function(d) { return "Tutorial do 1Password:"; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "O 1Password preencheu seu nome de usuário e senha, então tudo o que resta é clicar em entrar."; },
    "Any-Website": function(d) { return "Qualquer site"; },
    "Confirm-Password": function(d) { return "Confirme sua senha"; },
    "Create-Account": function(d) { return "Criar uma conta"; },
    "Exit-Tutorial": function(d) { return "Tutorial de saída"; },
    "Finish-tutorial": function(d) { return "Concluir tutorial"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "Assim como você faria normalmente ao fazer login em um site."; },
    Next: function(d) { return "Próximo"; },
    "Next--learn-to-sign-in": function(d) { return "Em seguida, aprenda a fazer login"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "Nossas senhas sugeridas são únicas para cada login e muito fortes."; },
    Password: function(d) { return "Senha"; },
    "Practice-by-creating-a-fake-account": function(d) { return "Pratique criando uma conta falsa"; },
    "Practice-how-to-save---fill": function(d) { return "Pratique como salvar e preencher"; },
    "Saved-in-1Password-": function(d) { return "Salvo no 1Password!"; },
    "Sign-In": function(d) { return "Entrar"; },
    "Step-1--Click-the-password-field": function(d) { return "Etapa 1: clique no campo de senha"; },
    "Step-1--Select-the-username-field-": function(d) { return "Etapa 1: selecione o campo de nome de usuário."; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "Etapa 2: preencha com um clique!"; },
    "Step-2--Use-the-suggested-password": function(d) { return "Etapa 2: use a senha sugerida"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "Na próxima vez que você fizer login neste site, o 1Password poderá preencher os detalhes para você."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "Em seguida, selecione seu login entre as sugestões para preencher seu nome de usuário e senha."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "Este é apenas um formulário web de demonstração para prática."; },
    Username: function(d) { return "Nome de usuário"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "Inserimos um para você como exemplo."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "Ao criar uma nova conta, você insere seu nome de usuário."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "Ao clicar em um campo de senha em um formulário da web, uma sugestão do 1Password aparecerá."; },
    "You-re-done-": function(d) { return "Você terminou!"; }
  },
  ru: {
    "1Password-Tutorial-": function(d) { return "Раздел помощи 1Password: "; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password ввел ваши имя пользователя и пароль, и теперь вам осталось только нажать «Войти в аккаунт»."; },
    "Any-Website": function(d) { return "Любой веб-сайт"; },
    "Confirm-Password": function(d) { return "Подтвердить пароль"; },
    "Create-Account": function(d) { return "Создать аккаунт"; },
    "Exit-Tutorial": function(d) { return "Выйти из раздела для помощи"; },
    "Finish-tutorial": function(d) { return "Закончить раздел для помощи"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "Так, как вы обычно это делаете, когда заходите в аккаунт на сайте."; },
    Next: function(d) { return "Далее"; },
    "Next--learn-to-sign-in": function(d) { return "Теперь научитесь входить в аккаунт"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "Предлагаемые пароли уникальны для каждого аккаунта и очень надежны. "; },
    Password: function(d) { return "Пароль"; },
    "Practice-by-creating-a-fake-account": function(d) { return "Попробуйте это, создав ненастоящий аккаунт"; },
    "Practice-how-to-save---fill": function(d) { return "Попробуйте сохранить и запомнить данные"; },
    "Saved-in-1Password-": function(d) { return "Сохранено в 1Password!"; },
    "Sign-In": function(d) { return "Войти в аккаунт"; },
    "Step-1--Click-the-password-field": function(d) { return "Шаг 1: Нажмите на поле пароля"; },
    "Step-1--Select-the-username-field-": function(d) { return "Шаг 1: Выберите поле для имени пользователя."; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "Шаг 2: Заполните одним щелчком мыши!"; },
    "Step-2--Use-the-suggested-password": function(d) { return "Шаг 2: Используйте предложенный пароль"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "В следующий раз, когда вы войдете в аккаунт на этом сайте, 1Password заполнит данные за вас."; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "Затем выберите свои данные для входа из предложенных вариантов, чтобы ввести имя пользователя и пароль."; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "Это лишь пример веб-формы для того, чтобы вы смогли попробовать."; },
    Username: function(d) { return "Имя пользователя"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "Мы заполнили один в качестве примера."; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "При создании нового аккаунта вы вводите свое имя пользователя."; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "При нажатии на поле пароля в веб-форме появится предложенный пароль от 1Password."; },
    "You-re-done-": function(d) { return "Готово!"; }
  },
  "zh-CN": {
    "1Password-Tutorial-": function(d) { return "1Password 教学："; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password 为您填充用户名和密码，您只需点击登录。"; },
    "Any-Website": function(d) { return "任何网站"; },
    "Confirm-Password": function(d) { return "确认密码"; },
    "Create-Account": function(d) { return "创建帐户"; },
    "Exit-Tutorial": function(d) { return "退出教学"; },
    "Finish-tutorial": function(d) { return "完成教学"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "就像您平常在网站上登录一样。"; },
    Next: function(d) { return "下一步"; },
    "Next--learn-to-sign-in": function(d) { return "下一步，学习登录"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "我们建议的密码对每个登录场景都是唯一的，并且非常安全。"; },
    Password: function(d) { return "密码"; },
    "Practice-by-creating-a-fake-account": function(d) { return "通过创建一个虚拟帐户来练习。"; },
    "Practice-how-to-save---fill": function(d) { return "练习如何保存和填充"; },
    "Saved-in-1Password-": function(d) { return "在 1Password 中保存！"; },
    "Sign-In": function(d) { return "登录"; },
    "Step-1--Click-the-password-field": function(d) { return "第一步：点击密码字段"; },
    "Step-1--Select-the-username-field-": function(d) { return "第一步：选择用户名字段。"; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "第二步：一次点击即可填充！"; },
    "Step-2--Use-the-suggested-password": function(d) { return "第二步：使用建议的密码"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "您下次登录此网站时，1Password 可以为您填充详细信息。"; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "然后从建议中选择您的登录信息，以填充用户名和密码。"; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "这只是一个用于练习的演示网页表单。"; },
    Username: function(d) { return "用户名"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "我们已经为您输入了一个示例。"; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "在创建新帐户时，输入你的用户名。"; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "当您在网页表单中点击密码字段时，1Password 将为您建议密码。"; },
    "You-re-done-": function(d) { return "完成了！"; }
  },
  "zh-TW": {
    "1Password-Tutorial-": function(d) { return "1Password 教學："; },
    "1Password-filled-your-username-and-password--so-all-that-s-left-for-you-is-to-click-sign-in-": function(d) { return "1Password 為您填入使用者名稱和密碼，按一下即可登入。"; },
    "Any-Website": function(d) { return "任何網站"; },
    "Confirm-Password": function(d) { return "確認密碼"; },
    "Create-Account": function(d) { return "建立帳號"; },
    "Exit-Tutorial": function(d) { return "退出教學"; },
    "Finish-tutorial": function(d) { return "完成教學"; },
    "Just-like-you-normally-would-when-you-log-in-on-a-website-": function(d) { return "就像您平常在網站上登入一樣。"; },
    Next: function(d) { return "下一步"; },
    "Next--learn-to-sign-in": function(d) { return "下一步，學習登入"; },
    "Our-suggested-passwords-are-unique-for-every-login-and-very-strong-": function(d) { return "我們建議的密碼對每個登入場景都是唯一的，並且非常安全。"; },
    Password: function(d) { return "密碼"; },
    "Practice-by-creating-a-fake-account": function(d) { return "透過建立一個虛擬帳號來練習。"; },
    "Practice-how-to-save---fill": function(d) { return "練習如何儲存和填入"; },
    "Saved-in-1Password-": function(d) { return "在 1Password 中儲存！"; },
    "Sign-In": function(d) { return "登入"; },
    "Step-1--Click-the-password-field": function(d) { return "第一步：點選密碼欄位"; },
    "Step-1--Select-the-username-field-": function(d) { return "第一步：選擇使用者名欄位。"; },
    "Step-2--Fill-in-with-one-click-": function(d) { return "第二步：按一下即可填入！"; },
    "Step-2--Use-the-suggested-password": function(d) { return "第二步：使用建議的密碼"; },
    "The-next-time-you-log-in-to-this-website--1Password-can-fill-in-the-details-for-you-": function(d) { return "您下次登入此網站時，1Password 可以為您填入詳細資訊。"; },
    "Then-select-your-login-from-the-suggestions-to-fill-in-your-username-and-password-": function(d) { return "然後從建議中選擇您的登入資訊，以填入使用者名稱和密碼。"; },
    "This-is-just-a-demo-web-form-for-practice-": function(d) { return "這只是一個用於練習的演示網頁表單。"; },
    Username: function(d) { return "使用者名稱"; },
    "We-ve-entered-one-in-for-you-as-an-example-": function(d) { return "我們已經為您輸入了一個範例。"; },
    "When-creating-a-new-account--you-enter-your-username-": function(d) { return "在建立新帳號時，輸入你的使用者名稱。"; },
    "When-you-click-a-password-field-in-a-web-form--a-1Password-suggestion-will-appear-": function(d) { return "當您在網頁表單中點選密碼欄位時，1Password 將為您建議密碼。"; },
    "You-re-done-": function(d) { return "完成了！"; }
  }
}