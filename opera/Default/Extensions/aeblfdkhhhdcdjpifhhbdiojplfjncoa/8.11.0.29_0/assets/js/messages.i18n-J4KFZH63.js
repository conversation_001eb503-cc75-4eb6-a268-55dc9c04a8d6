
export default {
  de: {
    "-accountName--account-page": function(d) { return d.accountName + "-Kontoseite"; },
    "-appName--information": function(d) { return d.appName + "-Informationen"; },
    "Access-requested": function(d) { return "Zugriff angefordert"; },
    "Account-Icon": function(d) { return "Konto-Symbol"; },
    "Add-Shortcut": function(d) { return "Tastaturkürzel hinzufügen"; },
    "Add-URL-shortcut": function(d) { return "URL-Tastaturkürzel hinzufügen"; },
    "Add-custom-URL": function(d) { return "Benutzerdefinierte URL hinzufügen"; },
    "Add-shortcut": function(d) { return "Tastaturkürzel hinzufügen"; },
    "All-Bookmarks": function(d) { return "Alle Lesezeichen"; },
    "All-Good": function(d) { return "Alles gut"; },
    Alphabetically: function(d) { return "Alphabetisch"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "Lesezeichen beim Ö<PERSON>nen eines neuen Tabs immer anzeigen?"; },
    Apps: function(d) { return "Apps"; },
    Close: function(d) { return "Schließen"; },
    "Device-Health-Info": function(d) { return "Informationen zum Gerätezustand"; },
    Empty: function(d) { return "Leer"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "Weitere unter " + d.employer + " verfügbare Apps entdecken"; },
    "Finding-any-app-is-easy": function(d) { return "Das Finden von Apps ist einfach"; },
    "Frequently-used": function(d) { return "Häufig verwendet"; },
    "Loading---": function(d) { return "Wird geladen …"; },
    "Manage-Bookmarks": function(d) { return "Lesezeichen verwalten"; },
    "Multiple-blocking-issues": function(d) { return "Mehrere Blockierungsprobleme"; },
    Next: function(d) { return "Weiter"; },
    "No-results-for": function(d) { return "Keine Ergebnisse für"; },
    "Not-now": function(d) { return "Nicht jetzt"; },
    "Open---Fill": function(d) { return "Öffnen und einfügen"; },
    Other: function(d) { return "Andere"; },
    "Other-issues": function(d) { return "Andere Probleme"; },
    "Other-pending-issues": function(d) { return "Weitere offene Probleme"; },
    "Recently-used": function(d) { return "Zuletzt verwendet"; },
    "Search-apps-or-the-web": function(d) { return "Apps oder das Internet durchsuchen"; },
    "Search-for-an-app-or-item": function(d) { return "Nach einer App oder einem Objekt suchen"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "Suchen Sie oder scrollen Sie nach unten, um alles zu sehen, worauf Sie Zugriff haben. Passen Sie Ihre Erfahrung an oder deaktivieren Sie sie jederzeit in den <link>Einstellungen</link>."; },
    "Search-the-web-for": function(d) { return "Im Internet suchen nach"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "Wählen Sie die App aus, die Sie starten möchten, und schon kann es losgehen."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "Ihre Apps wurden von " + d.employer + " mit 1Password eingerichtet und können nun gestartet werden."; },
    "Show-bookmarks": function(d) { return "Lesezeichen anzeigen"; },
    "Show-categories": function(d) { return "Kategorien anzeigen"; },
    Sort: function(d) { return "Sortieren"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "Starten Sie jeden Tab mit Ihren Arbeitsanwendungen"; },
    "Submit-feedback": function(d) { return "Feedback senden"; },
    "Unlock-1Password-to-view-apps": function(d) { return "Entsperren Sie 1Password, um Apps anzuzeigen"; },
    "View-Item": function(d) { return "Objekt anzeigen"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "Sie können in den Einstellungen festlegen, dass die Lesezeichenleiste immer angezeigt wird"; },
    "Your-top-apps--up-front": function(d) { return "Ihre beliebtesten Apps an erster Stelle"; },
    "your-employer": function(d) { return "Ihr Arbeitgeber"; }
  },
  en: {
    "-accountName--account-page": function(d) { return d.accountName + " account page"; },
    "-appName--information": function(d) { return d.appName + " information"; },
    "Access-requested": function(d) { return "Access requested"; },
    "Account-Icon": function(d) { return "Account Icon"; },
    "Add-Shortcut": function(d) { return "Add Shortcut"; },
    "Add-URL-shortcut": function(d) { return "Add URL shortcut"; },
    "Add-custom-URL": function(d) { return "Add custom URL"; },
    "Add-shortcut": function(d) { return "Add shortcut"; },
    "All-Bookmarks": function(d) { return "All Bookmarks"; },
    "All-Good": function(d) { return "All Good"; },
    Alphabetically: function(d) { return "Alphabetically"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "Always show bookmarks when you open a new tab?"; },
    Apps: function(d) { return "Apps"; },
    Close: function(d) { return "Close"; },
    "Device-Health-Info": function(d) { return "Device Health Info"; },
    Empty: function(d) { return "Empty"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "Explore more apps available at " + d.employer; },
    "Finding-any-app-is-easy": function(d) { return "Finding any app is easy"; },
    "Frequently-used": function(d) { return "Frequently used"; },
    "Loading---": function(d) { return "Loading..."; },
    "Manage-Bookmarks": function(d) { return "Manage Bookmarks"; },
    "Multiple-blocking-issues": function(d) { return "Multiple blocking issues"; },
    Name: function(d) { return "Name"; },
    Next: function(d) { return "Next"; },
    "No-results-for": function(d) { return "No results for"; },
    "Not-now": function(d) { return "Not now"; },
    "Open---Fill": function(d) { return "Open & Fill"; },
    Other: function(d) { return "Other"; },
    "Other-issues": function(d) { return "Other issues"; },
    "Other-pending-issues": function(d) { return "Other pending issues"; },
    "Recently-used": function(d) { return "Recently used"; },
    "Search-apps-or-the-web": function(d) { return "Search apps or the web"; },
    "Search-for-an-app-or-item": function(d) { return "Search for an app or item"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "Search or scroll down to see everything you have access to. Customize your experience or turn it off anytime in <link>settings</link>."; },
    "Search-the-web-for": function(d) { return "Search the web for"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "Select the app you want to launch and you're there."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "Set up by " + d.employer + " with 1Password, your apps are ready for you to launch."; },
    "Show-bookmarks": function(d) { return "Show bookmarks"; },
    "Show-categories": function(d) { return "Show categories"; },
    Sort: function(d) { return "Sort"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "Start every tab with your work apps"; },
    "Submit-feedback": function(d) { return "Submit feedback"; },
    URL: function(d) { return "URL"; },
    "Unlock-1Password-to-view-apps": function(d) { return "Unlock 1Password to view apps"; },
    "View-Item": function(d) { return "View Item"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "You can set the Bookmarks Bar to always show from Settings"; },
    "Your-top-apps--up-front": function(d) { return "Your top apps, up front"; },
    "your-employer": function(d) { return "your employer"; }
  },
  es: {
    "-accountName--account-page": function(d) { return "Página de la cuenta " + d.accountName; },
    "-appName--information": function(d) { return "Información de " + d.appName; },
    "Access-requested": function(d) { return "Acceso solicitado"; },
    "Account-Icon": function(d) { return "Icono de la cuenta"; },
    "Add-Shortcut": function(d) { return "Añadir atajo"; },
    "Add-URL-shortcut": function(d) { return "Añadir atajo de URL"; },
    "Add-custom-URL": function(d) { return "Añadir URL personalizada"; },
    "Add-shortcut": function(d) { return "Añadir atajo"; },
    "All-Bookmarks": function(d) { return "Todos los marcadores"; },
    "All-Good": function(d) { return "Todo bien"; },
    Alphabetically: function(d) { return "Alfabéticamente"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "¿Mostrar siempre los marcadores al abrir una nueva pestaña?"; },
    Apps: function(d) { return "Aplicaciones"; },
    Close: function(d) { return "Cerrar"; },
    "Device-Health-Info": function(d) { return "Información del estado del dispositivo"; },
    Empty: function(d) { return "Vacío"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "Explorar más aplicaciones disponibles en " + d.employer; },
    "Finding-any-app-is-easy": function(d) { return "Encontrar cualquier aplicación es sencillo"; },
    "Frequently-used": function(d) { return "Usado frecuentemente"; },
    "Loading---": function(d) { return "Cargando…"; },
    "Manage-Bookmarks": function(d) { return "Administrar marcadores"; },
    "Multiple-blocking-issues": function(d) { return "Múltiples problemas de bloqueo"; },
    Next: function(d) { return "Siguiente"; },
    "No-results-for": function(d) { return "No hay resultados para"; },
    "Not-now": function(d) { return "Ahora no"; },
    "Open---Fill": function(d) { return "Abrir y rellenar"; },
    Other: function(d) { return "Otros"; },
    "Other-issues": function(d) { return "Otros problemas"; },
    "Other-pending-issues": function(d) { return "Otros problemas pendientes"; },
    "Recently-used": function(d) { return "Usado recientemente"; },
    "Search-apps-or-the-web": function(d) { return "Buscar aplicaciones o la web"; },
    "Search-for-an-app-or-item": function(d) { return "Buscar una aplicación o elemento"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "Busca o navega para ver todo a lo que tienes acceso. Personaliza tu experiencia o desactívala en cualquier momento desde <link>ajustes</link>."; },
    "Search-the-web-for": function(d) { return "Buscar en la web"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "Simplemente selecciona la aplicación que deseas lanzar."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "Configuradas por " + d.employer + " con 1Password, tus aplicaciones están listas para lanzarse."; },
    "Show-bookmarks": function(d) { return "Mostrar marcadores"; },
    "Show-categories": function(d) { return "Mostrar categorías"; },
    Sort: function(d) { return "Ordenar"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "Abrir cada pestaña con tus aplicaciones de trabajo"; },
    "Submit-feedback": function(d) { return "Enviar sugerencias"; },
    "Unlock-1Password-to-view-apps": function(d) { return "Desbloquear 1Password para ver las aplicaciones"; },
    "View-Item": function(d) { return "Ver elemento"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "Puedes configurar la barra de marcadores para que esté siempre visible desde Ajustes"; },
    "Your-top-apps--up-front": function(d) { return "Tus aplicaciones favoritas, al frente"; },
    "your-employer": function(d) { return "tu empleador"; }
  },
  fr: {
    "-accountName--account-page": function(d) { return "Page du compte " + d.accountName; },
    "-appName--information": function(d) { return "Informations de " + d.appName; },
    "Access-requested": function(d) { return "Accès demandé"; },
    "Account-Icon": function(d) { return "Icône du compte"; },
    "Add-Shortcut": function(d) { return "Ajouter un raccourci"; },
    "Add-URL-shortcut": function(d) { return "Ajouter un raccourci URL"; },
    "Add-custom-URL": function(d) { return "Ajouter une URL personnalisée"; },
    "Add-shortcut": function(d) { return "Ajouter un raccourci"; },
    "All-Bookmarks": function(d) { return "Tous les signets"; },
    "All-Good": function(d) { return "Terminé"; },
    Alphabetically: function(d) { return "Par ordre alphabétique"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "Toujours afficher les signets lorsque vous ouvrez un nouvel onglet ?"; },
    Apps: function(d) { return "Applications"; },
    Close: function(d) { return "Fermer"; },
    "Device-Health-Info": function(d) { return "Informations sur l'état de l'appareil"; },
    Empty: function(d) { return "Vide"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "Explorer plus d'applications disponibles sur " + d.employer; },
    "Finding-any-app-is-easy": function(d) { return "Trouver une application est facile"; },
    "Frequently-used": function(d) { return "Fréquemment utilisé"; },
    "Loading---": function(d) { return "Chargement..."; },
    "Manage-Bookmarks": function(d) { return "Gérer les signets"; },
    "Multiple-blocking-issues": function(d) { return "Plusieurs problèmes de blocage"; },
    Next: function(d) { return "Suivant"; },
    "No-results-for": function(d) { return "Aucun résultat pour"; },
    "Not-now": function(d) { return "Pas maintenant"; },
    "Open---Fill": function(d) { return "Ouvrir et remplir"; },
    Other: function(d) { return "Autre"; },
    "Other-issues": function(d) { return "Autres problèmes"; },
    "Other-pending-issues": function(d) { return "Autres problèmes en attente"; },
    "Recently-used": function(d) { return "Récemment utilisé"; },
    "Search-apps-or-the-web": function(d) { return "Rechercher des applications ou sur le web"; },
    "Search-for-an-app-or-item": function(d) { return "Rechercher une application ou un élément"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "Recherchez ou parcourez la liste pour voir tout ce à quoi vous avez accès. Personnalisez votre expérience ou désactivez-la à tout moment dans les <link>paramètres</link>."; },
    "Search-the-web-for": function(d) { return "Rechercher sur le web pour"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "Sélectionnez l'application que vous souhaitez lancer et voilà."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "Configurées par " + d.employer + " avec 1Password, vos applications sont prêtes à être lancées."; },
    "Show-bookmarks": function(d) { return "Afficher les signets"; },
    "Show-categories": function(d) { return "Afficher les catégories"; },
    Sort: function(d) { return "Trier"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "Lancer chaque onglet avec vos applications professionnelles"; },
    "Submit-feedback": function(d) { return "Envoyer des commentaires"; },
    "Unlock-1Password-to-view-apps": function(d) { return "Déverrouiller 1Password pour afficher les applications"; },
    "View-Item": function(d) { return "Afficher l'élément"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "Vous pouvez configurer l'affichage permanent de la barre de signets dans les paramètres"; },
    "Your-top-apps--up-front": function(d) { return "Vos applications préférées, à portée de main"; },
    "your-employer": function(d) { return "votre employeur"; }
  },
  it: {
    "-accountName--account-page": function(d) { return "Pagina dell’account " + d.accountName; },
    "-appName--information": function(d) { return "Informazioni su " + d.appName; },
    "Access-requested": function(d) { return "Accesso richiesto"; },
    "Account-Icon": function(d) { return "Icona dell’account"; },
    "Add-Shortcut": function(d) { return "Aggiungi scelta rapida"; },
    "Add-URL-shortcut": function(d) { return "Aggiungi scelta rapida URL"; },
    "Add-custom-URL": function(d) { return "Aggiungi URL personalizzato"; },
    "Add-shortcut": function(d) { return "Aggiungi scelta rapida"; },
    "All-Bookmarks": function(d) { return "Tutti i segnalibri"; },
    "All-Good": function(d) { return "Tutto perfetto"; },
    Alphabetically: function(d) { return "In ordine alfabetico"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "Vuoi che i segnalibri siano sempre visibili quando apri una nuova scheda?"; },
    Apps: function(d) { return "App"; },
    Close: function(d) { return "Chiudi"; },
    "Device-Health-Info": function(d) { return "Informazioni sull’integrità del dispositivo"; },
    Empty: function(d) { return "Vuoto"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "Esplora altre app disponibili su " + d.employer; },
    "Finding-any-app-is-easy": function(d) { return "Trovare l’app che ti serve è semplice"; },
    "Frequently-used": function(d) { return "Utilizzate di frequente"; },
    "Loading---": function(d) { return "Caricamento..."; },
    "Manage-Bookmarks": function(d) { return "Gestisci segnalibri"; },
    "Multiple-blocking-issues": function(d) { return "Diversi problemi di blocco"; },
    Next: function(d) { return "Successivo"; },
    "No-results-for": function(d) { return "Nessun risultato per"; },
    "Not-now": function(d) { return "Non ora"; },
    "Open---Fill": function(d) { return "Apri e compila"; },
    Other: function(d) { return "Altro"; },
    "Other-issues": function(d) { return "Altri problemi"; },
    "Other-pending-issues": function(d) { return "Altri problemi in sospeso"; },
    "Recently-used": function(d) { return "Utilizzate di recente"; },
    "Search-apps-or-the-web": function(d) { return "Cerca app o sul web"; },
    "Search-for-an-app-or-item": function(d) { return "Cerca un elemento o una app"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "Cerca o scorri verso il basso per visualizzare tutto ciò a cui hai accesso. Personalizza la tua esperienza o disattiva in qualsiasi momento nelle <link>impostazioni</link>."; },
    "Search-the-web-for": function(d) { return "Cerca sul web"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "Scegli l’app che vuoi avviare e il gioco è fatto."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "Configurate dal " + d.employer + " con 1Password, le tue app sono pronte per essere avviate."; },
    "Show-bookmarks": function(d) { return "Mostra segnalibri"; },
    "Show-categories": function(d) { return "Mostra categorie"; },
    Sort: function(d) { return "Ordina"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "Apri ogni scheda con le tue app di lavoro"; },
    "Submit-feedback": function(d) { return "Invia feedback"; },
    "Unlock-1Password-to-view-apps": function(d) { return "Sblocca 1Password per visualizzare le app"; },
    "View-Item": function(d) { return "Visualizza elemento"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "Puoi impostare la barra dei segnalibri in modo che sia sempre visibile dalle Impostazioni"; },
    "Your-top-apps--up-front": function(d) { return "Le tue app preferite, sempre a portata di mano"; },
    "your-employer": function(d) { return "tuo capo"; }
  },
  ja: {
    "-accountName--account-page": function(d) { return d.accountName + "アカウントページ"; },
    "-appName--information": function(d) { return d.appName + "情報"; },
    "Access-requested": function(d) { return "アクセスがリクエストされました"; },
    "Account-Icon": function(d) { return "アカウントアイコン"; },
    "Add-Shortcut": function(d) { return "ショートカットを追加する"; },
    "Add-URL-shortcut": function(d) { return "URLショートカットを追加する"; },
    "Add-custom-URL": function(d) { return "カスタムURLを追加する"; },
    "Add-shortcut": function(d) { return "ショートカットを追加する"; },
    "All-Bookmarks": function(d) { return "すべてのブックマーク"; },
    "All-Good": function(d) { return "すべて良い"; },
    Alphabetically: function(d) { return "アルファベット順"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "新しいタブを開いたときにブックマークを常に表示しますか？"; },
    Apps: function(d) { return "アプリ"; },
    Close: function(d) { return "閉じる"; },
    "Device-Health-Info": function(d) { return "デバイスの健康情報"; },
    Empty: function(d) { return "空"; },
    "Explore-more-apps-available-at--employer-": function(d) { return d.employer + "で利用可能なその他のアプリを調べる"; },
    "Finding-any-app-is-easy": function(d) { return "アプリの検索は簡単です"; },
    "Frequently-used": function(d) { return "頻繁に使用"; },
    "Loading---": function(d) { return "読み込んでいます…"; },
    "Manage-Bookmarks": function(d) { return "ブックマークを管理する"; },
    "Multiple-blocking-issues": function(d) { return "複数のブロッキング問題"; },
    Next: function(d) { return "次へ"; },
    "No-results-for": function(d) { return "結果が見つかりません"; },
    "Not-now": function(d) { return "今はしない"; },
    "Open---Fill": function(d) { return "開いて入力"; },
    Other: function(d) { return "その他"; },
    "Other-issues": function(d) { return "その他の問題"; },
    "Other-pending-issues": function(d) { return "その他の未解決の問題"; },
    "Recently-used": function(d) { return "最近使用されたもの"; },
    "Search-apps-or-the-web": function(d) { return "アプリまたはウェブを検索する"; },
    "Search-for-an-app-or-item": function(d) { return "アプリまたはアイテムを検索する"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "検索またはスクロールダウンして、アクセスできるすべてのものを見ることができます。<link>設定</link> でエクスペリエンスをカスタマイズしたり、いつでもオフにすることができます。"; },
    "Search-the-web-for": function(d) { return "ウェブを検索する"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "起動したいアプリを選択すれば、そこで完了です。"; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return d.employer + "が1Passwordでセットアップすれば、アプリの起動準備は完了です。"; },
    "Show-bookmarks": function(d) { return "ブックマークを表示する"; },
    "Show-categories": function(d) { return "カテゴリーを表示"; },
    Sort: function(d) { return "並べ替え"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "すべてのタブで仕事用アプリを起動する"; },
    "Submit-feedback": function(d) { return "フィードバックを送信する"; },
    "Unlock-1Password-to-view-apps": function(d) { return "1Passwordのロックを解除してアプリを見る"; },
    "View-Item": function(d) { return "アイテムを表示"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "設定からブックマークバーを常に表示するように設定できます"; },
    "Your-top-apps--up-front": function(d) { return "トップアプリケーションを前もって"; },
    "your-employer": function(d) { return "従業員"; }
  },
  ko: {
    "-accountName--account-page": function(d) { return d.accountName + " 계정 페이지"; },
    "-appName--information": function(d) { return d.appName + " 정보"; },
    "Access-requested": function(d) { return "액세스 권한 요청함"; },
    "Account-Icon": function(d) { return "계정 아이콘"; },
    "Add-Shortcut": function(d) { return "단축키 추가"; },
    "Add-URL-shortcut": function(d) { return "URL 단축키 추가"; },
    "Add-custom-URL": function(d) { return "사용자 지정 URL 추가"; },
    "Add-shortcut": function(d) { return "단축키 추가"; },
    "All-Bookmarks": function(d) { return "모든 북마크"; },
    "All-Good": function(d) { return "다 되었습니다"; },
    Alphabetically: function(d) { return "알파벳순"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "새 탭을 열 때 항상 북마크를 표시할까요?"; },
    Apps: function(d) { return "앱"; },
    Close: function(d) { return "닫기"; },
    "Device-Health-Info": function(d) { return "장치 양호 상태 검사"; },
    Empty: function(d) { return "비어 있음"; },
    "Explore-more-apps-available-at--employer-": function(d) { return d.employer + "에서 사용 가능한 앱 더 살펴보기"; },
    "Finding-any-app-is-easy": function(d) { return "어떤 앱이든 쉽게 찾을 수 있습니다"; },
    "Frequently-used": function(d) { return "자주 사용"; },
    "Loading---": function(d) { return "불러오는 중..."; },
    "Manage-Bookmarks": function(d) { return "북마크 관리"; },
    "Multiple-blocking-issues": function(d) { return "복수의 차단 문제"; },
    Next: function(d) { return "다음"; },
    "No-results-for": function(d) { return "다음에 대한 결과 없음:"; },
    "Not-now": function(d) { return "나중에"; },
    "Open---Fill": function(d) { return "열기 및 채우기"; },
    Other: function(d) { return "기타"; },
    "Other-issues": function(d) { return "기타 문제"; },
    "Other-pending-issues": function(d) { return "기타 미해결 문제"; },
    "Recently-used": function(d) { return "최근 사용"; },
    "Search-apps-or-the-web": function(d) { return "앱 또는 웹 검색"; },
    "Search-for-an-app-or-item": function(d) { return "항목 또는 항목 검색"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "검색하거나 아래로 스크롤하여 액세스 권한이 있는 모든 항목을 확인할 수 있습니다. <link>설정</link>에서 환경을 맞춤 설정하거나 비활성화하세요."; },
    "Search-the-web-for": function(d) { return "다음에 대한 웹 검색:"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "시작하려는 앱을 선택하시면 됩니다."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "1Password를 통해 " + d.employer + "에서 설정 완료, 앱이 시작 준비가 되었습니다."; },
    "Show-bookmarks": function(d) { return "북마크 표시"; },
    "Show-categories": function(d) { return "카테고리 표시"; },
    Sort: function(d) { return "정렬"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "업무용 앱으로 모든 탭 시작"; },
    "Submit-feedback": function(d) { return "피드백 제출"; },
    "Unlock-1Password-to-view-apps": function(d) { return "앱을 보려면 1Password를 잠금 해제하세요"; },
    "View-Item": function(d) { return "항목 보기"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "'설정'에서 북마크 표시줄이 항상 표시되도록 설정할 수 있습니다"; },
    "Your-top-apps--up-front": function(d) { return "가장 많이 사용하는 앱, 가장 먼저 표시"; },
    "your-employer": function(d) { return "귀하의 고용주"; }
  },
  nl: {
    "-accountName--account-page": function(d) { return "Accountpagina " + d.accountName; },
    "-appName--information": function(d) { return "Gegevens " + d.appName; },
    "Access-requested": function(d) { return "Toegangsverzoek"; },
    "Account-Icon": function(d) { return "Accountpictogram"; },
    "Add-Shortcut": function(d) { return "Snelkoppeling toevoegen"; },
    "Add-URL-shortcut": function(d) { return "Snelkoppelingslink toevoegen"; },
    "Add-custom-URL": function(d) { return "Eigen link toevoegen"; },
    "Add-shortcut": function(d) { return "Snelkoppeling toevoegen"; },
    "All-Bookmarks": function(d) { return "Alle bladwijzers"; },
    "All-Good": function(d) { return "Alles in orde"; },
    Alphabetically: function(d) { return "Alfabetisch"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "Altijd bladwijzers tonen bij het openen van een nieuw tabblad?"; },
    Apps: function(d) { return "Apps"; },
    Close: function(d) { return "Sluiten"; },
    "Device-Health-Info": function(d) { return "Apparaatstatus"; },
    Empty: function(d) { return "Leeg"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "Meer beschikbare apps bekijken voor " + d.employer; },
    "Finding-any-app-is-easy": function(d) { return "Apps vind je eenvoudig"; },
    "Frequently-used": function(d) { return "Vaak gebruikt"; },
    "Loading---": function(d) { return "Laden..."; },
    "Manage-Bookmarks": function(d) { return "Bladwijzers beheren"; },
    "Multiple-blocking-issues": function(d) { return "Meerdere problemen met blokkeren"; },
    Next: function(d) { return "Volgende"; },
    "No-results-for": function(d) { return "Geen resultaten voor"; },
    "Not-now": function(d) { return "Niet nu"; },
    "Open---Fill": function(d) { return "Openen en aanvullen"; },
    Other: function(d) { return "Overig"; },
    "Other-issues": function(d) { return "Overige problemen"; },
    "Other-pending-issues": function(d) { return "Andere lopende problemen"; },
    "Recently-used": function(d) { return "Recent gebruikt"; },
    "Search-apps-or-the-web": function(d) { return "Apps doorzoeken of online zoeken"; },
    "Search-for-an-app-or-item": function(d) { return "Naar een app of item zoeken"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "Zoek of scroll naar beneden om alles te zien waartoe je toegang hebt. Pas je ervaring aan of schakel deze uit via <link>instellingen</link>."; },
    "Search-the-web-for": function(d) { return "Zoek online naar"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "Selecteer de app die je wilt starten en je bent er."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "Apps ingesteld door " + d.employer + " met 1Password staan klaar om te openen."; },
    "Show-bookmarks": function(d) { return "Bladwijzers weergeven"; },
    "Show-categories": function(d) { return "Categorieën weergeven"; },
    Sort: function(d) { return "Sorteren"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "Open elke tab met je werkapps"; },
    "Submit-feedback": function(d) { return "Feedback achterlaten"; },
    "Unlock-1Password-to-view-apps": function(d) { return "Ontgrendel 1Password om apps te bekijken"; },
    "View-Item": function(d) { return "Item weergeven"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "Je kunt via de instellingen instellen dat de bladwijzerbalk altijd wordt weergegeven"; },
    "Your-top-apps--up-front": function(d) { return "Ja favoriete apps vooraan"; },
    "your-employer": function(d) { return "je werkgever"; }
  },
  pt: {
    "-accountName--account-page": function(d) { return "Página da conta " + d.accountName; },
    "-appName--information": function(d) { return "Informações sobre " + d.appName; },
    "Access-requested": function(d) { return "Acesso solicitado"; },
    "Account-Icon": function(d) { return "Ícone da conta"; },
    "Add-Shortcut": function(d) { return "Adicionar atalho"; },
    "Add-URL-shortcut": function(d) { return "Adicionar atalho de URL"; },
    "Add-custom-URL": function(d) { return "Adicionar URL personalizado"; },
    "Add-shortcut": function(d) { return "Adicionar atalho"; },
    "All-Bookmarks": function(d) { return "Todos os favoritos"; },
    "All-Good": function(d) { return "Tudo certo"; },
    Alphabetically: function(d) { return "Em ordem alfabética"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "Sempre mostrar favoritos ao abrir uma nova aba?"; },
    Apps: function(d) { return "Aplicativos"; },
    Close: function(d) { return "Fechar"; },
    "Device-Health-Info": function(d) { return "Informações sobre o estado do dispositivo"; },
    Empty: function(d) { return "Vazio"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "Explore mais aplicativos disponíveis em " + d.employer; },
    "Finding-any-app-is-easy": function(d) { return "Encontrar qualquer aplicativo é fácil"; },
    "Frequently-used": function(d) { return "Usados com frequência"; },
    "Loading---": function(d) { return "Carregando..."; },
    "Manage-Bookmarks": function(d) { return "Gerenciar favoritos"; },
    "Multiple-blocking-issues": function(d) { return "Múltiplos problemas de bloqueio"; },
    Next: function(d) { return "Próximo"; },
    "No-results-for": function(d) { return "Nenhum resultado para"; },
    "Not-now": function(d) { return "Agora não"; },
    "Open---Fill": function(d) { return "Abrir e preencher"; },
    Other: function(d) { return "Outro"; },
    "Other-issues": function(d) { return "Outros problemas"; },
    "Other-pending-issues": function(d) { return "Outros problemas pendentes"; },
    "Recently-used": function(d) { return "Usados recentemente"; },
    "Search-apps-or-the-web": function(d) { return "Pesquisar aplicativos ou a rede"; },
    "Search-for-an-app-or-item": function(d) { return "Pesquisar por um app ou item"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "Pesquise ou role para baixo para ver tudo a que você tem acesso. Personalize sua experiência ou desative-a a qualquer momento em <link>configurações</link>."; },
    "Search-the-web-for": function(d) { return "Pesquise na “web” por"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "Selecione o aplicativo que você deseja abrir e pronto."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "Configuradas por " + d.employer + " com o 1Password, seus aplicativos estão prontos para serem abertos."; },
    "Show-bookmarks": function(d) { return "Mostrar favoritos"; },
    "Show-categories": function(d) { return "Mostrar categorias"; },
    Sort: function(d) { return "Ordenar"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "Inicie cada aba com seus aplicativos profissionais"; },
    "Submit-feedback": function(d) { return "Enviar comentários"; },
    "Unlock-1Password-to-view-apps": function(d) { return "Desbloquear o 1Password para exibir os aplicativos"; },
    "View-Item": function(d) { return "Exibir o item"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "Você pode configurar a exibição permanente da barra de favoritos nas configurações"; },
    "Your-top-apps--up-front": function(d) { return "Seus aplicativos favoritos, ao seu alcance"; },
    "your-employer": function(d) { return "seu empregador"; }
  },
  ru: {
    "-accountName--account-page": function(d) { return "Страница аккаунта " + d.accountName; },
    "-appName--information": function(d) { return "Информация о приложении " + d.appName; },
    "Access-requested": function(d) { return "Доступ запрошен"; },
    "Account-Icon": function(d) { return "Иконка аккаунта"; },
    "Add-Shortcut": function(d) { return "Добавить сочетание клавиш"; },
    "Add-URL-shortcut": function(d) { return "Добавить сочетание клавиш для URL-ссылки"; },
    "Add-custom-URL": function(d) { return "Добавить собственную URL-ссылку"; },
    "Add-shortcut": function(d) { return "Добавить сочетание клавиш"; },
    "All-Bookmarks": function(d) { return "Все закладки"; },
    "All-Good": function(d) { return "Все хорошо"; },
    Alphabetically: function(d) { return "В алфавитном порядке"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "Всегда показывать меню закладок в новой вкладке?"; },
    Apps: function(d) { return "Приложения"; },
    Close: function(d) { return "Закрыть"; },
    "Device-Health-Info": function(d) { return "Проверка состояния устройства"; },
    Empty: function(d) { return "Очистить"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "Смотреть другие доступные приложения для " + d.employer; },
    "Finding-any-app-is-easy": function(d) { return "Вы легко найдете любое приложение"; },
    "Frequently-used": function(d) { return "Часто используемые"; },
    "Loading---": function(d) { return "Загрузка..."; },
    "Manage-Bookmarks": function(d) { return "Управлять закладками"; },
    "Multiple-blocking-issues": function(d) { return "Несколько серьезных проблем"; },
    Next: function(d) { return "Дальше"; },
    "No-results-for": function(d) { return "Нет результатов для"; },
    "Not-now": function(d) { return "Не сейчас"; },
    "Open---Fill": function(d) { return "Открыть и заполнить"; },
    Other: function(d) { return "Другое"; },
    "Other-issues": function(d) { return "Другие ошибки"; },
    "Other-pending-issues": function(d) { return "Другие неразрешенные ошибки"; },
    "Recently-used": function(d) { return "Недавно использовано"; },
    "Search-apps-or-the-web": function(d) { return "Поиск в приложениях или в Интернете"; },
    "Search-for-an-app-or-item": function(d) { return "Искать приложение или элемент"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "Воспользуйтесь функцией поиска или прокрутите вниз, чтобы увидеть все, к чему у вас есть доступ. Измените или отключите доступ в любое время в <link>Настройках</link>."; },
    "Search-the-web-for": function(d) { return "Найдите в Интернете:"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "Просто выберите приложение, которое хотите запустить."; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "Ваши приложения настроены " + d.employer + " с помощью 1Password и готовы к запуску."; },
    "Show-bookmarks": function(d) { return "Показать закладки"; },
    "Show-categories": function(d) { return "Показать категории"; },
    Sort: function(d) { return "Сортировать"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "Начало каждой вкладки с ваших рабочих приложений"; },
    "Submit-feedback": function(d) { return "Отправить отзыв"; },
    "Unlock-1Password-to-view-apps": function(d) { return "Разблокировать 1Password для просмотра приложений"; },
    "View-Item": function(d) { return "Смотреть элемент"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "Вы можете настроить меню закладок так, чтобы оно всегда отображалось в Настройках"; },
    "Your-top-apps--up-front": function(d) { return "В первую очередь самые важные приложения"; },
    "your-employer": function(d) { return "ваш работодатель"; }
  },
  "zh-CN": {
    "-accountName--account-page": function(d) { return d.accountName + " 帐户页面"; },
    "-appName--information": function(d) { return d.appName + " 信息"; },
    "Access-requested": function(d) { return "已请求访问"; },
    "Account-Icon": function(d) { return "帐户图标"; },
    "Add-Shortcut": function(d) { return "添加快捷键"; },
    "Add-URL-shortcut": function(d) { return "添加 URL 快捷键"; },
    "Add-custom-URL": function(d) { return "添加自定义 URL"; },
    "Add-shortcut": function(d) { return "添加快捷键"; },
    "All-Bookmarks": function(d) { return "所有书签"; },
    "All-Good": function(d) { return "一切正常"; },
    Alphabetically: function(d) { return "按字母顺序"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "在打开新标签页时始终显示书签？\n"; },
    Apps: function(d) { return "应用"; },
    Close: function(d) { return "关闭"; },
    "Device-Health-Info": function(d) { return "设备健康信息"; },
    Empty: function(d) { return "空"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "发现更多 " + d.employer + " 可用的应用"; },
    "Finding-any-app-is-easy": function(d) { return "寻找任何应用都很简单"; },
    "Frequently-used": function(d) { return "经常使用的"; },
    "Loading---": function(d) { return "正在加载..."; },
    "Manage-Bookmarks": function(d) { return "管理书签"; },
    "Multiple-blocking-issues": function(d) { return "多个阻止问题"; },
    Next: function(d) { return "下一步"; },
    "No-results-for": function(d) { return "无结果："; },
    "Not-now": function(d) { return "稍后再说"; },
    "Open---Fill": function(d) { return "打开并填充"; },
    Other: function(d) { return "其他"; },
    "Other-issues": function(d) { return "其他问题"; },
    "Other-pending-issues": function(d) { return "其他待处理的问题"; },
    "Recently-used": function(d) { return "最近使用的"; },
    "Search-apps-or-the-web": function(d) { return "搜索应用或网页"; },
    "Search-for-an-app-or-item": function(d) { return "搜索应用或项目"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "搜索或向下滚动以查看您有权访问的所有内容。您可以在<link>设置</link>中随时自定义体验或将其关闭。"; },
    "Search-the-web-for": function(d) { return "在网络上搜索"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "选择您要启动的应用，即可进入。"; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "由 " + d.employer + " 通过 1Password 设置，您的应用已准备就绪，随时可启动。"; },
    "Show-bookmarks": function(d) { return "显示书签"; },
    "Show-categories": function(d) { return "显示类别"; },
    Sort: function(d) { return "排序"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "每个标签页都从您的工作应用开始"; },
    "Submit-feedback": function(d) { return "提交反馈"; },
    "Unlock-1Password-to-view-apps": function(d) { return "解锁 1Password 以查看应用"; },
    "View-Item": function(d) { return "查看项目"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "您可以将书签栏设置为始终从“设置”中显示"; },
    "Your-top-apps--up-front": function(d) { return "您的热门应用，尽在眼前"; },
    "your-employer": function(d) { return "你的雇主"; }
  },
  "zh-TW": {
    "-accountName--account-page": function(d) { return d.accountName + " 帳號頁面"; },
    "-appName--information": function(d) { return d.appName + " 資訊"; },
    "Access-requested": function(d) { return "已請求存取"; },
    "Account-Icon": function(d) { return "帳號圖示"; },
    "Add-Shortcut": function(d) { return "新增 URL 快速鍵"; },
    "Add-URL-shortcut": function(d) { return "新增 URL 快速鍵"; },
    "Add-custom-URL": function(d) { return "新增自訂 URL"; },
    "Add-shortcut": function(d) { return "新增快速鍵"; },
    "All-Bookmarks": function(d) { return "所有書籤"; },
    "All-Good": function(d) { return "一切正常"; },
    Alphabetically: function(d) { return "按字母排序"; },
    "Always-show-bookmarks-when-you-open-a-new-tab-": function(d) { return "在開啟新標簽頁時始終顯示書籤？"; },
    Apps: function(d) { return "應用程式"; },
    Close: function(d) { return "關閉"; },
    "Device-Health-Info": function(d) { return "裝置健康資訊"; },
    Empty: function(d) { return "無內容"; },
    "Explore-more-apps-available-at--employer-": function(d) { return "發現更多 " + d.employer + " 可用的應用程式"; },
    "Finding-any-app-is-easy": function(d) { return "尋找任何應用程式都很簡單"; },
    "Frequently-used": function(d) { return "經常使用的"; },
    "Loading---": function(d) { return "正在載入..."; },
    "Manage-Bookmarks": function(d) { return "管理書籤"; },
    "Multiple-blocking-issues": function(d) { return "多個阻止問題"; },
    Next: function(d) { return "下一步"; },
    "No-results-for": function(d) { return "無結果："; },
    "Not-now": function(d) { return "現在不要"; },
    "Open---Fill": function(d) { return "開啟並填入"; },
    Other: function(d) { return "其他"; },
    "Other-issues": function(d) { return "其他問題"; },
    "Other-pending-issues": function(d) { return "其他待處理的問題"; },
    "Recently-used": function(d) { return "最近使用過的"; },
    "Search-apps-or-the-web": function(d) { return "搜尋應用程式或網頁"; },
    "Search-for-an-app-or-item": function(d) { return "搜尋應用程式或項目"; },
    "Search-or-scroll-down-to-see-everything-you-have-access-to--Customize-your-experience-or-turn-it-off-anytime-in--link-settings--link--": function(d) { return "搜尋或向下捲動以查看您有權存取的所有內容。您可以在<link>設定</link>中隨時自訂體驗或將其關閉。"; },
    "Search-the-web-for": function(d) { return "在網路上搜尋"; },
    "Select-the-app-you-want-to-launch-and-you-re-there-": function(d) { return "選擇您要啟動的應用程式，即可進入。"; },
    "Set-up-by--employer--with-1Password--your-apps-are-ready-for-you-to-launch-": function(d) { return "由 " + d.employer + " 透過 1Password 設定，您的應用程式已準備就緒，隨時可啟動。"; },
    "Show-bookmarks": function(d) { return "顯示書籤"; },
    "Show-categories": function(d) { return "顯示類別"; },
    Sort: function(d) { return "排序"; },
    "Start-every-tab-with-your-work-apps": function(d) { return "每個標籤頁都從您的工作應用程式開始"; },
    "Submit-feedback": function(d) { return "提交回饋"; },
    "Unlock-1Password-to-view-apps": function(d) { return "解鎖 1Password 來檢視應用程式"; },
    "View-Item": function(d) { return "檢視項目"; },
    "You-can-set-the-Bookmarks-Bar-to-always-show-from-Settings": function(d) { return "您可以將書籤列設定為始終從「設定」顯示"; },
    "Your-top-apps--up-front": function(d) { return "您的熱門應用程式，盡在眼前"; },
    "your-employer": function(d) { return "你的僱主"; }
  }
}