
export default {
  de: {
    "1Password-is-locked": function(d) { return "1Password ist gesperrt"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password ist gesperrt. Bitte entsperren und erneut versuchen."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "Für die Integration mit Privacy ist eine 1Password-Mitgliedschaft erforderlich."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "Eine Dauer für das Ausgabenlimit ist erforderlich"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Bei der Kommunikation mit Brex.com ist ein Problem aufgetreten. Bitte versuchen Sie es erneut"; },
    "A-title-is-required": function(d) { return "Ein Titel ist erforderlich"; },
    "Add-to-1Password": function(d) { return "Zu 1Password hinzufügen"; },
    "All-vaults-are-disabled-": function(d) { return "Alle Tresore sind deaktiviert."; },
    "An-error-has-occurred": function(d) { return "Es ist ein Fehler aufgetreten"; },
    "An-unexpected-error-occurred-": function(d) { return "Ein unerwarteter Fehler ist aufgetreten."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Es ist ein unerwarteter Fehler aufgetreten. Bitte <NAME_EMAIL>"; },
    "Assign-to-Account": function(d) { return "Zu Konto zuweisen"; },
    Back: function(d) { return "Zurück"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Brex-Karten werden von Brex.com gespeichert und synchronisiert."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex füllt Transaktionsnotizen auf dieser Karte automatisch aus."; },
    Cancel: function(d) { return "Abbrechen"; },
    "Choose-an-account": function(d) { return "Wählen Sie ein Konto"; },
    Close: function(d) { return "Schließen"; },
    "Conceal-previous-value": function(d) { return "Vorherigen Wert verbergen"; },
    Confirm: function(d) { return "Bestätigen"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Mit Privacy Card verbinden …"; },
    "Create---Fill": function(d) { return "Erstellen und ausfüllen"; },
    "Create---Fill-SSH-Key": function(d) { return "SSH-Schlüssel erstellen und ausfüllen"; },
    "Create-Brex-vendor-card-": function(d) { return "Brex-Verkäuferkarte erstellen …"; },
    "Create-Masked-Email": function(d) { return "Maskierte E-Mail erstellen"; },
    "Create-Privacy-Card": function(d) { return "Privacy Card erstellen"; },
    Custom: function(d) { return "Benutzerdefiniert"; },
    "Enter-the-amount-in-dollars": function(d) { return "Betrag in Dollar eingeben"; },
    Error: function(d) { return "Fehler"; },
    "Error-creating-card-": function(d) { return "Fehler beim Erstellen der Karte."; },
    "Error-creating-card---message-": function(d) { return "Fehler beim Erstellen der Karte: " + d.message; },
    "Existing-items": function(d) { return "Bestehende Objekte"; },
    "Fill-Email": function(d) { return "E-Mail ausfüllen"; },
    "Funding-Source": function(d) { return "Finanzierungsquelle"; },
    "Get-Help-": function(d) { return "Hilfe anfordern …"; },
    "Input-field-for-the-item-s-title": function(d) { return "Eingabefeld für den Objekttitel"; },
    "Loading-": function(d) { return "Wird geladen …"; },
    "Lock-Card": function(d) { return "Karte sperren"; },
    "Lock-card-is-required": function(d) { return "Karte sperren ist erforderlich"; },
    "New-Item": function(d) { return "Neues Objekt"; },
    None: function(d) { return "Keine"; },
    OK: function(d) { return "OK"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Nach dem Speichern merken wir es uns für Sie."; },
    "Open-1Password": function(d) { return "1Password öffnen"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "Bitte aktivieren Sie die Privacy-Integration über das Kontextmenü der Developer Tools."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Bitte vergewissern Sie sich, dass mindestens eine Finanzierungsquelle mit Ihrem Privacy.com-Konto verknüpft ist und versuchen Sie es dann erneut."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "Bitte geben Sie einen Kartennamen ein."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "Bitte geben Sie einen kürzeren Kartennamen ein."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "Bitte geben Sie ein geringeres Ausgabenlimit für die Karte ein."; },
    "Please-select-a-vault-": function(d) { return "Wählen Sie einen Tresor aus."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "Bitte entsperren Sie 1Password, um die Integration zu aktivieren."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "Bitte entsperren Sie das ausgewählte Konto, um die Integration zu aktivieren."; },
    "Press--shortcut--to-open-1Password-": function(d) { return "Drücken Sie auf " + d.shortcut + ", um 1Password zu öffnen."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return d.shortcut + "-Kürzel tippen, um 1Password zu entsperren"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "Drücken Sie zum Öffnen von 1Password auf das 1Password-Symbol in der Symbolleiste Ihres Browsers."; },
    Previous: function(d) { return "Vorherige"; },
    "Privacy-Card": function(d) { return "Privacy Card"; },
    "Reason-for-card": function(d) { return "Grund für Karte"; },
    Regenerate: function(d) { return "Regenerieren"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return "Erneut regenerieren in " + d.secondsLeft + " s"; },
    "Reveal-previous-value": function(d) { return "Vorherigen Wert aufdecken"; },
    "Reveal-to-see-previous-value": function(d) { return "Aufdecken, um vorherigen Wert zu sehen"; },
    Save: function(d) { return "Speichern"; },
    "Save-Item": function(d) { return "Objekt speichern"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "Dialog zum Speichern oder Aktualisieren von Informationen in 1Password"; },
    "Select-a-vault": function(d) { return "Tresor wählen"; },
    "Select-account": function(d) { return "Konto auswählen"; },
    "Set-Spending-Limit": function(d) { return "Ausgabenlimit festlegen"; },
    "Single-Use": function(d) { return "Einmaliger Gebrauch"; },
    "Spending-Limit": function(d) { return "Ausgabenlimit"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Schritt 3: Prüfen oder bearbeiten Sie Ihr neues Login-Objekt und speichern Sie es anschließend."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "Die Integration mit Brex wurde deaktiviert. Bitte stellen Sie sie auf 1Password.com wieder her"; },
    "The-reason-for-a-card-is-required": function(d) { return "Der Grund für eine Karte ist erforderlich"; },
    "The-spending-limit-is-required": function(d) { return "Das Ausgabenlimit ist erforderlich"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "Tippen Sie anschließend auf die Offline-Anzeige (<offlineIndicator />) und schließen Sie Ihre Anmeldung ab, um Ihre Änderungen zu speichern."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "Dieser Tresor ist gesperrt. Bitte entsperren und erneut versuchen."; },
    Title: function(d) { return "Titel"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "Um weiter mit 1Password zu speichern, entsperren Sie ein Konto."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Authentifizierung mit „Privacy“ nicht möglich. Bitte überprüfen Sie Ihren API-Schlüssel und versuchen Sie es erneut."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "Karte kann nicht erstellt werden, bitte erneut versuchen."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Es ist nicht möglich, ein Kreditkarten-Objekt von der Privacy Card aus zu erstellen."; },
    "Unable-to-enable-integration": function(d) { return "Integration kann nicht aktiviert werden"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Die Integration von Privacy konnte nicht aktiviert werden. Bitte später erneut versuchen."; },
    "Unable-to-parse-URL": function(d) { return "URL kann nicht geparst werden"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "Objekt konnte nicht gespeichert werden. Prüfen Sie, ob der Tresor entsperrt ist, und versuchen Sie es erneut."; },
    Update: function(d) { return "Aktualisieren"; },
    "Update-Existing": function(d) { return "Bestehende aktualisieren"; },
    "Update-Item": function(d) { return "Objekt aktualisieren"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "Verwenden Sie 1Password, um überall dort, wo Sie online bezahlen, Privacy Cards zu erstellen und auszufüllen und Händlerkarten für die zukünftige Verwendung zu speichern."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "„Privacy“ nicht erreichbar. Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "Sie müssen ein Ausgabenlimit für die Karte angeben."; },
    "Your-account-is-offline-": function(d) { return "Ihr Konto ist offline."; },
    email: function(d) { return "E-Mail"; },
    "every-month": function(d) { return "jeden Monat"; },
    "every-quarter": function(d) { return "jedes Quartal"; },
    "every-transaction": function(d) { return "jede Transaktion"; },
    "every-year": function(d) { return "jedes Jahr"; },
    forever: function(d) { return "für immer"; },
    "in-1-Month": function(d) { return "in 1 Monat"; },
    "in-1-Year": function(d) { return "in 1 Jahr"; },
    "in-7-Days": function(d) { return "in 7 Tagen"; },
    "loading---": function(d) { return "wird geladen …"; },
    "one-time": function(d) { return "einmalig"; }
  },
  en: {
    "1Password-is-locked": function(d) { return "1Password is locked"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password is locked. Please try again after unlocking."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "A 1Password membership is required to integrate with Privacy."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "A duration for the spending limit is required"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "A problem occurred when communicating with Brex.com. Please try again"; },
    "A-title-is-required": function(d) { return "A title is required"; },
    "Add-to-1Password": function(d) { return "Add to 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "All vaults are disabled."; },
    "An-error-has-occurred": function(d) { return "An error has occurred"; },
    "An-unexpected-error-occurred-": function(d) { return "An unexpected error occurred."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "An unexpected error occurred. <NAME_EMAIL>"; },
    "Assign-to-Account": function(d) { return "Assign to Account"; },
    Back: function(d) { return "Back"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Brex cards are stored and sync from Brex.com."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex will auto-fill transaction memos on this card."; },
    Cancel: function(d) { return "Cancel"; },
    "Choose-an-account": function(d) { return "Choose an account"; },
    Close: function(d) { return "Close"; },
    "Conceal-previous-value": function(d) { return "Conceal previous value"; },
    Confirm: function(d) { return "Confirm"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Connecting to Privacy Card…"; },
    "Create---Fill": function(d) { return "Create & Fill"; },
    "Create---Fill-SSH-Key": function(d) { return "Create & Fill SSH Key"; },
    "Create-Brex-vendor-card-": function(d) { return "Create Brex vendor card…"; },
    "Create-Masked-Email": function(d) { return "Create Masked Email"; },
    "Create-Privacy-Card": function(d) { return "Create Privacy Card"; },
    Custom: function(d) { return "Custom"; },
    "Enter-the-amount-in-dollars": function(d) { return "Enter the amount in dollars"; },
    Error: function(d) { return "Error"; },
    "Error-creating-card-": function(d) { return "Error creating card."; },
    "Error-creating-card---message-": function(d) { return "Error creating card: " + d.message; },
    "Existing-items": function(d) { return "Existing items"; },
    "Fill-Email": function(d) { return "Fill Email"; },
    "Funding-Source": function(d) { return "Funding Source"; },
    "Get-Help-": function(d) { return "Get Help…"; },
    "Input-field-for-the-item-s-title": function(d) { return "Input field for the item's title"; },
    "Loading-": function(d) { return "Loading…"; },
    "Lock-Card": function(d) { return "Lock Card"; },
    "Lock-card-is-required": function(d) { return "Lock card is required"; },
    "New-Item": function(d) { return "New Item"; },
    None: function(d) { return "None"; },
    OK: function(d) { return "OK"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Once saved, we'll remember it for you."; },
    "Open-1Password": function(d) { return "Open 1Password"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "Please enable the Privacy integration from the Developer Tools context menu."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Please ensure there is at least one Funding Source associated with your Privacy.com account, then try again."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "Please enter a name for the card."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "Please enter a smaller name for the card."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "Please enter a smaller spending limit for the card."; },
    "Please-select-a-vault-": function(d) { return "Please select a vault."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "Please unlock 1Password to enable the integration."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "Please unlock the selected account to enable the integration."; },
    "Press--shortcut--to-open-1Password-": function(d) { return "Press " + d.shortcut + " to open 1Password."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "Press " + d.shortcut + " to unlock 1Password"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "Press the 1Password icon in your browser’s toolbar to open 1Password."; },
    Previous: function(d) { return "Previous"; },
    "Privacy-Card": function(d) { return "Privacy Card"; },
    "Reason-for-card": function(d) { return "Reason for card"; },
    Regenerate: function(d) { return "Regenerate"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return "Regenerate again in " + d.secondsLeft + "s"; },
    "Reveal-previous-value": function(d) { return "Reveal previous value"; },
    "Reveal-to-see-previous-value": function(d) { return "Reveal to see previous value"; },
    Save: function(d) { return "Save"; },
    "Save-Item": function(d) { return "Save Item"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "Save or update information dialog in 1Password"; },
    "Select-a-vault": function(d) { return "Select a vault"; },
    "Select-account": function(d) { return "Select account"; },
    "Set-Spending-Limit": function(d) { return "Set Spending Limit"; },
    "Single-Use": function(d) { return "Single Use"; },
    "Spending-Limit": function(d) { return "Spending Limit"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Step 3: Review or edit your new login item, then save it."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "The integration with Brex has been disabled, please reconnect it on 1Password.com"; },
    "The-reason-for-a-card-is-required": function(d) { return "The reason for a card is required"; },
    "The-spending-limit-is-required": function(d) { return "The spending limit is required"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "Then tap the offline indicator ( <offlineIndicator /> ) and finish signing in to save your changes."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "This vault is locked. Please try again after unlocking."; },
    Title: function(d) { return "Title"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "To continue saving with 1Password, unlock an account."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Unable to authenticate with Privacy. Please check your API key and try again."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "Unable to create card, please try again."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Unable to create credit card item from Privacy Card."; },
    "Unable-to-enable-integration": function(d) { return "Unable to enable integration"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Unable to enable the Privacy integration. Please try again later."; },
    "Unable-to-parse-URL": function(d) { return "Unable to parse URL"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "Unable to save item. Check that the vault is unlocked and try again."; },
    Update: function(d) { return "Update"; },
    "Update-Existing": function(d) { return "Update Existing"; },
    "Update-Item": function(d) { return "Update Item"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "Use 1Password to create and fill Privacy Cards everywhere you pay online, and save merchant cards for future use."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "We were unable to reach Privacy. Please check your internet connection and try again."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "You must provide a spending limit for the card."; },
    "Your-account-is-offline-": function(d) { return "Your account is offline."; },
    email: function(d) { return "email"; },
    "every-month": function(d) { return "every month"; },
    "every-quarter": function(d) { return "every quarter"; },
    "every-transaction": function(d) { return "every transaction"; },
    "every-year": function(d) { return "every year"; },
    forever: function(d) { return "forever"; },
    "in-1-Month": function(d) { return "in 1 Month"; },
    "in-1-Year": function(d) { return "in 1 Year"; },
    "in-7-Days": function(d) { return "in 7 Days"; },
    "loading---": function(d) { return "loading..."; },
    "one-time": function(d) { return "one time"; }
  },
  es: {
    "1Password-is-locked": function(d) { return "1Password está bloqueado"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password está bloqueado. Inténtalo de nuevo después de desbloquearlo."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "Se requiere una suscripción de 1Password para integrarlo con Privacy."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "Se requiere una duración de límite de gasto"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Ha ocurrido un problema al comunicarnos con Brex.com. Inténtalo de nuevo"; },
    "A-title-is-required": function(d) { return "Se requiere un título"; },
    "Add-to-1Password": function(d) { return "Añadir a 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "Todas las bóvedas están desactivadas."; },
    "An-error-has-occurred": function(d) { return "Ha ocurrido un error"; },
    "An-unexpected-error-occurred-": function(d) { return "Se ha producido un error inesperado."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Se ha producido un error inesperado. Ponte en <NAME_EMAIL>"; },
    "Assign-to-Account": function(d) { return "Asignar a una cuenta"; },
    Back: function(d) { return "Atrás"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Las tarjetas Brex se almacenan y sincronizan desde Brex.com."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex cumplimentará automáticamente registros de transacciones en esta tarjeta."; },
    Cancel: function(d) { return "Cancelar"; },
    "Choose-an-account": function(d) { return "Elige una cuenta"; },
    Close: function(d) { return "Cerrar"; },
    "Conceal-previous-value": function(d) { return "Ocultar el valor anterior"; },
    Confirm: function(d) { return "Confirmar"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Conectando con la tarjeta de Privacy…"; },
    "Create---Fill": function(d) { return "Crear y cumplimentar"; },
    "Create---Fill-SSH-Key": function(d) { return "Crear y cumplimentar clave SSH"; },
    "Create-Brex-vendor-card-": function(d) { return "Crear tarjeta de proveedor de Brex…"; },
    "Create-Masked-Email": function(d) { return "Crear correo electrónico enmascarado"; },
    "Create-Privacy-Card": function(d) { return "Crear tarjeta de Privacy"; },
    Custom: function(d) { return "Personalizado"; },
    "Enter-the-amount-in-dollars": function(d) { return "Introduce la cantidad en dólares"; },
    Error: function(d) { return "Error"; },
    "Error-creating-card-": function(d) { return "Error al crear la tarjeta."; },
    "Error-creating-card---message-": function(d) { return "Error al crear la tarjeta: " + d.message; },
    "Existing-items": function(d) { return "Elementos existentes"; },
    "Fill-Email": function(d) { return "Cumplimentar correo electrónico"; },
    "Funding-Source": function(d) { return "Fuente de financiación"; },
    "Get-Help-": function(d) { return "Conseguir ayuda…"; },
    "Input-field-for-the-item-s-title": function(d) { return "Campo de entrada para el título del elemento"; },
    "Loading-": function(d) { return "Cargando…"; },
    "Lock-Card": function(d) { return "Bloquear tarjeta"; },
    "Lock-card-is-required": function(d) { return "Se requiere bloquear la tarjeta"; },
    "New-Item": function(d) { return "Nuevo elemento"; },
    None: function(d) { return "Ninguno"; },
    OK: function(d) { return "Aceptar"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Una vez guardado, lo recordaremos por ti."; },
    "Open-1Password": function(d) { return "Abrir 1Password"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "Habilita la integración de Privacy desde el menú contextual de las herramientas del desarrollador."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Asegúrate de que hay al menos una fuente de financiación vinculada a tu cuenta de Privacy.com e inténtalo de nuevo."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "Introduce un nombre para la tarjeta."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "Introduce un nombre más corto para la tarjeta."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "Introduce un límite de gastos menor para la tarjeta."; },
    "Please-select-a-vault-": function(d) { return "Selecciona una bóveda."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "Desbloquea 1Password para habilitar la integración."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "Desbloquea la cuenta seleccionada para habilitar la integración."; },
    "Press--shortcut--to-open-1Password-": function(d) { return "Pulsa " + d.shortcut + " para abrir 1Password."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "Pulsa " + d.shortcut + " para desbloquear 1Password"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "Pulsa el icono de 1Password en la barra de herramientas de tu navegador para abrir 1Password."; },
    Previous: function(d) { return "Anterior"; },
    "Privacy-Card": function(d) { return "Tarjeta de Privacy"; },
    "Reason-for-card": function(d) { return "Motivo de la tarjeta"; },
    Regenerate: function(d) { return "Generar de nuevo"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return "Generar de nuevo en " + d.secondsLeft + " s"; },
    "Reveal-previous-value": function(d) { return "Mostrar el valor anterior"; },
    "Reveal-to-see-previous-value": function(d) { return "Mostrar para ver el valor anterior"; },
    Save: function(d) { return "Guardar"; },
    "Save-Item": function(d) { return "Guardar elemento"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "Guardar o actualizar el diálogo de información en 1Password"; },
    "Select-a-vault": function(d) { return "Seleccionar una bóveda"; },
    "Select-account": function(d) { return "Seleccionar cuenta"; },
    "Set-Spending-Limit": function(d) { return "Configurar límite de gasto"; },
    "Single-Use": function(d) { return "Uso único"; },
    "Spending-Limit": function(d) { return "Límite de gastos"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Paso 3: revisa o edita el nuevo elemento de inicio de sesión y guárdalo."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "La integración con Brex se ha desactivado, vuelve a conectarlo en 1Password.com"; },
    "The-reason-for-a-card-is-required": function(d) { return "Se requiere el motivo de la tarjeta"; },
    "The-spending-limit-is-required": function(d) { return "Se requiere el límite de gasto"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "Después, pulsa el indicador de fuera de línea ( <offlineIndicator /> ) y termina de iniciar sesión para guardar tus cambios."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "Esta bóveda está bloqueada. Inténtalo de nuevo después de desbloquearla."; },
    Title: function(d) { return "Título"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "Para continuar guardando con 1Password, desbloquea una cuenta."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "No se ha podido autenticar con Privacy. Comprueba tu clave API y vuelve a intentarlo."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "No se ha podido crear la tarjeta, inténtalo de nuevo."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "No se ha podido crear el elemento de la tarjeta de crédito desde la tarjeta de Privacy."; },
    "Unable-to-enable-integration": function(d) { return "No se puede habilitar la integración"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "No se ha podido habilitar la integración de Privacy. Inténtalo de nuevo más tarde."; },
    "Unable-to-parse-URL": function(d) { return "No se ha podido analizar la URL"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "No se ha podido guardar el elemento. Comprueba que la bóveda está desbloqueada y vuelve a intentarlo."; },
    Update: function(d) { return "Actualizar"; },
    "Update-Existing": function(d) { return "Actualizar existente"; },
    "Update-Item": function(d) { return "Actualizar elemento"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "Usa 1Password para crear y cumplimentar tarjetas de Privacy siempre que pagues en línea, y guarda tarjetas comerciales para usarlas en el futuro."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "No nos hemos podido conectar con Privacy. Revisa tu conexión a internet e inténtalo de nuevo."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "Debes facilitar un límite de gastos para la tarjeta."; },
    "Your-account-is-offline-": function(d) { return "Tu cuenta está fuera de línea."; },
    email: function(d) { return "correo electrónico"; },
    "every-month": function(d) { return "cada mes"; },
    "every-quarter": function(d) { return "cada trimestre"; },
    "every-transaction": function(d) { return "cada transacción"; },
    "every-year": function(d) { return "cada año"; },
    forever: function(d) { return "para siempre"; },
    "in-1-Month": function(d) { return "en 1 mes"; },
    "in-1-Year": function(d) { return "en 1 año"; },
    "in-7-Days": function(d) { return "en 7 días"; },
    "loading---": function(d) { return "cargando..."; },
    "one-time": function(d) { return "una vez"; }
  },
  fr: {
    "1Password-is-locked": function(d) { return "1Password est verrouillé"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password est verrouillé. Veuillez réessayer après l'avoir débloqué."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "Un abonnement 1Password est nécessaire pour intégrer avec Privacy."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "Une durée pour le plafond de dépenses est nécessaire"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Un problème est survenur lors des communications avec Bex.com. Veuillez réessayer"; },
    "A-title-is-required": function(d) { return "Un titre est nécessaire"; },
    "Add-to-1Password": function(d) { return "Ajouter à 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "Tous les coffres sont désactivés."; },
    "An-error-has-occurred": function(d) { return "Une erreur s'est produite"; },
    "An-unexpected-error-occurred-": function(d) { return "Une erreur inattendue s'est produite."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Une erreur inattendue s'est produite. <NAME_EMAIL>"; },
    "Assign-to-Account": function(d) { return "Assigner à un compte"; },
    Back: function(d) { return "Retour"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Les cartes Brex sont enregistrée et synchronisée sur Brex.com."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex va remplir automatiquement les mémos de transaction de cette carte."; },
    Cancel: function(d) { return "Annuler"; },
    "Choose-an-account": function(d) { return "Choisir un compte"; },
    Close: function(d) { return "Fermer"; },
    "Conceal-previous-value": function(d) { return "Masquer la valeur précédente"; },
    Confirm: function(d) { return "Confirmer"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Connexion à la carte Privacy…"; },
    "Create---Fill": function(d) { return "Créer et remplir"; },
    "Create---Fill-SSH-Key": function(d) { return "Créer et remplir la clé SSH"; },
    "Create-Brex-vendor-card-": function(d) { return "Créer une carte de vendeur Brex…"; },
    "Create-Masked-Email": function(d) { return "Créer un e-mail masqué"; },
    "Create-Privacy-Card": function(d) { return "Créer une carte Privacy"; },
    Custom: function(d) { return "Personnalisé"; },
    "Enter-the-amount-in-dollars": function(d) { return "Saisissez le montant en dollars"; },
    Error: function(d) { return "Erreur"; },
    "Error-creating-card-": function(d) { return "Erreur lors de la création de la carte."; },
    "Error-creating-card---message-": function(d) { return "Erreur lors de la création de la carte : " + d.message; },
    "Existing-items": function(d) { return "Éléments existants"; },
    "Fill-Email": function(d) { return "Remplir l'e-mail"; },
    "Funding-Source": function(d) { return "Source de financement"; },
    "Get-Help-": function(d) { return "Obtenir de l'aide…"; },
    "Input-field-for-the-item-s-title": function(d) { return "Champ de saisie pour le titre de l'élément"; },
    "Loading-": function(d) { return "Chargement…"; },
    "Lock-Card": function(d) { return "Verrouiller la carte"; },
    "Lock-card-is-required": function(d) { return "Il est nécessaire de verrouiller la carte"; },
    "New-Item": function(d) { return "Nouvel élément"; },
    None: function(d) { return "Aucun"; },
    OK: function(d) { return "OK"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Une fois enregistré, nous le garderons en mémoire pour vous."; },
    "Open-1Password": function(d) { return "Ouvrir 1Password"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "Veuillez activer l'intégration Privacy dans le menu contextuel des outils pour les développeurs."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Veuillez vous assurer de disposer au moins d'une source de financement associée à votre compte Privacy.com, puis réessayez."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "Veuillez saisir un nom pour la carte."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "Veuillez indiquer un nom plus court pour la carte."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "Veuillez indiquer un plafond de dépenses inférieur pour la carte."; },
    "Please-select-a-vault-": function(d) { return "Veuillez sélectionner un coffre."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "Veuillez déverrouiller 1Password pour activer l'intégration."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "Veuillez déverrouiller le compte sélectionné pour activer l'intégration."; },
    "Press--shortcut--to-open-1Password-": function(d) { return "Appuyez sur " + d.shortcut + " pour ouvrir 1Password."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "Appuyez sur " + d.shortcut + " pour déverrouiller 1Password"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "Appuyez sur l'icône 1Password dans la barre d'outils de votre navigateur pour ouvrir 1Password."; },
    Previous: function(d) { return "Précédent"; },
    "Privacy-Card": function(d) { return "Carte Privacy"; },
    "Reason-for-card": function(d) { return "Motif pour la carte"; },
    Regenerate: function(d) { return "Générer à nouveau"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return "Générer à nouveau dans " + d.secondsLeft + " s"; },
    "Reveal-previous-value": function(d) { return "Révéler la valeur précédente"; },
    "Reveal-to-see-previous-value": function(d) { return "Révéler pour afficher la valeur précédente"; },
    Save: function(d) { return "Enregistrer"; },
    "Save-Item": function(d) { return "Enregistrer l'élément"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "Enregistrez ou mettez à jour les informations dans la boîte de dialogue de 1Password"; },
    "Select-a-vault": function(d) { return "Sélectionnez un coffre"; },
    "Select-account": function(d) { return "Sélectionnez le compte"; },
    "Set-Spending-Limit": function(d) { return "Définir un plafond de dépenses"; },
    "Single-Use": function(d) { return "Usage unique"; },
    "Spending-Limit": function(d) { return "Plafond de dépenses"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Étape 3 : Consultez ou modifiez votre nouvel identifiant de connexion, puis enregistrez-le."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "L'intégration das Brex a été désactivée, veuillez la reconnecter depuis 1Password.com"; },
    "The-reason-for-a-card-is-required": function(d) { return "Le motif pour cette carte est nécessaire"; },
    "The-spending-limit-is-required": function(d) { return "Le plafond de dépenses est nécessaire"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "Appuyez ensuite sur l'indicateur hors ligne ( <offlineIndicator /> ) et finissez de vous connecter pour enregistrer vos modifications."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "Ce coffre est verrouillé. Veuillez réessayer après l'avoir débloqué."; },
    Title: function(d) { return "Titre"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "Pour continuer à enregistrer vos éléments avec 1Password, déverrouillez un compte."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Impossible d'authentifier avec Privacy. Veuillez vérifier votre clé API, puis réessayez."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "Impossible de créer la carte, veuillez réessayer."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Impossible de créer un élément de carte de crédit à partir de la carte Privacy."; },
    "Unable-to-enable-integration": function(d) { return "Impossible d'activer l'intégration"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Impossible d'activer l'intégration Privacy. Veuillez réessayer plus tard."; },
    "Unable-to-parse-URL": function(d) { return "Impossible d'analyser l'URL"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "Impossible d'enregistrer cet élément. Vérifiez que le coffre est déverrouillé, puis réessayez."; },
    Update: function(d) { return "Mettre à jour"; },
    "Update-Existing": function(d) { return "Mettre à jour l'élément existant"; },
    "Update-Item": function(d) { return "Mettre à jour l'élément"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "Utilisez 1Password pour créer et remplir des cartes Privacy partout où vous payez en ligne, et enregistrez les cartes des commerçants pour une utilisation ultérieure."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "Nous n'avons pas pu atteindre Privacy. Veuillez vérifier votre connexion Internet, puis réessayez."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "Vous devez indiquer un plafond de dépenses pour la carte."; },
    "Your-account-is-offline-": function(d) { return "Votre compte est hors ligne."; },
    email: function(d) { return "e-mail"; },
    "every-month": function(d) { return "tous les mois"; },
    "every-quarter": function(d) { return "tous les trimestres"; },
    "every-transaction": function(d) { return "toutes les transactions"; },
    "every-year": function(d) { return "tous les ans"; },
    forever: function(d) { return "pour toujours"; },
    "in-1-Month": function(d) { return "dans 1 mois"; },
    "in-1-Year": function(d) { return "dans 1 an"; },
    "in-7-Days": function(d) { return "dans 7 jours"; },
    "loading---": function(d) { return "chargement..."; },
    "one-time": function(d) { return "une fois"; }
  },
  it: {
    "1Password-is-locked": function(d) { return "1Password è bloccato"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password è bloccato. Prova di nuovo dopo lo sblocco."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "È necessario l’abbonamento a 1Password per l’integrazione con Privacy."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "È obbligatoria una durata per il limite di spesa"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Si è verificato un problema di comunicazione con Brex.com. Riprova"; },
    "A-title-is-required": function(d) { return "È richiesto un titolo"; },
    "Add-to-1Password": function(d) { return "Aggiungi a 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "Tutte le casseforti sono disabilitate."; },
    "An-error-has-occurred": function(d) { return "Si è verificato un errore"; },
    "An-unexpected-error-occurred-": function(d) { return "Si è verificato un errore imprevisto."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Si è verificato un errore imprevisto. Contatta <EMAIL>"; },
    "Assign-to-Account": function(d) { return "Assegna all’account"; },
    Back: function(d) { return "Indietro"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Le carte Brex vengono archiviate e sincronizzate da Brex.com."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex compilerà automaticamente i promemoria delle transazioni su questa carta."; },
    Cancel: function(d) { return "Annulla"; },
    "Choose-an-account": function(d) { return "Scegli un account"; },
    Close: function(d) { return "Chiudi"; },
    "Conceal-previous-value": function(d) { return "Nascondi il valore precedente"; },
    Confirm: function(d) { return "Conferma"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Connessione alla carta Privacy…"; },
    "Create---Fill": function(d) { return "Crea e compila"; },
    "Create---Fill-SSH-Key": function(d) { return "Crea e compila la chiave SSH"; },
    "Create-Brex-vendor-card-": function(d) { return "Crea carta venditore Brex…"; },
    "Create-Masked-Email": function(d) { return "Crea email mascherata"; },
    "Create-Privacy-Card": function(d) { return "Crea carta Privacy"; },
    Custom: function(d) { return "Personalizzato"; },
    "Enter-the-amount-in-dollars": function(d) { return "Inserisci l’importo in dollari"; },
    Error: function(d) { return "Errore"; },
    "Error-creating-card-": function(d) { return "Errore durante la creazione della carta."; },
    "Error-creating-card---message-": function(d) { return "Errore durante la creazione della carta: " + d.message; },
    "Existing-items": function(d) { return "Elementi esistenti"; },
    "Fill-Email": function(d) { return "Compila email"; },
    "Funding-Source": function(d) { return "Fonte di finanziamento"; },
    "Get-Help-": function(d) { return "Ricevi aiuto…"; },
    "Input-field-for-the-item-s-title": function(d) { return "Campo di immissione per il titolo dell’elemento"; },
    "Loading-": function(d) { return "Caricamento in corso…"; },
    "Lock-Card": function(d) { return "Blocca carta"; },
    "Lock-card-is-required": function(d) { return "È necessario il blocco della carta"; },
    "New-Item": function(d) { return "Nuovo elemento"; },
    None: function(d) { return "Nessuno"; },
    OK: function(d) { return "OK"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Una volta salvato, lo ricorderemo per te."; },
    "Open-1Password": function(d) { return "Apri 1Password"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "Abilita l’integrazione di Privacy dal menu contestuale Strumenti per sviluppatori."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Assicurati che ci sia almeno una fonte di finanziamento associata al tuo account su Privacy.com, quindi riprova."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "Inserisci un nome per la carta."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "Inserisci un nome più breve per la carta."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "Inserisci un limite di spesa inferiore per la carta."; },
    "Please-select-a-vault-": function(d) { return "Seleziona una cassaforte."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "Sblocca 1Password per abilitare l’integrazione."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "Sblocca l’account selezionato per abilitare l’integrazione."; },
    "Press--shortcut--to-open-1Password-": function(d) { return "Premi " + d.shortcut + " per aprire 1Password."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "Premi " + d.shortcut + " per sbloccare 1Password"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "Premi l’icona 1Password nella barra degli strumenti del browser per aprire 1Password."; },
    Previous: function(d) { return "Precedente"; },
    "Privacy-Card": function(d) { return "Carta Privacy"; },
    "Reason-for-card": function(d) { return "Motivo della carta"; },
    Regenerate: function(d) { return "Rigenera"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return "Rigenera di nuovo tra " + d.secondsLeft + "s"; },
    "Reveal-previous-value": function(d) { return "Mostra il valore precedente"; },
    "Reveal-to-see-previous-value": function(d) { return "Mostra per vedere il valore precedente"; },
    Save: function(d) { return "Salva"; },
    "Save-Item": function(d) { return "Salva l’elemento"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "Finestra di dialogo per il salvataggio o l’aggiornamento delle informazioni in 1Password"; },
    "Select-a-vault": function(d) { return "Seleziona una cassaforte"; },
    "Select-account": function(d) { return "Seleziona l’account"; },
    "Set-Spending-Limit": function(d) { return "Imposta il limite di spesa"; },
    "Single-Use": function(d) { return "Uso singolo"; },
    "Spending-Limit": function(d) { return "Limite di spesa"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Passaggio 3: rivedi o modifica il tuo nuovo elemento di accesso, quindi salvalo."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "L’integrazione con Brex è stata disabilitata. Riconnettiti con 1Password.com"; },
    "The-reason-for-a-card-is-required": function(d) { return "È richiesto il motivo della carta"; },
    "The-spending-limit-is-required": function(d) { return "È richiesto il limite di spesa"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "Quindi tocca l’indicatore di offline ( <offlineIndicator /> ) e completa l’accesso per salvare le modifiche."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "Questa cassaforte è bloccata. Riprova dopo lo sblocco."; },
    Title: function(d) { return "Titolo"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "Per continuare a salvare con 1Password, sblocca un account."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Impossibile eseguire l’autenticazione con Privacy. Controlla la tua chiave API e riprova."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "Impossibile creare la carta, riprova."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Impossibile creare l’elemento della carta di credito dalla carta Privacy."; },
    "Unable-to-enable-integration": function(d) { return "Impossibile abilitare l’integrazione"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Impossibile abilitare l’integrazione di Privacy. Riprova più tardi."; },
    "Unable-to-parse-URL": function(d) { return "Impossibile analizzare l’URL"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "Impossibile salvare l’elemento. Verifica che la cassaforte sia sbloccata e riprova."; },
    Update: function(d) { return "Aggiorna"; },
    "Update-Existing": function(d) { return "Aggiorna esistente"; },
    "Update-Item": function(d) { return "Aggiorna l’elemento"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "Usa 1Password per creare e compilare le carte Privacy ovunque paghi online, e salva le carte del commerciante per gli utilizzi futuri."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "Impossibile raggiungere Privacy. Controlla la connessione a internet e riprova."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "È necessario fornire un limite di spesa per la carta."; },
    "Your-account-is-offline-": function(d) { return "Il tuo account è offline."; },
    email: function(d) { return "email"; },
    "every-month": function(d) { return "ogni mese"; },
    "every-quarter": function(d) { return "ogni trimestre"; },
    "every-transaction": function(d) { return "ogni transazione"; },
    "every-year": function(d) { return "ogni anno"; },
    forever: function(d) { return "per sempre"; },
    "in-1-Month": function(d) { return "tra 1 mese"; },
    "in-1-Year": function(d) { return "tra 1 anno"; },
    "in-7-Days": function(d) { return "tra 7 giorni"; },
    "loading---": function(d) { return "caricamento in corso..."; },
    "one-time": function(d) { return "una volta"; }
  },
  ja: {
    "1Password-is-locked": function(d) { return "1Passwordはロックされています"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Passwordがロックされています。ロックを解除してからもう一度お試しください。"; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "Privacyと連携するには、1Passwordメンバーシップが必要です。"; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "利用限度額の設定期間が必要です"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Brex.comと通信する際に問題が発生しました。もう一度お試しください。"; },
    "A-title-is-required": function(d) { return "タイトルが必要です"; },
    "Add-to-1Password": function(d) { return "1Passwordに追加"; },
    "All-vaults-are-disabled-": function(d) { return "すべての保管庫は無効になっています。"; },
    "An-error-has-occurred": function(d) { return "エラーが発生しました"; },
    "An-unexpected-error-occurred-": function(d) { return "予期せぬエラーが発生しました。"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "予期せぬエラーが発生しました。*********************までお問い合わせください"; },
    "Assign-to-Account": function(d) { return "アカウントに割り当てる"; },
    Back: function(d) { return "戻る"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "BrexカードはBrex.comから保存・同期されます。"; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brexはこのカードに取引メモを自動入力します。"; },
    Cancel: function(d) { return "キャンセル"; },
    "Choose-an-account": function(d) { return "アカウントを選択"; },
    Close: function(d) { return "閉じる"; },
    "Conceal-previous-value": function(d) { return "以前の値を非表示にする"; },
    Confirm: function(d) { return "確認"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Privacyカードに接続しています……"; },
    "Create---Fill": function(d) { return "作成と入力"; },
    "Create---Fill-SSH-Key": function(d) { return "SSHキーの作成と入力"; },
    "Create-Brex-vendor-card-": function(d) { return "Brexベンダーカードを作成……"; },
    "Create-Masked-Email": function(d) { return "マスクアドレスの作成"; },
    "Create-Privacy-Card": function(d) { return "Privacyカードを作成"; },
    Custom: function(d) { return "カスタム"; },
    "Enter-the-amount-in-dollars": function(d) { return "金額をドルで入力してください"; },
    Error: function(d) { return "エラー"; },
    "Error-creating-card-": function(d) { return "カード作成エラー。"; },
    "Error-creating-card---message-": function(d) { return "カード作成エラー：" + d.message; },
    "Existing-items": function(d) { return "既存のアイテム"; },
    "Fill-Email": function(d) { return "メールアドレスの入力"; },
    "Funding-Source": function(d) { return "資金調達先"; },
    "Get-Help-": function(d) { return "ヘルプを参照……"; },
    "Input-field-for-the-item-s-title": function(d) { return "アイテムのタイトルの入力フィールド"; },
    "Loading-": function(d) { return "読み込んでいます……"; },
    "Lock-Card": function(d) { return "ロックカード"; },
    "Lock-card-is-required": function(d) { return "ロックカードが必要です"; },
    "New-Item": function(d) { return "新規アイテム"; },
    None: function(d) { return "なし"; },
    OK: function(d) { return "OK"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "一度保存すれば、代わりに記憶します。"; },
    "Open-1Password": function(d) { return "1Passwordを開く"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "開発者ツールのコンテキストメニューからPrivacyの統合を有効にしてください。"; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Privacy.comアカウントに資金調達先が1つ以上紐付けられていることを確認してから、再度お試しください。"; },
    "Please-enter-a-name-for-the-card-": function(d) { return "カードの名前を入力してください。"; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "カードの名前を短く入力してください。"; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "カードの利用限度額を少なく入力してください。"; },
    "Please-select-a-vault-": function(d) { return "保管庫を選択してください。"; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "統合を有効にするため、1Passwordをロック解除してください。"; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "統合を有効にするため、選択したアカウントをロック解除してください。"; },
    "Press--shortcut--to-open-1Password-": function(d) { return d.shortcut + "を押して、1Passwordを開く。"; },
    "Press--shortcut--to-unlock-1Password": function(d) { return d.shortcut + "を押して、1Passwordをロック解除する"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "ブラウザのツールバーの1Passwordアイコンを押して、1Passwordを開きます。"; },
    Previous: function(d) { return "前"; },
    "Privacy-Card": function(d) { return "プライバシーカード"; },
    "Reason-for-card": function(d) { return "カードの理由"; },
    Regenerate: function(d) { return "再生成"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return d.secondsLeft + "秒後に再生成する"; },
    "Reveal-previous-value": function(d) { return "以前の値を表示する"; },
    "Reveal-to-see-previous-value": function(d) { return "以前の値を確認するために表示する"; },
    Save: function(d) { return "保存"; },
    "Save-Item": function(d) { return "アイテムを保存"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "1Passwordの情報保存・更新ダイアログ"; },
    "Select-a-vault": function(d) { return "保管庫を選択"; },
    "Select-account": function(d) { return "アカウントを選択"; },
    "Set-Spending-Limit": function(d) { return "利用限度額の設定"; },
    "Single-Use": function(d) { return "シングルユース"; },
    "Spending-Limit": function(d) { return "利用限度額"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "ステップ3：新しいログインアイテムを確認または編集してから、保存します。"; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "Brexとの統合は無効になっています。1Password.comで再接続してください。"; },
    "The-reason-for-a-card-is-required": function(d) { return "カードの理由が必要です"; },
    "The-spending-limit-is-required": function(d) { return "利用限度額が必要です"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "その後、オフラインインジケーター（<offlineIndicator />）をタップし、サインインを完了すると、変更が保存されます。"; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "保管庫がロックされています。ロックを解除してからもう一度お試しください。"; },
    Title: function(d) { return "タイトル"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "引き続き1Passwordで保存するには、アカウントのロックを解除してください。"; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Privacyの認証ができません。APIキーをご確認の上、再度お試しください。"; },
    "Unable-to-create-card--please-try-again-": function(d) { return "カードを作成できません。再度お試しください."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Privacyカードからクレジットカードのアイテムを作成できません。"; },
    "Unable-to-enable-integration": function(d) { return "統合を有効にできません"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Privacyの統合を有効にすることができません。後でもう一度お試しください。"; },
    "Unable-to-parse-URL": function(d) { return "URLを解析できません。"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "アイテムを保存できません。保管庫のロックが解除されていることを確認してから、もう一度試してみてください。"; },
    Update: function(d) { return "アップデート"; },
    "Update-Existing": function(d) { return "既存のものをアップデート"; },
    "Update-Item": function(d) { return "アイテムをアップデート"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "1Passwordを使用して、オンラインで支払う場所のどこにでもPrivacyカードを作成して入力し、将来使用するために加盟店カードを保存します。"; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "Privacyにアクセスできませんでした。インターネット接続を確認して再実行してください。"; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "カードの利用限度額を入力してください。"; },
    "Your-account-is-offline-": function(d) { return "あなたのアカウントはオフラインです。"; },
    email: function(d) { return "メール"; },
    "every-month": function(d) { return "毎月"; },
    "every-quarter": function(d) { return "3ヶ月ごと"; },
    "every-transaction": function(d) { return "取引ごと"; },
    "every-year": function(d) { return "毎年"; },
    forever: function(d) { return "永久"; },
    "in-1-Month": function(d) { return "1ヶ月後"; },
    "in-1-Year": function(d) { return "1年後"; },
    "in-7-Days": function(d) { return "7日後"; },
    "loading---": function(d) { return "読み込んでいます……"; },
    "one-time": function(d) { return "1回"; }
  },
  ko: {
    "1Password-is-locked": function(d) { return "1Password가 잠겨 있습니다"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password가 잠겨 있습니다. 잠금을 해제한 후 다시 시도하세요."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "Privacy와 통합하려면 1Password 멤버십이 필요합니다."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "지출 한도의 기간이 필요합니다"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Brex.com과 통신하는 동안 문제가 발생했습니다. 다시 시도하세요"; },
    "A-title-is-required": function(d) { return "제목은 필수 항목입니다"; },
    "Add-to-1Password": function(d) { return "1Password에 추가"; },
    "All-vaults-are-disabled-": function(d) { return "모든 금고가 비활성화되었습니다."; },
    "An-error-has-occurred": function(d) { return "오류가 발생했습니다"; },
    "An-unexpected-error-occurred-": function(d) { return "예기치 않은 오류가 발생했습니다."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "예기치 않은 오류가 발생했습니다. *********************으로 문의해 주세요"; },
    "Assign-to-Account": function(d) { return "계정에 할당"; },
    Back: function(d) { return "뒤로"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Brex 카드는 Brex.com에서 보관 및 동기화됩니다."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex가 이 카드에 거래 메모를 자동으로 입력합니다."; },
    Cancel: function(d) { return "취소"; },
    "Choose-an-account": function(d) { return "계정 선택"; },
    Close: function(d) { return "닫기"; },
    "Conceal-previous-value": function(d) { return "이전 값 숨기기"; },
    Confirm: function(d) { return "확인"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Privacy 카드에 연결하는 중…"; },
    "Create---Fill": function(d) { return "생성 및 채우기"; },
    "Create---Fill-SSH-Key": function(d) { return "SSH 키 생성 및 채우기"; },
    "Create-Brex-vendor-card-": function(d) { return "Brex 벤더 카드 만들기…"; },
    "Create-Masked-Email": function(d) { return "마스킹된 이메일 생성"; },
    "Create-Privacy-Card": function(d) { return "Privacy 카드 만들기"; },
    Custom: function(d) { return "사용자 지정"; },
    "Enter-the-amount-in-dollars": function(d) { return "달러로 금액을 입력하세요"; },
    Error: function(d) { return "오류"; },
    "Error-creating-card-": function(d) { return "카드 생성 중 오류 발생."; },
    "Error-creating-card---message-": function(d) { return "카드 생성 중 오류 발생: " + d.message; },
    "Existing-items": function(d) { return "기존 항목"; },
    "Fill-Email": function(d) { return "이메일 채우기"; },
    "Funding-Source": function(d) { return "자금 출처"; },
    "Get-Help-": function(d) { return "도움 받기…"; },
    "Input-field-for-the-item-s-title": function(d) { return "항목의 제목에 대한 입력란"; },
    "Loading-": function(d) { return "불러오는 중…"; },
    "Lock-Card": function(d) { return "카드 잠금"; },
    "Lock-card-is-required": function(d) { return "카드 잠금은 필수 항목입니다"; },
    "New-Item": function(d) { return "새 항목"; },
    None: function(d) { return "없음"; },
    OK: function(d) { return "확인"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "저장하면 이 정보가 기억됩니다."; },
    "Open-1Password": function(d) { return "1Password 열기"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "개발자 도구 컨텍스트 메뉴에서 Privacy 통합을 활성화하세요."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "회원님의 Privacy.com 계정에 하나 이상의 자금 출처가 연결되어 있는지 확인한 다음, 다시 시도하세요."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "카드의 이름을 입력하세요."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "이 카드에 대해 더 짧은 이름을 입력하세요."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "이 카드에 대해 더 적은 지출 한도를 입력하세요."; },
    "Please-select-a-vault-": function(d) { return "금고를 선택하세요."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "통합을 활성화하려면 1Password를 잠금 해제하세요."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "통합을 활성화하려면 선택한 계정을 잠금 해제하세요."; },
    "Press--shortcut--to-open-1Password-": function(d) { return d.shortcut + "을(를) 눌러서 1Password를 엽니다."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return d.shortcut + "을(를) 눌러서 1Password를 잠금 해제합니다"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "브라우저의 도구 모음에 있는 1Password 아이콘을 눌러서 1Password를 엽니다."; },
    Previous: function(d) { return "이전"; },
    "Privacy-Card": function(d) { return "Privacy 카드"; },
    "Reason-for-card": function(d) { return "카드 이유"; },
    Regenerate: function(d) { return "다시 생성"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return d.secondsLeft + "초 후 다시 생성"; },
    "Reveal-previous-value": function(d) { return "이전 값 표시"; },
    "Reveal-to-see-previous-value": function(d) { return "표시하여 이전 값 보기"; },
    Save: function(d) { return "저장"; },
    "Save-Item": function(d) { return "항목 저장"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "1Password의 정보 대화 상자를 저장하거나 업데이트합니다"; },
    "Select-a-vault": function(d) { return "금고 선택"; },
    "Select-account": function(d) { return "계정 선택"; },
    "Set-Spending-Limit": function(d) { return "지출 한도 설정"; },
    "Single-Use": function(d) { return "단일 사용"; },
    "Spending-Limit": function(d) { return "지출 한도"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "3단계: 새로운 로그인 항목을 검토 또는 편집한 다음, 저장합니다."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "Brex와의 통합이 비활성화되었습니다. 1Password.com에서 다시 연결하세요"; },
    "The-reason-for-a-card-is-required": function(d) { return "카드 이유는 필수 항목입니다"; },
    "The-spending-limit-is-required": function(d) { return "지출 한도는 필수 항목입니다"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "그런 다음, 오프라인 지표( <offlineIndicator /> )를 탭하고 로그인을 완료한 후 변경 사항을 저장합니다."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "이 금고는 잠겨 있습니다. 잠금을 해제한 후 다시 시도하세요."; },
    Title: function(d) { return "제목"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "1Password를 사용해 저장을 계속하려면 계정을 잠금 해제하세요."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Privacy 인증을 수행할 수 없습니다. API 키를 확인한 후 다시 시도하세요."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "카드를 생성할 수 없습니다. 다시 시도하세요."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Privacy 카드에서 신용 카드 항목을 생성할 수 없습니다."; },
    "Unable-to-enable-integration": function(d) { return "통합을 활성화할 수 없습니다"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Privacy 통합을 활성화할 수 없습니다. 나중에 다시 시도하세요."; },
    "Unable-to-parse-URL": function(d) { return "URL을 구문 분석할 수 없습니다"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "항목을 저장할 수 없습니다. 금고가 잠겨 있는지 확인한 후 다시 시도하세요."; },
    Update: function(d) { return "업데이트"; },
    "Update-Existing": function(d) { return "기존 정보 업데이트"; },
    "Update-Item": function(d) { return "항목 업데이트"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "온라인 결제가 가능한 어느 곳에서든 1Password를 사용하여 Privacy 카드를 생성 및 기입하고, 나중에 사용할 때를 대비하여 머천트 카드를 저장해 두세요."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "Privacy에 접속하지 못했습니다. 인터넷 연결 상태를 확인한 후 다시 시도하세요."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "카드의 지출 한도를 입력해야 합니다."; },
    "Your-account-is-offline-": function(d) { return "계정이 오프라인 상태입니다."; },
    email: function(d) { return "이메일"; },
    "every-month": function(d) { return "매월"; },
    "every-quarter": function(d) { return "매분기"; },
    "every-transaction": function(d) { return "매 거래마다"; },
    "every-year": function(d) { return "매년"; },
    forever: function(d) { return "영원히"; },
    "in-1-Month": function(d) { return "1개월 후"; },
    "in-1-Year": function(d) { return "1년 후"; },
    "in-7-Days": function(d) { return "7일 후"; },
    "loading---": function(d) { return "불러오는 중..."; },
    "one-time": function(d) { return "일회성"; }
  },
  nl: {
    "1Password-is-locked": function(d) { return "1Password is vergrendeld"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password is vergrendeld. Probeer het na het ontgrendelen opnieuw."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "Er is een 1Password-abonnement vereist om privékaarten te integreren."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "Een duur voor de uitgavelimiet is verplicht"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Er is een fout opgetreden bij het communiceren met Brex.com. Probeer het opnieuw"; },
    "A-title-is-required": function(d) { return "Een titel is verplicht"; },
    "Add-to-1Password": function(d) { return "Toevoegen aan 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "Alle kluizen zijn uitgeschakeld."; },
    "An-error-has-occurred": function(d) { return "Er is een fout opgetreden"; },
    "An-unexpected-error-occurred-": function(d) { return "Er is een onbekende fout opgetreden."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Er is een onverwachte fout opgetreden. Neem contact <NAME_EMAIL>"; },
    "Assign-to-Account": function(d) { return "Toewijzen aan account"; },
    Back: function(d) { return "Terug"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Brex-kaarten zijn opgeslagen en synchroniseren met Brex.com."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex vult trasnactiememo's op deze kaart automatisch aan."; },
    Cancel: function(d) { return "Annuleren"; },
    "Choose-an-account": function(d) { return "Kies een account"; },
    Close: function(d) { return "Sluiten"; },
    "Conceal-previous-value": function(d) { return "Vorige waarde verbergen"; },
    Confirm: function(d) { return "Bevestigen"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Verbinden met privékaart…"; },
    "Create---Fill": function(d) { return "Aanmaken en automatisch aanvullen"; },
    "Create---Fill-SSH-Key": function(d) { return "SSH-key aanmaken en aanvullen"; },
    "Create-Brex-vendor-card-": function(d) { return "Brex-leverancierskaart aanmaken…"; },
    "Create-Masked-Email": function(d) { return "Gemaskeerde e-mail aanmaken"; },
    "Create-Privacy-Card": function(d) { return "Privékaart aanmaken"; },
    Custom: function(d) { return "Aangepast"; },
    "Enter-the-amount-in-dollars": function(d) { return "Voer het bedrag in dollars in"; },
    Error: function(d) { return "Foutmelding"; },
    "Error-creating-card-": function(d) { return "Fout bij het aanmaken van kaart."; },
    "Error-creating-card---message-": function(d) { return "Fout bij het aanmaken van kaart: " + d.message; },
    "Existing-items": function(d) { return "Bestaande items"; },
    "Fill-Email": function(d) { return "E-mailadres invullen"; },
    "Funding-Source": function(d) { return "Financieringsbron"; },
    "Get-Help-": function(d) { return "Krijg ondersteuning…"; },
    "Input-field-for-the-item-s-title": function(d) { return "Invoerveld voor de titel van de items"; },
    "Loading-": function(d) { return "Laden…"; },
    "Lock-Card": function(d) { return "Kaart vergrendelen"; },
    "Lock-card-is-required": function(d) { return "Kaart vergrendelen is verplicht"; },
    "New-Item": function(d) { return "Nieuw item"; },
    None: function(d) { return "Geen"; },
    OK: function(d) { return "OK"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Sla het op en wij onthouden het voortaan voor je."; },
    "Open-1Password": function(d) { return "1Password openen"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "Schakel de privacy-integratie in via het ontwikkelaarstools-contextmenu."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Zorg ervoor dat er minstens één financieringsbron is gekoppeld aan je Privacy.com-account. Probeer het daarna opnieuw."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "Voer een naam in voor de kaart."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "Voer een kortere naam in voor de kaart."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "Voer een kleinere bestedingslimiet in voor de kaart."; },
    "Please-select-a-vault-": function(d) { return "Selecteer een kluis."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "Ontgrendel 1Password om de integratie in te schakelen."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "Ontgrendel het geselecteerde account om de integratie in te schakelen."; },
    "Press--shortcut--to-open-1Password-": function(d) { return "Druk op " + d.shortcut + " om 1Password te openen."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "Druk op " + d.shortcut + " om 1Password te ontgrendelen"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "Druk op het pictogram in de werkbalk van je browser om 1Password te openen."; },
    Previous: function(d) { return "Vorige"; },
    "Privacy-Card": function(d) { return "Privékaart"; },
    "Reason-for-card": function(d) { return "Reden voor kaart"; },
    Regenerate: function(d) { return "Opnieuw genereren"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return "Opnieuw genereren in " + d.secondsLeft + " seconden"; },
    "Reveal-previous-value": function(d) { return "Vorige waarde weergeven"; },
    "Reveal-to-see-previous-value": function(d) { return "Kies weergeven voor vorige waarde"; },
    Save: function(d) { return "Bewaar"; },
    "Save-Item": function(d) { return "Item opslaan"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "Informatiedialoog in 1Password opslaan of bijwerken"; },
    "Select-a-vault": function(d) { return "Selecteer een kluis"; },
    "Select-account": function(d) { return "Account selecteren"; },
    "Set-Spending-Limit": function(d) { return "Bestedingslimiet instellen"; },
    "Single-Use": function(d) { return "Eenmalig gebruik"; },
    "Spending-Limit": function(d) { return "Bestedingslimiet"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Stap 3: bekijk en wijzig eventueel je nieuwe inlogitem. Sla het vervolgens op."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "De integratie met Brex is uitgeschakeld. Stel opnieuw in op 1Password.com"; },
    "The-reason-for-a-card-is-required": function(d) { return "De reden voor een kaart is verplicht"; },
    "The-spending-limit-is-required": function(d) { return "De bestedingslimiet is verplicht"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "Tik dan op de indicator dat je offline bent (<offlineIndicator />) en voltooi het inlogproces om je wijzigingen op te slaan."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "Deze kluis is vergrendeld. Probeer het na het ontgrendelen opnieuw."; },
    Title: function(d) { return "Titel"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "Ontgrendel een account om verder te gaan met opslaan met 1Password."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Verificatie voor privékaart mislukt. Controleer je API-sleutel en probeer het opnieuw."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "Kaart aanmaken mislukt, probeer het opnieuw."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Kan geen creditcarditem aanmaken via privékaart."; },
    "Unable-to-enable-integration": function(d) { return "Kan integratie niet inschakelen"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Kan de integratie voor privékaart niet inschakelen. Probeer het later opnieuw."; },
    "Unable-to-parse-URL": function(d) { return "Kan URL niet verwerken"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "Kan item niet opslaan. Controleer of de kluis ontgrendeld is en probeer het opnieuw."; },
    Update: function(d) { return "Bijwerken"; },
    "Update-Existing": function(d) { return "Bestaande bijwerken"; },
    "Update-Item": function(d) { return "Item bijwerken"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "Gebruik 1Password om overal waar je online betaalt privékaarten aan te maken aan te vullen en sla betaalkaarten op voor toekomstig gebruik."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "Verbinding maken voor privékaart mislukt. Controleer je internetverbinding en probeer het opnieuw."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "Je moet een bestedingslimiet voor de kaart instellen."; },
    "Your-account-is-offline-": function(d) { return "Je account is offline."; },
    email: function(d) { return "e-mail"; },
    "every-month": function(d) { return "elke maand"; },
    "every-quarter": function(d) { return "elk kwartaal"; },
    "every-transaction": function(d) { return "elke transactie"; },
    "every-year": function(d) { return "elk jaar"; },
    forever: function(d) { return "voor altijd"; },
    "in-1-Month": function(d) { return "over 1 maand"; },
    "in-1-Year": function(d) { return "over 1 jaar"; },
    "in-7-Days": function(d) { return "over 7 dagen"; },
    "loading---": function(d) { return "laden..."; },
    "one-time": function(d) { return "eenmalig"; }
  },
  pt: {
    "1Password-is-locked": function(d) { return "O 1Password está bloqueado"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "O 1Password está bloqueado. Tente novamente após desbloquear."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "É necessária uma assinatura do 1Password para integrar com o Privacy."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "É necessária uma duração para o limite de gastos"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Ocorreu um problema ao comunicar com Brex.com. Por favor, tente novamente"; },
    "A-title-is-required": function(d) { return "É necessário um título"; },
    "Add-to-1Password": function(d) { return "Adicionar ao 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "Todos os cofres estão desativados."; },
    "An-error-has-occurred": function(d) { return "Ocorreu um erro"; },
    "An-unexpected-error-occurred-": function(d) { return "Ocorreu um erro inesperado."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Ocorreu um erro inesperado. Entre em contato <NAME_EMAIL>"; },
    "Assign-to-Account": function(d) { return "Atribuir à conta"; },
    Back: function(d) { return "Voltar"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Os cartões Brex são armazenados e sincronizados no Brex.com."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "O Brex preencherá automaticamente os memorandos de transação neste cartão."; },
    Cancel: function(d) { return "Cancelar"; },
    "Choose-an-account": function(d) { return "Escolha uma conta"; },
    Close: function(d) { return "Fechar"; },
    "Conceal-previous-value": function(d) { return "Ocultar valor anterior"; },
    Confirm: function(d) { return "Confirmar"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Conectando o Privacy Card…"; },
    "Create---Fill": function(d) { return "Criar e preencher"; },
    "Create---Fill-SSH-Key": function(d) { return "Criar e preencher chave SSH"; },
    "Create-Brex-vendor-card-": function(d) { return "Crie um cartão de fornecedor Brex…"; },
    "Create-Masked-Email": function(d) { return "Criar e-mail mascarado"; },
    "Create-Privacy-Card": function(d) { return "Criar cartão de privacidade"; },
    Custom: function(d) { return "Personalizado"; },
    "Enter-the-amount-in-dollars": function(d) { return "Insira o valor em dólares"; },
    Error: function(d) { return "Erro"; },
    "Error-creating-card-": function(d) { return "Erro ao criar cartão."; },
    "Error-creating-card---message-": function(d) { return "Erro ao criar cartão: " + d.message; },
    "Existing-items": function(d) { return "Itens existentes"; },
    "Fill-Email": function(d) { return "Preencher e-mail"; },
    "Funding-Source": function(d) { return "Fonte de financiamento"; },
    "Get-Help-": function(d) { return "Obtenha ajuda…"; },
    "Input-field-for-the-item-s-title": function(d) { return "Campo de entrada para o título do item"; },
    "Loading-": function(d) { return "Carregando…"; },
    "Lock-Card": function(d) { return "Cartão de bloqueio"; },
    "Lock-card-is-required": function(d) { return "Cartão de bloqueio é necessário"; },
    "New-Item": function(d) { return "Novo Item"; },
    None: function(d) { return "Nenhum"; },
    OK: function(d) { return "OK"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "Depois de salvo, nós o lembraremos para você."; },
    "Open-1Password": function(d) { return "Abra o 1Password"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "Ative a integração de Privacidade no menu de contexto das Ferramentas do desenvolvedor."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Certifique-se de que haja pelo menos uma fonte de financiamento associada à sua conta Privacy.com e tente novamente."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "Insira um nome para o cartão."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "Insira um nome menor para o cartão."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "Insira um limite de gastos menor para o cartão."; },
    "Please-select-a-vault-": function(d) { return "Selecione um cofre."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "Desbloqueie o 1Password para habilitar a integração."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "Desbloqueie a conta selecionada para habilitar a integração."; },
    "Press--shortcut--to-open-1Password-": function(d) { return "Pressione " + d.shortcut + " para abrir o 1Password."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "Pressione " + d.shortcut + " para desbloquear o 1Password"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "Pressione o ícone 1Password na barra de ferramentas do seu navegador para abrir o 1Password."; },
    Previous: function(d) { return "Anterior"; },
    "Privacy-Card": function(d) { return "Cartões Privacy"; },
    "Reason-for-card": function(d) { return "Motivo do cartão"; },
    Regenerate: function(d) { return "Regenerar"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return "Regenerar novamente em " + d.secondsLeft + "s"; },
    "Reveal-previous-value": function(d) { return "Revelar valor anterior"; },
    "Reveal-to-see-previous-value": function(d) { return "Revelar para ver o valor anterior"; },
    Save: function(d) { return "Salvar"; },
    "Save-Item": function(d) { return "Salvar item"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "Caixa de diálogo Salvar ou atualizar informações no 1Password"; },
    "Select-a-vault": function(d) { return "Selecione um cofre"; },
    "Select-account": function(d) { return "Selecione a conta"; },
    "Set-Spending-Limit": function(d) { return "Definir limite de gastos"; },
    "Single-Use": function(d) { return "Uso Único"; },
    "Spending-Limit": function(d) { return "Limite de gastos"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Etapa 3: revise ou edite seu novo item de login e salve-o."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "A integração com o Brex foi desabilitada, reconecte-a no 1Password.com"; },
    "The-reason-for-a-card-is-required": function(d) { return "O motivo do cartão é obrigatório"; },
    "The-spending-limit-is-required": function(d) { return "O limite de gastos é obrigatório"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "Em seguida, toque no indicador offline ( <offlineIndicator /> ) e conclua o login para salvar suas alterações."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "Este cofre está bloqueado. Por favor, tente novamente após desbloquear."; },
    Title: function(d) { return "Título"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "Para continuar economizando com o 1Password, desbloqueie uma conta."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Não é possível autenticar com Privacidade. Verifique sua chave de API e tente novamente."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "Não foi possível criar o cartão, tente novamente."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Não é possível criar um item de cartão de crédito a partir do Privacy Card."; },
    "Unable-to-enable-integration": function(d) { return "Não é possível habilitar a integração"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Não é possível habilitar a integração de Privacidade. Tente novamente mais tarde."; },
    "Unable-to-parse-URL": function(d) { return "Não é possível analisar URL"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "Não foi possível salvar o item. Verifique se o cofre está desbloqueado e tente novamente."; },
    Update: function(d) { return "Atualizar"; },
    "Update-Existing": function(d) { return "Atualizar existente"; },
    "Update-Item": function(d) { return "Atualizar item"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "Use o 1Password para criar e preencher Cartões de Privacidade em todos os lugares em que você fizer pagamentos on-line e salve os cartões de comerciante para uso futuro."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "Não foi possível acessar o Privacy. Verifique a conexão com a internet e tente novamente."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "You must provide a spending limit for the card."; },
    "Your-account-is-offline-": function(d) { return "Sua conta está offline."; },
    email: function(d) { return "e-mail"; },
    "every-month": function(d) { return "todos os meses"; },
    "every-quarter": function(d) { return "todo trimestre"; },
    "every-transaction": function(d) { return "toda transação"; },
    "every-year": function(d) { return "todos os anos"; },
    forever: function(d) { return "vitalício"; },
    "in-1-Month": function(d) { return "em 1 mês"; },
    "in-1-Year": function(d) { return "em 1 ano"; },
    "in-7-Days": function(d) { return "em 7 dias"; },
    "loading---": function(d) { return "carregando..."; },
    "one-time": function(d) { return "um tempo"; }
  },
  ru: {
    "1Password-is-locked": function(d) { return "1Password заблокирован"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password заблокирован. Пожалуйста, попробуйте ещё раз после разблокировки."; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "Для интеграции с Privacy требуется подписка на 1Password."; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "Необходимо указать продолжительность лимита расходов"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "Во время подключения к Brex.com произошла ошибка. Пожалуйста, попробуйте еще раз"; },
    "A-title-is-required": function(d) { return "Требуется название"; },
    "Add-to-1Password": function(d) { return "Добавить в 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "Все сейфы деактивированы."; },
    "An-error-has-occurred": function(d) { return "Произошла ошибка"; },
    "An-unexpected-error-occurred-": function(d) { return "Произошла непредвиденная ошибка."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Возникла непредвиденная ошибка. Пожалуйста, свяжитесь с нами: <EMAIL>"; },
    "Assign-to-Account": function(d) { return "Присвоить аккаунту"; },
    Back: function(d) { return "Назад"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Карты Brex сохраняются и синхронизируются из Brex.com."; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex будет автоматически заполнять информацию о транзакциях на этой карте."; },
    Cancel: function(d) { return "Отменить"; },
    "Choose-an-account": function(d) { return "Выберите аккаунт"; },
    Close: function(d) { return "Закрыть"; },
    "Conceal-previous-value": function(d) { return "Скрыть предыдущее значение"; },
    Confirm: function(d) { return "Подтвердить"; },
    "Connecting-to-Privacy-Card-": function(d) { return "Соединение с картой Privacy…"; },
    "Create---Fill": function(d) { return "Создать и заполнить"; },
    "Create---Fill-SSH-Key": function(d) { return "Создать и заполнить SSH-ключ"; },
    "Create-Brex-vendor-card-": function(d) { return "Создать карту поставщика Brex…"; },
    "Create-Masked-Email": function(d) { return "Создать замаскированный адрес эл. почты"; },
    "Create-Privacy-Card": function(d) { return "Создать карту Privacy"; },
    Custom: function(d) { return "Индивидуальные"; },
    "Enter-the-amount-in-dollars": function(d) { return "Укажите сумму в долларах"; },
    Error: function(d) { return "Ошибка"; },
    "Error-creating-card-": function(d) { return "При создании карты возникла ошибка."; },
    "Error-creating-card---message-": function(d) { return "При создании карты возникла ошибка: " + d.message; },
    "Existing-items": function(d) { return "Существующие элементы"; },
    "Fill-Email": function(d) { return "Заполнить адрес эл. почты"; },
    "Funding-Source": function(d) { return "Источник финансирования"; },
    "Get-Help-": function(d) { return "Помощь…"; },
    "Input-field-for-the-item-s-title": function(d) { return "Поле ввода для названия элемента"; },
    "Loading-": function(d) { return "Загрузка…"; },
    "Lock-Card": function(d) { return "Заблокировать карту"; },
    "Lock-card-is-required": function(d) { return "Требуется заблокировать карту"; },
    "New-Item": function(d) { return "Новый элемент"; },
    None: function(d) { return "Нет"; },
    OK: function(d) { return "OK"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "После сохранения мы запомним его за вас."; },
    "Open-1Password": function(d) { return "Открыть 1Password"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "Пожалуйста, включите интеграцию с Privacy в контекстном меню Инструментов разработчика."; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "Пожалуйста, убедитесь в том, что к вашему аккаунту на Privacy.com прикреплен по крайней мере один Источник финансирования."; },
    "Please-enter-a-name-for-the-card-": function(d) { return "Пожалуйста, укажите название карты."; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "Пожалуйста, укажите более короткое название карты."; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "Пожалуйста, укажите меньший лимит расходов по карте."; },
    "Please-select-a-vault-": function(d) { return "Пожалуйста, выберите сейф."; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "Пожалуйста, разблокируйте 1Password для включения интеграции."; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "Пожалуйста, разблокируйте выбранный аккаунт для включения интеграции."; },
    "Press--shortcut--to-open-1Password-": function(d) { return "Нажмите " + d.shortcut + " для того, чтобы открыть 1Password."; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "Нажмите " + d.shortcut + " для разблокировки 1Password"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "Нажмите на иконку 1Password на панели инструментов своего браузера, чтобы открыть 1Password."; },
    Previous: function(d) { return "Назад"; },
    "Privacy-Card": function(d) { return "Карта Privacy"; },
    "Reason-for-card": function(d) { return "Причина для карты"; },
    Regenerate: function(d) { return "Создать новый"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return "Сгенерировать еще раз через " + d.secondsLeft + " сек."; },
    "Reveal-previous-value": function(d) { return "Показать предыдущее значение"; },
    "Reveal-to-see-previous-value": function(d) { return "Открыть, чтобы смотреть предыдущее значение"; },
    Save: function(d) { return "Сохранить"; },
    "Save-Item": function(d) { return "Сохранить элемент"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "Сохраните или измените информационный диалог в 1Password"; },
    "Select-a-vault": function(d) { return "Выберите сейф"; },
    "Select-account": function(d) { return "Выбрать аккаунт"; },
    "Set-Spending-Limit": function(d) { return "Установить лимит расходов"; },
    "Single-Use": function(d) { return "Одноразовое использование"; },
    "Spending-Limit": function(d) { return "Лимит расходов"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "Шаг 3: Просмотрите или измените новый элемент для входа в аккаунт, а затем сохраните его."; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "Интеграция с Brex отключена. Еще раз установите связь на 1Password.com"; },
    "The-reason-for-a-card-is-required": function(d) { return "Необходима причина для карты"; },
    "The-spending-limit-is-required": function(d) { return "Необходимо указать лимит расходов"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "Затем нажмите на индикатор статуса «Не в сети»‎ ( <offlineIndicator /> ) и войдите в аккаунт, чтобы сохранить изменения."; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "Этот сейф заблокирован. Пожалуйста, попробуйте еще раз после разблокировки."; },
    Title: function(d) { return "Название"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "Чтобы продолжить и сохранить данные в 1Password, разблокируйте аккаунт."; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "Невозможно подтвердить подлинность с помощью Privacy. Пожалуйста, проверьте свой API-ключ и попробуйте еще раз."; },
    "Unable-to-create-card--please-try-again-": function(d) { return "Не удалось создать карту. Пожалуйста, попробуйте еще раз."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "Не удалось создать элемент кредитной карты для карты Privacy."; },
    "Unable-to-enable-integration": function(d) { return "Не удалось активировать интеграцию"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "Не удалось включить интеграцию с Privacy. Пожалуйста, повторите попытку позже."; },
    "Unable-to-parse-URL": function(d) { return "Не удалось проанализировать URL-адрес"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "Не удалось сохранить элемент. Убедитесь, что сейф разблокирован и повторите попытку."; },
    Update: function(d) { return "Обновить"; },
    "Update-Existing": function(d) { return "Изменить существующие"; },
    "Update-Item": function(d) { return "Изменить элемент"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "Используйте 1Password, чтобы создавать и заполнять данные карт Privacy при оплате онлайн и сохранять карты магазинов для дальнейшего использования."; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "Нам не удалось связаться с Privacy. Пожалуйста, проверьте подключение к Интернету и попробуйте снова."; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "Для карты необходимо установить лимит расходов."; },
    "Your-account-is-offline-": function(d) { return "Ваш аккаунт не в сети."; },
    email: function(d) { return "эл. почта"; },
    "every-month": function(d) { return "в месяц"; },
    "every-quarter": function(d) { return "в квартал"; },
    "every-transaction": function(d) { return "каждая транзакция"; },
    "every-year": function(d) { return "в год"; },
    forever: function(d) { return "навсегда"; },
    "in-1-Month": function(d) { return "через 1 месяц"; },
    "in-1-Year": function(d) { return "через 1 год"; },
    "in-7-Days": function(d) { return "через 7 дней"; },
    "loading---": function(d) { return "загрузка..."; },
    "one-time": function(d) { return "один раз"; }
  },
  "zh-CN": {
    "1Password-is-locked": function(d) { return "1Password 被锁定"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password 已锁定。请解锁后重试。"; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "需要 1Password 会员才能与 Privacy 集成。"; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "需要有支付限额的期限"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "与 Brex.com 通信时遇到问题。请再试一次"; },
    "A-title-is-required": function(d) { return "需要标题"; },
    "Add-to-1Password": function(d) { return "添加至 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "所有保险库已禁用。"; },
    "An-error-has-occurred": function(d) { return "发生错误"; },
    "An-unexpected-error-occurred-": function(d) { return "发生意外错误。"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "发生意外错误。请联系 <EMAIL>"; },
    "Assign-to-Account": function(d) { return "分配到帐户"; },
    Back: function(d) { return "返回"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Brex 卡是由 Brex.com 存储和同步的。"; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex 将自动在此卡上填写交易备忘。"; },
    Cancel: function(d) { return "取消"; },
    "Choose-an-account": function(d) { return "选择一个帐户"; },
    Close: function(d) { return "关闭"; },
    "Conceal-previous-value": function(d) { return "隐藏之前的值"; },
    Confirm: function(d) { return "确认"; },
    "Connecting-to-Privacy-Card-": function(d) { return "正在连接 Privacy 卡片…"; },
    "Create---Fill": function(d) { return "创建并填充"; },
    "Create---Fill-SSH-Key": function(d) { return "创建并填充 SSH 密钥"; },
    "Create-Brex-vendor-card-": function(d) { return "创建 Brex 供应商卡片…"; },
    "Create-Masked-Email": function(d) { return "创建蒙面电子邮件"; },
    "Create-Privacy-Card": function(d) { return "创建 Privacy 卡片"; },
    Custom: function(d) { return "自定义"; },
    "Enter-the-amount-in-dollars": function(d) { return "请以美元为单位输入金额"; },
    Error: function(d) { return "错误"; },
    "Error-creating-card-": function(d) { return "制作卡片时发生问题。"; },
    "Error-creating-card---message-": function(d) { return "制作卡片时发生问题：" + d.message; },
    "Existing-items": function(d) { return "现有的项目"; },
    "Fill-Email": function(d) { return "填写电子邮件"; },
    "Funding-Source": function(d) { return "资金来源"; },
    "Get-Help-": function(d) { return "获取帮助…"; },
    "Input-field-for-the-item-s-title": function(d) { return "项目标题的输入字段"; },
    "Loading-": function(d) { return "正在加载..."; },
    "Lock-Card": function(d) { return "锁定卡片"; },
    "Lock-card-is-required": function(d) { return "需要锁定卡片"; },
    "New-Item": function(d) { return "新建项目"; },
    None: function(d) { return "无"; },
    OK: function(d) { return "好"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "保存之后，我们会为你记住。"; },
    "Open-1Password": function(d) { return "打开 1Password"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "请在开发人员工具内容菜单开启 Privacy 集成。"; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "请先确保您的 Privacy.com 帐户至少关联一个资金来源，然后再试一次。"; },
    "Please-enter-a-name-for-the-card-": function(d) { return "请为卡片命名。"; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "请为卡片取个短一点的名字。"; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "请输入较小的卡片额度。"; },
    "Please-select-a-vault-": function(d) { return "请选择保险库。"; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "请解锁 1Password 以启用集成功能。"; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "请解锁选择的帐户以启用集成功能。"; },
    "Press--shortcut--to-open-1Password-": function(d) { return "按下 " + d.shortcut + " 来打开 1Password。"; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "按下 " + d.shortcut + " 来解锁 1Password"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "按下浏览器工具栏上的 1Password 图标以打开 1Password。"; },
    Previous: function(d) { return "上一步"; },
    "Privacy-Card": function(d) { return "Privacy 卡片"; },
    "Reason-for-card": function(d) { return "卡片理由"; },
    Regenerate: function(d) { return "重新生成"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return d.secondsLeft + " 秒后再次重新生成"; },
    "Reveal-previous-value": function(d) { return "显示之前的值"; },
    "Reveal-to-see-previous-value": function(d) { return "显示以查看之前的值"; },
    Save: function(d) { return "保存"; },
    "Save-Item": function(d) { return "保存项目"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "在 1Password 中保存或更新信息对话框"; },
    "Select-a-vault": function(d) { return "选择保险库"; },
    "Select-account": function(d) { return "选择帐户"; },
    "Set-Spending-Limit": function(d) { return "设定支付限额"; },
    "Single-Use": function(d) { return "一次性使用"; },
    "Spending-Limit": function(d) { return "支付限额"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "第 3 步：检查或编辑新的登录项目，然后保存。"; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "与 Brex 的集成已被禁用，请重新与 1Password.com 连接"; },
    "The-reason-for-a-card-is-required": function(d) { return "卡片理由是必需的"; },
    "The-spending-limit-is-required": function(d) { return "支付限额是必需的"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "然后轻按离线指示器（<offlineIndicator />）完成登录以保存更改。"; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "这个保险库已锁定。请解锁后重试。"; },
    Title: function(d) { return "标题"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "要继续使用 1Password 保存，请解锁一个帐户。"; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "无法使用 Privacy 验证。请确认你的 API 密钥并再试一次。"; },
    "Unable-to-create-card--please-try-again-": function(d) { return "创建卡片失败，请重试。"; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "无法从 Privacy 卡片创建信用卡。"; },
    "Unable-to-enable-integration": function(d) { return "无法启用集成"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "无法启用 Privacy 集成。请再试一次。"; },
    "Unable-to-parse-URL": function(d) { return "无法解析 URL"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "无法保存项目。请检查保险库是否解锁后再试一次。"; },
    Update: function(d) { return "更新"; },
    "Update-Existing": function(d) { return "更新现有"; },
    "Update-Item": function(d) { return "更新项目"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "使用 1Password 在线上消费时制作并填入 Privacy Card，保存商业卡还能在未来使用。"; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "我们无法连接 Privacy。请确认你的互联网连接并再试一次。"; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "请提供卡片支付额度。"; },
    "Your-account-is-offline-": function(d) { return "您的帐户已离线。"; },
    email: function(d) { return "电子邮件"; },
    "every-month": function(d) { return "每月"; },
    "every-quarter": function(d) { return "每季度"; },
    "every-transaction": function(d) { return "每笔交易"; },
    "every-year": function(d) { return "每年"; },
    forever: function(d) { return "永远"; },
    "in-1-Month": function(d) { return "1 个月后"; },
    "in-1-Year": function(d) { return "1 年后"; },
    "in-7-Days": function(d) { return "7 天后"; },
    "loading---": function(d) { return "正在加载..."; },
    "one-time": function(d) { return "一次性"; }
  },
  "zh-TW": {
    "1Password-is-locked": function(d) { return "1Password 已上鎖"; },
    "1Password-is-locked--Please-try-again-after-unlocking-": function(d) { return "1Password 已鎖上。請解鎖後重試。"; },
    "A-1Password-membership-is-required-to-integrate-with-Privacy-": function(d) { return "需要 1Password 會員才能與 Privacy 整合。"; },
    "A-duration-for-the-spending-limit-is-required": function(d) { return "需要有支付限額的期限"; },
    "A-problem-occurred-when-communicating-with-Brex-com--Please-try-again": function(d) { return "與 Brex.com 通訊時遇到問題。請再試一次"; },
    "A-title-is-required": function(d) { return "需要標題"; },
    "Add-to-1Password": function(d) { return "添加至 1Password"; },
    "All-vaults-are-disabled-": function(d) { return "所有保險庫已停用。"; },
    "An-error-has-occurred": function(d) { return "發生錯誤"; },
    "An-unexpected-error-occurred-": function(d) { return "發生意外錯誤。"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "發生意外錯誤。請聯繫 <EMAIL>"; },
    "Assign-to-Account": function(d) { return "分配到帳號"; },
    Back: function(d) { return "返回"; },
    "Brex-cards-are-stored-and-sync-from-Brex-com-": function(d) { return "Brex 卡是由 Brex.com 存儲和同步的。"; },
    "Brex-will-auto-fill-transaction-memos-on-this-card-": function(d) { return "Brex 將自動在此卡上填寫交易備忘。"; },
    Cancel: function(d) { return "取消"; },
    "Choose-an-account": function(d) { return "選擇一個帳號"; },
    Close: function(d) { return "關閉"; },
    "Conceal-previous-value": function(d) { return "隱藏之前的值"; },
    Confirm: function(d) { return "確認"; },
    "Connecting-to-Privacy-Card-": function(d) { return "正在連接 Privacy 卡片…"; },
    "Create---Fill": function(d) { return "建立並填入"; },
    "Create---Fill-SSH-Key": function(d) { return "建立並填入 SSH 金鑰"; },
    "Create-Brex-vendor-card-": function(d) { return "建立 Brex 供應商卡片…"; },
    "Create-Masked-Email": function(d) { return "建立蒙面電子郵件"; },
    "Create-Privacy-Card": function(d) { return "建立 Privacy 卡片"; },
    Custom: function(d) { return "自訂"; },
    "Enter-the-amount-in-dollars": function(d) { return "請以美金為單位輸入金額"; },
    Error: function(d) { return "錯誤"; },
    "Error-creating-card-": function(d) { return "製作卡片時發生問題。"; },
    "Error-creating-card---message-": function(d) { return "製作卡片時發生問題：" + d.message; },
    "Existing-items": function(d) { return "現有項目"; },
    "Fill-Email": function(d) { return "填入電子郵件"; },
    "Funding-Source": function(d) { return "資金來源"; },
    "Get-Help-": function(d) { return "取得說明…"; },
    "Input-field-for-the-item-s-title": function(d) { return "項目標題的輸入欄位"; },
    "Loading-": function(d) { return "正在載入⋯"; },
    "Lock-Card": function(d) { return "鎖上卡片"; },
    "Lock-card-is-required": function(d) { return "需要鎖上卡片"; },
    "New-Item": function(d) { return "新增項目"; },
    None: function(d) { return "無"; },
    OK: function(d) { return "好"; },
    "Once-saved--we-ll-remember-it-for-you-": function(d) { return "儲存之後，我們會為你記住。"; },
    "Open-1Password": function(d) { return "開啟 1Password"; },
    "Please-enable-the-Privacy-integration-from-the-Developer-Tools-context-menu-": function(d) { return "請在開發人員工具內容選單開啟 Privacy 整合。"; },
    "Please-ensure-there-is-at-least-one-Funding-Source-associated-with-your-Privacy-com-account--then-try-again-": function(d) { return "請先確保您的 Privacy.com 帳號至少有連接一個資金來源，之後再重試一次。"; },
    "Please-enter-a-name-for-the-card-": function(d) { return "請為卡片命名。"; },
    "Please-enter-a-smaller-name-for-the-card-": function(d) { return "請為卡片取個字數少一點的名字。"; },
    "Please-enter-a-smaller-spending-limit-for-the-card-": function(d) { return "請輸入較小的卡片額度。"; },
    "Please-select-a-vault-": function(d) { return "請選擇保險庫。"; },
    "Please-unlock-1Password-to-enable-the-integration-": function(d) { return "請解鎖 1Password 以啟用整合功能。"; },
    "Please-unlock-the-selected-account-to-enable-the-integration-": function(d) { return "請解鎖選擇的帳號以啟用整合功能。"; },
    "Press--shortcut--to-open-1Password-": function(d) { return "按 " + d.shortcut + " 來開啟 1Password。"; },
    "Press--shortcut--to-unlock-1Password": function(d) { return "按 " + d.shortcut + " 來解鎖 1Password"; },
    "Press-the-1Password-icon-in-your-browser-s-toolbar-to-open-1Password-": function(d) { return "按下瀏覽器工具列上的 1Password 圖示以開啟 1Password。"; },
    Previous: function(d) { return "上一步"; },
    "Privacy-Card": function(d) { return "Privacy 卡片"; },
    "Reason-for-card": function(d) { return "卡片理由"; },
    Regenerate: function(d) { return "重新產生"; },
    "Regenerate-again-in--secondsLeft-s": function(d) { return d.secondsLeft + " 秒後再次重新產生"; },
    "Reveal-previous-value": function(d) { return "顯示之前的值"; },
    "Reveal-to-see-previous-value": function(d) { return "顯示以查看之前的值"; },
    Save: function(d) { return "儲存"; },
    "Save-Item": function(d) { return "儲存項目"; },
    "Save-or-update-information-dialog-in-1Password": function(d) { return "在 1Password 中儲存或更新資訊對話方塊"; },
    "Select-a-vault": function(d) { return "選擇保險庫"; },
    "Select-account": function(d) { return "選擇帳號"; },
    "Set-Spending-Limit": function(d) { return "設定支付限額"; },
    "Single-Use": function(d) { return "一次性使用"; },
    "Spending-Limit": function(d) { return "支付限額"; },
    "Step-3--Review-or-edit-your-new-login-item--then-save-it-": function(d) { return "第 3 步：檢查或編輯新的登入項目，然後儲存。"; },
    "The-integration-with-Brex-has-been-disabled--please-reconnect-it-on-1Password-com": function(d) { return "與 Brex 的整合已被停用，請重新與 1Password.com 連接"; },
    "The-reason-for-a-card-is-required": function(d) { return "卡片理由是必需的"; },
    "The-spending-limit-is-required": function(d) { return "支付限額是必需的"; },
    "Then-tap-the-offline-indicator----offlineIndicator------and-finish-signing-in-to-save-your-changes-": function(d) { return "然後輕按離線指示器（<offlineIndicator />）完成登入以儲存變更。"; },
    "This-vault-is-locked--Please-try-again-after-unlocking-": function(d) { return "這個保險庫已鎖上。請解鎖後重試。"; },
    Title: function(d) { return "標題"; },
    "To-continue-saving-with-1Password--unlock-an-account-": function(d) { return "要繼續使用 1Password 儲存，請解鎖一個帳號。"; },
    "Unable-to-authenticate-with-Privacy--Please-check-your-API-key-and-try-again-": function(d) { return "無法使用 Privacy 驗證。請確認你的 API 金鑰並再試一次。"; },
    "Unable-to-create-card--please-try-again-": function(d) { return "建立卡片失敗，請重試."; },
    "Unable-to-create-credit-card-item-from-Privacy-Card-": function(d) { return "無法從 Privacy 卡片製作信用卡。"; },
    "Unable-to-enable-integration": function(d) { return "無法啟用整合"; },
    "Unable-to-enable-the-Privacy-integration--Please-try-again-later-": function(d) { return "無法啟用 Privacy 整合。請再試一次。"; },
    "Unable-to-parse-URL": function(d) { return "無法解析 URL"; },
    "Unable-to-save-item--Check-that-the-vault-is-unlocked-and-try-again-": function(d) { return "無法儲存項目。請檢查保險庫是否解鎖後再試一次。"; },
    Update: function(d) { return "更新"; },
    "Update-Existing": function(d) { return "更新現有"; },
    "Update-Item": function(d) { return "更新項目"; },
    "Use-1Password-to-create-and-fill-Privacy-Cards-everywhere-you-pay-online--and-save-merchant-cards-for-future-use-": function(d) { return "使用 1Password 在線上消費時製作並填入 Privacy Card，儲存商業卡還能在未來使用。"; },
    "We-were-unable-to-reach-Privacy--Please-check-your-internet-connection-and-try-again-": function(d) { return "我們無法連接 Privacy。請確認你的網路連線並再試一次。"; },
    "You-must-provide-a-spending-limit-for-the-card-": function(d) { return "請提供卡片支付額度。"; },
    "Your-account-is-offline-": function(d) { return "您的帳號已離線。"; },
    email: function(d) { return "電子郵件"; },
    "every-month": function(d) { return "每月"; },
    "every-quarter": function(d) { return "每季度"; },
    "every-transaction": function(d) { return "每筆交易"; },
    "every-year": function(d) { return "每年"; },
    forever: function(d) { return "永遠"; },
    "in-1-Month": function(d) { return "1 個月後"; },
    "in-1-Year": function(d) { return "1 年後"; },
    "in-7-Days": function(d) { return "7 天後"; },
    "loading---": function(d) { return "正在載入..."; },
    "one-time": function(d) { return "一次性"; }
  }
}