var de = function(n, ord
) {
  var s = String(n).split('.'), v0 = !s[1];
  if (ord) return 'other';
  return (n == 1 && v0) ? 'one' : 'other';
};
var en = function(n, ord
) {
  var s = String(n).split('.'), v0 = !s[1], t0 = Number(s[0]) == n,
      n10 = t0 && s[0].slice(-1), n100 = t0 && s[0].slice(-2);
  if (ord) return (n10 == 1 && n100 != 11) ? 'one'
      : (n10 == 2 && n100 != 12) ? 'two'
      : (n10 == 3 && n100 != 13) ? 'few'
      : 'other';
  return (n == 1 && v0) ? 'one' : 'other';
};
var es = function(n, ord
) {
  if (ord) return 'other';
  return (n == 1) ? 'one' : 'other';
};
var fr = function(n, ord
) {
  if (ord) return (n == 1) ? 'one' : 'other';
  return (n >= 0 && n < 2) ? 'one' : 'other';
};
var it = function(n, ord
) {
  var s = String(n).split('.'), v0 = !s[1];
  if (ord) return ((n == 11 || n == 8 || n == 80
          || n == 800)) ? 'many' : 'other';
  return (n == 1 && v0) ? 'one' : 'other';
};
var ja = function(n, ord
) {
  if (ord) return 'other';
  return 'other';
};
var ko = function(n, ord
) {
  if (ord) return 'other';
  return 'other';
};
var nl = function(n, ord
) {
  var s = String(n).split('.'), v0 = !s[1];
  if (ord) return 'other';
  return (n == 1 && v0) ? 'one' : 'other';
};
var pt = function(n, ord
) {
  var s = String(n).split('.'), i = s[0];
  if (ord) return 'other';
  return ((i == 0
          || i == 1)) ? 'one' : 'other';
};
var ru = function(n, ord
) {
  var s = String(n).split('.'), i = s[0], v0 = !s[1], i10 = i.slice(-1),
      i100 = i.slice(-2);
  if (ord) return 'other';
  return (v0 && i10 == 1 && i100 != 11) ? 'one'
      : (v0 && (i10 >= 2 && i10 <= 4) && (i100 < 12
          || i100 > 14)) ? 'few'
      : (v0 && i10 == 0 || v0 && (i10 >= 5 && i10 <= 9)
          || v0 && (i100 >= 11 && i100 <= 14)) ? 'many'
      : 'other';
};
var number = function (value, name, offset) {
  if (!offset) return value;
  if (isNaN(value)) throw new Error("Can't apply offset:" + offset + ' to argument `' + name + '` with non-numerical value ' + JSON.stringify(value) + '.');
  return value - offset;
};
var plural = function (value, offset, lcfunc, data, isOrdinal) {
  if ({}.hasOwnProperty.call(data, value)) return data[value];
  if (offset) value -= offset;
  var key = lcfunc(value, isOrdinal);
  return key in data ? data[key] : data.other;
};

export default {
  de: {
    "--accountName---was-added-to-1Password-": function(d) { return "\"" + d.accountName + "\" zu 1Password hinzugefügt."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "\"" + d.itemName + "\" wurde in \"" + d.vaultName + "\" gespeichert."; },
    "-Internal--Report-feedback": function(d) { return "[Intern] Feedback zum Bericht"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " ist offline."; },
    "-count--Items-archived": function(d) { return d.count + " Objekte archiviert"; },
    "-count--Items-deleted": function(d) { return d.count + " Objekte gelöscht"; },
    "-count--Items-favorited": function(d) { return d.count + " Objekte als Favoriten markiert"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " Objekte aus Favoriten entfernt"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.deviceName + " in der Nähe von " + d.location + " versucht, sich bei Ihrem " + d.accountName + "-Konto anzumelden."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.deviceName + " in der Nähe von " + d.location + " versucht, sich bei Ihrem " + d.accountName + "-Konto anzumelden. Zum Anzeigen öffnen Sie bitte 1Password in der Symbolleiste Ihres Browsers."; },
    "-fileName--downloaded": function(d) { return "„" + d.fileName + "“ heruntergeladen"; },
    "-itemType--saved": function(d) { return d.itemType + " gespeichert"; },
    "1Password-8-Required": function(d) { return "1Password 8 erforderlich"; },
    "1Password-account-added": function(d) { return "1Password-Konto hinzugefügt"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password kann die Datenbank nicht öffnen, da nicht genügend Speicherplatz für " + d.browser + " verfügbar ist. Um 1Password zu verwenden, geben Sie bitte etwas Speicherplatz frei."; },
    "1Password-is-up-to-date": function(d) { return "1Password ist aktuell"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password macht auf dieser Seite keine Vorschläge mehr zum automatischen Ausfüllen.\n\nDie Liste verborgener Seiten wird in diesem Webbrowser gespeichert. Sie können die Liste jederzeit in den Einstellungen löschen."; },
    "A-database-error-occurred-": function(d) { return "Ein Datenbankfehler ist aufgetreten."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "Eine Website wurden in " + d.count + " von Ihren Konten Watchtower hinzugefügt."; },
    "API-Key": function(d) { return "API-Schlüssel"; },
    "Add-account-": function(d) { return "Konto hinzufügen…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Alle diese Objekte werden sofort entfernt. Sie können diese jedoch für eine begrenzte Zeit auf 1Password.com wiederherstellen."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "Bem Generieren der maskierten E-Mail ist ein Fehler aufgetreten."; },
    "An-error-occurred--Please-try-again-": function(d) { return "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "Beim Aktivieren und Ausfüllen der maskierten E-Mail ist ein Fehler aufgetreten."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Es ist ein unerwarteter Fehler aufgetreten. Bitte <NAME_EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "Ein unbekannter Fehler ist aufgetreten."; },
    "Any-Website": function(d) { return "Jede Website"; },
    Archive: function(d) { return "Archivieren"; },
    "Archive--count--items-": function(d) { return d.count + " Elemente archivieren?"; },
    "Archived---title--": function(d) { return "„" + d.title + "“ archiviert"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "Archivierte Objekte werden nicht in Füllvorschlägen für Websites oder in Apps angezeigt."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "Soll „" + d.itemName + "“ wirklich archiviert werden?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "Soll „" + d.itemName + "“ wirklich gelöscht werden?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "Sind Sie sicher, dass Sie " + plural(d.count, 0, de, { one: number(d.count, "count") + " Objekt", other: number(d.count, "count") + " Objekte" }) + " löschen möchten?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "Sind Sie sicher, dass Sie dieses Passwort löschen möchten?"; },
    "Authentication-Required": function(d) { return "Authentifizierung erforderlich"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "Authentifizierung wurde unterbrochen oder hat eine Zeitüberschreitung verursacht. Bitte versuchen Sie es erneut."; },
    Benchmarking: function(d) { return "Benchmarking"; },
    Cancel: function(d) { return "Abbrechen"; },
    "Collect-Page-Structure": function(d) { return "Seitenstruktur sammeln"; },
    "Continue-Signing-In": function(d) { return "Anmeldung fortsetzen"; },
    "Copied--fieldTitle-": function(d) { return d.fieldTitle + " kopiert"; },
    "Copied-field": function(d) { return "Kopiertes Feld"; },
    "Copied-generated-password": function(d) { return "Generiertes Passwort kopiert"; },
    "Copied-item-JSON": function(d) { return "Objekt-JSON kopiert"; },
    "Copied-item-UUID": function(d) { return "Objekt-UUID kopiert"; },
    "Copied-item-link": function(d) { return "Objektlink kopiert"; },
    "Copied-one-time-password": function(d) { return "Einmal-Passwort kopiert"; },
    "Corporate-Card": function(d) { return "Firmenkarte"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "Verbindung zur 1Password-App konnte nicht hergestellt werden. Versuchen Sie erneut, die App zu entsperren."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "Authentifizierung konnte nicht abgeschlossen werden. Aktualisieren Sie die 1Password-Desktop-Version und versuchen Sie es erneut."; },
    "Create-Brex-vendor-card-": function(d) { return "Brex-Verkäuferkarte erstellen …"; },
    "Create-Masked-Email-": function(d) { return "Verschleierte E-Mail erstellen…"; },
    "Create-Privacy-Card-": function(d) { return "Privacy Card erstellen…"; },
    "Create-SSH-Key-": function(d) { return "SSH-Key erstellen…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "Für das Erstellen und Bearbeiten von SSH-Keys ist 1Password 8 erforderlich."; },
    "Credit-Card": function(d) { return "Kreditkarte"; },
    "Credit-card": function(d) { return "Kreditkarte"; },
    Delete: function(d) { return "Löschen"; },
    "Deleted---title--": function(d) { return "„" + d.title + "“ gelöscht"; },
    "Disable-Extensions": function(d) { return "Erweiterungen deaktivieren"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "Andere Versionen von 1Password im Browser deaktivieren?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "Automatische Anmeldung für „" + d.title + "“ deaktiviert"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "Automatische Anmeldung für " + d.count + " Objekte deaktiviert"; },
    "Disk-space-too-low": function(d) { return "Speicherplatz ist zu niedrig"; },
    "Do-you-have-an-existing-account-": function(d) { return "Haben Sie ein bestehendes Konto?"; },
    "Don-t-Save-in-1Password": function(d) { return "Nicht in 1Password speichern"; },
    "Download-failed": function(d) { return "Download fehlgeschlagen"; },
    "Duo-Authentication-Required": function(d) { return "Duo-Authentifizierung erforderlich"; },
    "Email-blocked": function(d) { return "E-Mail blockiert"; },
    "Email-unblocked": function(d) { return "E-Mail nicht mehr blockiert"; },
    Employee: function(d) { return "Angestellte"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "Automatische Anmeldung für „" + d.title + "“ aktiviert"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "Automatische Anmeldung für " + d.count + " Objekte aktiviert"; },
    "Enter-Account-Password": function(d) { return "Kontopasswort eingeben"; },
    "Failed-to-copy-field": function(d) { return "Feld konnte nicht kopiert werden"; },
    "Failed-to-delete-password": function(d) { return "Fehler beim Löschen des Passworts"; },
    "Favorited---title--": function(d) { return "„" + d.title + "“ als Favorit markiert"; },
    "Fill-Masked-Email-": function(d) { return "Verschleierte E-Mail ausfüllen …"; },
    "Get-Help": function(d) { return "Hilfe anfordern"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "Die Frist für die Migration der Authentifizierungsmethode ist abgelaufen. Bitte melden Sie sich erneut an."; },
    Help: function(d) { return "Hilfe"; },
    "Hide-on-this-page": function(d) { return "Auf dieser Seite ausblenden"; },
    Identity: function(d) { return "Identität"; },
    Item: function(d) { return "Objekt"; },
    "Learn-More-": function(d) { return "Erfahren Sie mehr…"; },
    "Link-Existing": function(d) { return "Bestehende verknüpfen"; },
    Lock: function(d) { return "Sperren"; },
    Login: function(d) { return "Login"; },
    Managed: function(d) { return "Verwaltet"; },
    "New-app-or-browser-found": function(d) { return "Neue App oder neuer Browser gefunden"; },
    "New-item-saved-in-1Password-": function(d) { return "Neues Objekt in 1Password gespeichert."; },
    "No-QR-code-found-for-2FA": function(d) { return "Kein QR-Code für 2FA gefunden"; },
    "No-accounts-found-": function(d) { return "Keine Konten gefunden."; },
    OK: function(d) { return "OK"; },
    Ok: function(d) { return "OK"; },
    Personal: function(d) { return "Persönlich"; },
    "Physical-Card": function(d) { return "Physische Karte"; },
    Private: function(d) { return "Persönlich"; },
    "Removed---title---from-favorites": function(d) { return "„" + d.title + "“ aus Favoriten entfernt"; },
    "Report-Issue": function(d) { return "Problem melden"; },
    "Report-error-": function(d) { return "Fehler melden…"; },
    Save: function(d) { return "Speichern"; },
    Security: function(d) { return "Sicherheit"; },
    Settings: function(d) { return "Einstellungen"; },
    Shared: function(d) { return "Geteilt"; },
    "Sign-Up": function(d) { return "Anmelden"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "Melden Sie sich in Ihrem 1Password-Konto (" + d.accountName + ") an und authentifizieren Sie sich mit Duo, um fortzufahren."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "Melden Sie sich in Ihrem 1Password-Konto (" + d.accountName + ") an, um fortzufahren."; },
    "Sign-in-with--authProvider-": function(d) { return "Anmelden mit " + d.authProvider; },
    "Sign-in-with-passkey": function(d) { return "Mit Passkey anmelden"; },
    "Stay-Offline": function(d) { return "Offline bleiben"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "Vielen Dank, dass Sie die neueste Version (" + d.version + ") von 1Password nutzen!"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "Das hat nicht funktioniert. Überprüfen Sie Ihr Passwort und versuchen Sie es erneut."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "Diese Karte wurde gesperrt/gekündigt. Bitte verwenden Sie eine andere Karte."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "Dieser Eintrag wird sofort entfernt."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Dieses Objekt wird sofort entfernt. Sie können Objekte für eine begrenzte Zeit auf 1Password.com wiederherstellen."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "Um ein Objekt in diesem Konto zu speichern, müssen Sie es mit dem zuvor ausgewählten Konto für maskierte E-Mails verknüpfen."; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "Der Touch ID-Sensor ist momentan nicht verfügbar."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "Der Touch ID-Sensor ist momentan nicht verfügbar. Sie müssen sich mit Ihrem Kontopasswort anmelden."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "Feld für Einmal-Passwort kann dem Objekt nicht hinzugefügt werden"; },
    "Unable-to-copy-generated-password": function(d) { return "Generiertes Passwort konnte nicht kopiert werden"; },
    "Unable-to-fill-generated-password": function(d) { return "Generiertes Passwort kann nicht eingetragen werden"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "Einmal-Passwort kann nicht generiert und kopiert werden"; },
    "Unable-to-save-generated-password": function(d) { return "Generiertes Passwort kann nicht gespeichert werden"; },
    "Unable-to-unlock-account-": function(d) { return "Entsperren des Kontos nicht möglich."; },
    "Unknown-Device": function(d) { return "Unbekanntes Gerät"; },
    Unlock: function(d) { return "Entsperren"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "Konto von der Desktop-App freischalten"; },
    "Use-Suggested-Password": function(d) { return "Empfohlenes Passwort nutzen"; },
    "Vendor-Card": function(d) { return "Verkäuferkarte"; },
    "Watchtower-Alert": function(d) { return "Watchtower-Alarm"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "Wir haben erkannt, dass andere Versionen dieser Erweiterung installiert sind. Möchten Sie " + d.extensionNames + " deaktivieren?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "Sie können einen Link zu einem bestehenden Familienkonto wählen oder ein neues Konto erstellen."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "Sie werden gebeten, Ihr Passwort erneut einzugeben, weil für " + d.browserName + " ein Update verfügbar ist."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "Sie sind offline. Um auf Ihr Konto zuzugreifen, überprüfen Sie bitte Ihre Internetverbindung und versuchen es dann erneut."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "Sie haben das tägliche Limit für die Erstellung maskierter E-Mails für das ausgewählte Konto erreicht."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "Sie haben das Limit für die Erstellung von maskierten E-Mails für das ausgewählte Konto erreicht."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "Sie können Änderungen erst dann speichern, wenn Sie angemeldet und online sind."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "Sie können Änderungen erst speichern, wenn Sie die Anmeldung abgeschlossen haben."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "Sie können ein generiertes Passwort erst dann verwenden, wenn Sie angemeldet und online sind."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "Ihr Kontozugriff wurde vorübergehend ausgesetzt. Weitere Informationen finden Sie in Ihrem E-Mail-Postfach."; },
    "Your-account-is-offline": function(d) { return "Ihr Konto ist offline"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "Ihr Konto wurde suspendiert. Bitte kontaktieren Sie Ihren Familien- oder Team-Administrator für mehr Informationen."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "Ihr Konto ist nicht verifiziert. Melden Sie sich bei Fastmail an, um das ausgewählte Konto zu verifizieren."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "Ihr Sitzung ist abgelaufen. 1Password im Browser möchte, dass Sie sich erneut anmelden, um sich zu authentifizieren."; },
    "one-time-password": function(d) { return "einmaliges Passwort"; },
    "your-browser": function(d) { return "Ihren Browser"; },
    "unit.B": function(d) { return d.size + " Bytes"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "Login"; },
    "category.002": function(d) { return "Kreditkarte"; },
    "category.003": function(d) { return "Sichere Notiz"; },
    "category.004": function(d) { return "Identität"; },
    "category.005": function(d) { return "Passwort"; },
    "category.006": function(d) { return "Dokument"; },
    "category.100": function(d) { return "Softwarelizenz"; },
    "category.101": function(d) { return "Bankkonto"; },
    "category.102": function(d) { return "Datenbank"; },
    "category.103": function(d) { return "Führerschein"; },
    "category.104": function(d) { return "Jagdschein"; },
    "category.105": function(d) { return "Mitgliedschaft"; },
    "category.106": function(d) { return "Reisepass"; },
    "category.107": function(d) { return "Treueprogramm"; },
    "category.108": function(d) { return "Sozialversicherungsnummer"; },
    "category.109": function(d) { return "WLAN"; },
    "category.110": function(d) { return "Server"; },
    "category.111": function(d) { return "E-Mail-Konto"; },
    "categories.001": function(d) { return "Logins"; },
    "categories.002": function(d) { return "Kreditkarten"; },
    "categories.003": function(d) { return "Sichere Notizen"; },
    "categories.004": function(d) { return "Identitäten"; },
    "categories.005": function(d) { return "Passwörter"; },
    "categories.006": function(d) { return "Dokumente"; },
    "categories.100": function(d) { return "Softwarelizenzen"; },
    "categories.101": function(d) { return "Bankkonten"; },
    "categories.102": function(d) { return "Datenbanken"; },
    "categories.103": function(d) { return "Führerscheine"; },
    "categories.104": function(d) { return "Jagdscheine"; },
    "categories.105": function(d) { return "Mitgliedschaften"; },
    "categories.106": function(d) { return "Reisepässe"; },
    "categories.107": function(d) { return "Treueprogramme"; },
    "categories.108": function(d) { return "Sozialversicherungsnummern"; },
    "categories.109": function(d) { return "WLANs"; },
    "categories.110": function(d) { return "Server"; },
    "categories.111": function(d) { return "E-Mail-Konten"; },
    "button.save": function(d) { return "Speichern"; },
    "button.cancel": function(d) { return "Abbrechen"; },
    "unexpected-error": function(d) { return "Es ist ein unerwarteter Fehler aufgetreten. Bitte <NAME_EMAIL>."; }
  },
  en: {
    "--accountName---was-added-to-1Password-": function(d) { return "\"" + d.accountName + "\" was added to 1Password."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "\"" + d.itemName + "\" was saved in \"" + d.vaultName + "\"."; },
    "-Internal--Report-feedback": function(d) { return "[Internal] Report feedback"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " is offline."; },
    "-count--Items-archived": function(d) { return d.count + " Items archived"; },
    "-count--Items-deleted": function(d) { return d.count + " Items deleted"; },
    "-count--Items-favorited": function(d) { return d.count + " Items favorited"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " Items removed from favorites"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.deviceName + " near " + d.location + " is trying to sign in to your " + d.accountName + " account."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.deviceName + " near " + d.location + " is trying to sign in to your " + d.accountName + " account. Open 1Password in your browser's toolbar to view."; },
    "-fileName--downloaded": function(d) { return d.fileName + " downloaded"; },
    "-itemType--saved": function(d) { return d.itemType + " saved"; },
    "1Password-8-Required": function(d) { return "1Password 8 Required"; },
    "1Password-account-added": function(d) { return "1Password account added"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password can't open its database because there isn't enough disk space available for " + d.browser + ". To use 1Password, free up some space."; },
    "1Password-is-up-to-date": function(d) { return "1Password is up to date"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password will no longer make autofill suggestions on this page.\n\nThe list of hidden pages is remembered in this web browser. You can clear the list at any time in settings."; },
    "A-database-error-occurred-": function(d) { return "A database error occurred."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "A website has been added to Watchtower in " + d.count + " of your accounts."; },
    "API-Key": function(d) { return "API Key"; },
    "Add-account-": function(d) { return "Add account…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "All of these items will be removed immediately. You can recover items for a limited time on 1Password.com."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "An error has occurred while generating the Masked Email."; },
    "An-error-occurred--Please-try-again-": function(d) { return "An error occurred. Please try again."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "An error occurred while enabling and filling the Masked Email."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "An unexpected error occurred. <NAME_EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "An unknown error has occurred."; },
    "Any-Website": function(d) { return "Any Website"; },
    Archive: function(d) { return "Archive"; },
    "Archive--count--items-": function(d) { return "Archive " + d.count + " items?"; },
    "Archived---title--": function(d) { return "Archived “" + d.title + "”"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "Archived items won’t show up in filling suggestions for websites or in apps."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "Are you sure you want to archive “" + d.itemName + "”?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "Are you sure you want to delete “" + d.itemName + "”?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "Are you sure you want to delete " + plural(d.count, 0, en, { one: number(d.count, "count") + " item", other: number(d.count, "count") + " items" }) + "?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "Are you sure you want to delete this password?"; },
    "Authentication-Required": function(d) { return "Authentication Required"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "Authentication was interrupted or timed out. Please try again."; },
    Benchmarking: function(d) { return "Benchmarking"; },
    Cancel: function(d) { return "Cancel"; },
    "Collect-Page-Structure": function(d) { return "Collect Page Structure"; },
    "Continue-Signing-In": function(d) { return "Continue Signing In"; },
    "Copied--fieldTitle-": function(d) { return "Copied " + d.fieldTitle; },
    "Copied-field": function(d) { return "Copied field"; },
    "Copied-generated-password": function(d) { return "Copied generated password"; },
    "Copied-item-JSON": function(d) { return "Copied item JSON"; },
    "Copied-item-UUID": function(d) { return "Copied item UUID"; },
    "Copied-item-link": function(d) { return "Copied item link"; },
    "Copied-one-time-password": function(d) { return "Copied one-time password"; },
    "Corporate-Card": function(d) { return "Corporate Card"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "Could not connect to the 1Password App. Try unlocking again."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "Couldn't complete authentication. Update 1Password for desktop and try again."; },
    "Create-Brex-vendor-card-": function(d) { return "Create Brex vendor card…"; },
    "Create-Masked-Email-": function(d) { return "Create Masked Email…"; },
    "Create-Privacy-Card-": function(d) { return "Create Privacy Card…"; },
    "Create-SSH-Key-": function(d) { return "Create SSH Key…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "Creating and editing SSH keys requires 1Password 8."; },
    "Credit-Card": function(d) { return "Credit Card"; },
    "Credit-card": function(d) { return "Credit card"; },
    Delete: function(d) { return "Delete"; },
    "Deleted---title--": function(d) { return "Deleted “" + d.title + "”"; },
    "Disable-Extensions": function(d) { return "Disable Extensions"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "Disable other versions of 1Password in the browser?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "Disabled sign in automatically on “" + d.title + "”"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "Disabled sign in automatically on " + d.count + " items"; },
    "Disk-space-too-low": function(d) { return "Disk space too low"; },
    "Do-you-have-an-existing-account-": function(d) { return "Do you have an existing account?"; },
    "Don-t-Save-in-1Password": function(d) { return "Don't Save in 1Password"; },
    "Download-failed": function(d) { return "Download failed"; },
    "Duo-Authentication-Required": function(d) { return "Duo Authentication Required"; },
    "Email-blocked": function(d) { return "Email blocked"; },
    "Email-unblocked": function(d) { return "Email unblocked"; },
    Employee: function(d) { return "Employee"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "Enabled sign in automatically on “" + d.title + "”"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "Enabled sign in automatically on " + d.count + " items"; },
    "Enter-Account-Password": function(d) { return "Enter Account Password"; },
    "Failed-to-copy-field": function(d) { return "Failed to copy field"; },
    "Failed-to-delete-password": function(d) { return "Failed to delete password"; },
    "Favorited---title--": function(d) { return "Favorited “" + d.title + "”"; },
    "Fill-Masked-Email-": function(d) { return "Fill Masked Email…"; },
    "Get-Help": function(d) { return "Get Help"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "Grace period for authentication method migration has expired. Please sign in again."; },
    Help: function(d) { return "Help"; },
    "Hide-on-this-page": function(d) { return "Hide on this page"; },
    Identity: function(d) { return "Identity"; },
    Item: function(d) { return "Item"; },
    "Learn-More-": function(d) { return "Learn More…"; },
    "Link-Existing": function(d) { return "Link Existing"; },
    Lock: function(d) { return "Lock"; },
    Login: function(d) { return "Login"; },
    Managed: function(d) { return "Managed"; },
    "Managed-Application": function(d) { return "Managed Application"; },
    "New-app-or-browser-found": function(d) { return "New app or browser found"; },
    "New-item-saved-in-1Password-": function(d) { return "New item saved in 1Password."; },
    "No-QR-code-found-for-2FA": function(d) { return "No QR code found for 2FA"; },
    "No-accounts-found-": function(d) { return "No accounts found."; },
    OK: function(d) { return "OK"; },
    Ok: function(d) { return "Ok"; },
    Personal: function(d) { return "Personal"; },
    "Physical-Card": function(d) { return "Physical Card"; },
    Private: function(d) { return "Private"; },
    "Removed---title---from-favorites": function(d) { return "Removed “" + d.title + "” from favorites"; },
    "Report-Issue": function(d) { return "Report Issue"; },
    "Report-error-": function(d) { return "Report error…"; },
    Save: function(d) { return "Save"; },
    Security: function(d) { return "Security"; },
    Settings: function(d) { return "Settings"; },
    Shared: function(d) { return "Shared"; },
    "Sign-Up": function(d) { return "Sign Up"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "Sign in to your 1Password account (" + d.accountName + ") and authenticate with Duo to continue."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "Sign in to your 1Password account (" + d.accountName + ") to continue."; },
    "Sign-in-with--authProvider-": function(d) { return "Sign in with " + d.authProvider; },
    "Sign-in-with-passkey": function(d) { return "Sign in with passkey"; },
    "Stay-Offline": function(d) { return "Stay Offline"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "Thank you for running the latest version (" + d.version + ") of 1Password!"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "That didn't work. Check your password and try again."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "This card has been locked/terminated, please use another card."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "This entry will be removed immediately."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "This item will be removed immediately. You can recover items for a limited time on 1Password.com."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "To save an item in this account you need to link it to the previously selected Masked Email account"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "Touch ID sensor is currently unavailable."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "Touch ID sensor is currently unavailable. You need to sign in using your account password."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "Unable to add one-time password field to item"; },
    "Unable-to-copy-generated-password": function(d) { return "Unable to copy generated password"; },
    "Unable-to-fill-generated-password": function(d) { return "Unable to fill generated password"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "Unable to generate and copy one-time password"; },
    "Unable-to-save-generated-password": function(d) { return "Unable to save generated password"; },
    "Unable-to-unlock-account-": function(d) { return "Unable to unlock account."; },
    "Unknown-Device": function(d) { return "Unknown Device"; },
    Unlock: function(d) { return "Unlock"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "Unlock account from the desktop app"; },
    "Use-Suggested-Password": function(d) { return "Use Suggested Password"; },
    "Vendor-Card": function(d) { return "Vendor Card"; },
    "Watchtower-Alert": function(d) { return "Watchtower Alert"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "We have detected other versions of this extension installed. Would you like to disable " + d.extensionNames + "?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "You can choose to link an existing family account or create a new one."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "You’re asked to re-enter your password because " + d.browserName + " has an update available."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "You're offline. To access your account, check your internet connection and try again."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "You've reached the daily limit for Masked Email creation for the selected account."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "You've reached the maximum Masked Email creation quota for the selected account."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "You won't be able to save changes until you are signed in and online."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "You won't be able to save changes until you finish signing in."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "You won't be able to use a generated password until you are signed in and online."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "Your account access has been temporarily paused. Check your email for more details."; },
    "Your-account-is-offline": function(d) { return "Your account is offline"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "Your account is suspended. Contact your family organizer or team administrator for more information."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "Your account is unverified. Sign in to Fastmail to verify the selected account."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "Your session has expired. 1Password in the browser would like you to sign in again to authenticate."; },
    "one-time-password": function(d) { return "one-time password"; },
    "your-browser": function(d) { return "your browser"; }
  },
  es: {
    "--accountName---was-added-to-1Password-": function(d) { return "\"" + d.accountName + "\" fue agregada a 1Password."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "Se guardó \"" + d.itemName + "\" en \"" + d.vaultName + "\"."; },
    "-Internal--Report-feedback": function(d) { return "[Interno] Informe de feedback"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " no tiene conexión."; },
    "-count--Items-archived": function(d) { return d.count + " elementos archivados"; },
    "-count--Items-deleted": function(d) { return d.count + " elementos eliminados"; },
    "-count--Items-favorited": function(d) { return d.count + " elementos favoritos"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " elementos eliminados de favoritos"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.deviceName + " cerca de " + d.location + " está intentando iniciar sesión en tu cuenta " + d.accountName + "."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.deviceName + " cerca de " + d.location + " está intentando iniciar sesión en tu cuenta " + d.accountName + ". Abre 1Password en la barra de herramientas del navegador para verlo."; },
    "-fileName--downloaded": function(d) { return d.fileName + " descargado"; },
    "-itemType--saved": function(d) { return d.itemType + " guardado"; },
    "1Password-8-Required": function(d) { return "Se requiere 1Password 8"; },
    "1Password-account-added": function(d) { return "Cuenta de 1Password agregada"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password no puede abrir su base de datos porque no hay espacio suficiente en el disco para " + d.browser + ". Para usar 1Password, libera espacio."; },
    "1Password-is-up-to-date": function(d) { return "1Password se ha actualizado"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password dejará de hacer sugerencias de autocompletado en esta página.\n\nLa lista de páginas ocultas se recuerda en este navegador web. Puedes borrar la lista en cualquier momento desde ajustes."; },
    "A-database-error-occurred-": function(d) { return "Se ha producido un error de la base de datos."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "Se ha añadido una página web a Watchtower en " + d.count + " de tus cuentas."; },
    "API-Key": function(d) { return "Clave API"; },
    "Add-account-": function(d) { return "Añadir cuenta…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Todos estos elementos se eliminarán inmediatamente. Puedes recuperar elementos durante un tiempo limitado en 1Password.com."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "Se ha producido un error al generar el correo electrónico enmascarado."; },
    "An-error-occurred--Please-try-again-": function(d) { return "Se ha producido un error. Inténtalo de nuevo."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "Se ha producido un error al habilitar y rellenar el correo electrónico enmascarado."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Se ha producido un error inesperado. Ponte en <NAME_EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "Se ha producido un error desconocido."; },
    "Any-Website": function(d) { return "Cualquier sitio web"; },
    Archive: function(d) { return "Archivar"; },
    "Archive--count--items-": function(d) { return "¿Archivar " + d.count + " elementos?"; },
    "Archived---title--": function(d) { return "“" + d.title + "” archivado"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "Los elementos archivados no se mostrarán en sugerencias de cumplimentación para sitios web y aplicaciones."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "¿Seguro que quieres archivar “" + d.itemName + "”?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "¿Seguro de que quieres eliminar “" + d.itemName + "”?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "¿Estás seguro de que quieres eliminar " + plural(d.count, 0, es, { one: number(d.count, "count") + " elemento", other: number(d.count, "count") + " elementos" }) + "?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "¿Está seguro que desea eliminar esta contraseña?"; },
    "Authentication-Required": function(d) { return "Se requiere autenticación"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "La autenticación se ha interrumpido o ha excedido el límite de tiempo. Inténtalo de nuevo."; },
    Benchmarking: function(d) { return "Análisis comparativo"; },
    Cancel: function(d) { return "Cancelar"; },
    "Collect-Page-Structure": function(d) { return "Recopilar la estructura de la página"; },
    "Continue-Signing-In": function(d) { return "Proceder a iniciar sesión"; },
    "Copied--fieldTitle-": function(d) { return d.fieldTitle + " copiado"; },
    "Copied-field": function(d) { return "Campo copiado"; },
    "Copied-generated-password": function(d) { return "Contraseña generada copiada"; },
    "Copied-item-JSON": function(d) { return "Elemento JSON copiado"; },
    "Copied-item-UUID": function(d) { return "UUID de elemento copiado"; },
    "Copied-item-link": function(d) { return "Enlace de elemento copiado"; },
    "Copied-one-time-password": function(d) { return "Contraseña de un solo uso copiada"; },
    "Corporate-Card": function(d) { return "Tarjeta corporativa"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "No se ha podido conectar a la aplicación de 1Password. Intenta desbloquearlo de nuevo."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "No se ha podido completar la autenticación. Actualiza 1Password para escritorio e inténtalo de nuevo."; },
    "Create-Brex-vendor-card-": function(d) { return "Crear tarjeta de proveedor de Brex…"; },
    "Create-Masked-Email-": function(d) { return "Crear correo electrónico enmascarado…"; },
    "Create-Privacy-Card-": function(d) { return "Crear tarjeta de Privacy…"; },
    "Create-SSH-Key-": function(d) { return "Crear clave SSH…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "La creación y edición de claves SSH requiere 1Password 8."; },
    "Credit-Card": function(d) { return "Tarjeta de crédito"; },
    "Credit-card": function(d) { return "Tarjeta de crédito"; },
    Delete: function(d) { return "Eliminar"; },
    "Deleted---title--": function(d) { return "“" + d.title + "” eliminado"; },
    "Disable-Extensions": function(d) { return "Deshabilitar extensiones"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "¿Deshabilitar otras versiones de 1Password en el navegador?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "Inicio de sesión deshabilitado automáticamente en “" + d.title + "”"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "Inicio de sesión deshabilitado automáticamente en " + d.count + " elementos"; },
    "Disk-space-too-low": function(d) { return "Espacio en disco insuficiente"; },
    "Do-you-have-an-existing-account-": function(d) { return "¿Tienes una cuenta existente?"; },
    "Don-t-Save-in-1Password": function(d) { return "No guardar en 1Password"; },
    "Download-failed": function(d) { return "Falló la descarga"; },
    "Duo-Authentication-Required": function(d) { return "Se requiere la autenticación Duo"; },
    "Email-blocked": function(d) { return "Correo electrónico bloqueado"; },
    "Email-unblocked": function(d) { return "Correo electrónico desbloqueado"; },
    Employee: function(d) { return "Empleado"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "Inicio de sesión habilitado automáticamente en “" + d.title + "”"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "Inicio de sesión habilitado automáticamente en " + d.count + " elementos"; },
    "Enter-Account-Password": function(d) { return "Introducir la contraseña de la cuenta"; },
    "Failed-to-copy-field": function(d) { return "Error al copiar el campo"; },
    "Failed-to-delete-password": function(d) { return "Error al eliminar la contraseña"; },
    "Favorited---title--": function(d) { return "“" + d.title + "” añadido a favoritos"; },
    "Fill-Masked-Email-": function(d) { return "Rellenar correo electrónico enmascarado…"; },
    "Get-Help": function(d) { return "Conseguir ayuda"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "El periodo de gracia para la migración del método de autenticación ha vencido. Inicia sesión de nuevo."; },
    Help: function(d) { return "Ayuda"; },
    "Hide-on-this-page": function(d) { return "Ocultar en esta página"; },
    Identity: function(d) { return "Identidad"; },
    Item: function(d) { return "Elemento"; },
    "Learn-More-": function(d) { return "Más información…"; },
    "Link-Existing": function(d) { return "Enlazar existente"; },
    Lock: function(d) { return "Bloquear"; },
    Login: function(d) { return "Inicio de sesión"; },
    Managed: function(d) { return "Gestionado"; },
    "New-app-or-browser-found": function(d) { return "Se ha encontrado una nueva aplicación o navegador"; },
    "New-item-saved-in-1Password-": function(d) { return "Nuevo elemento guardado en 1Password."; },
    "No-QR-code-found-for-2FA": function(d) { return "No se ha encontrado ningún código QR para 2FA"; },
    "No-accounts-found-": function(d) { return "No se ha encontrado ninguna cuenta."; },
    OK: function(d) { return "Aceptar"; },
    Ok: function(d) { return "Ok"; },
    Personal: function(d) { return "Personal"; },
    "Physical-Card": function(d) { return "Tarjeta física"; },
    Private: function(d) { return "Privado"; },
    "Removed---title---from-favorites": function(d) { return "“" + d.title + "” eliminados de favoritos"; },
    "Report-Issue": function(d) { return "Informar del problema"; },
    "Report-error-": function(d) { return "Informar de error…"; },
    Save: function(d) { return "Guardar"; },
    Security: function(d) { return "Seguridad"; },
    Settings: function(d) { return "Ajustes"; },
    Shared: function(d) { return "Compartido"; },
    "Sign-Up": function(d) { return "Crear una cuenta"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "Inicia sesión en tu cuenta de 1Password (" + d.accountName + ") y autentícate con Duo para continuar."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "Inicia sesión en tu cuenta de 1Password (" + d.accountName + ") para continuar."; },
    "Sign-in-with--authProvider-": function(d) { return "Iniciar sesión con " + d.authProvider; },
    "Sign-in-with-passkey": function(d) { return "Iniciar sesión con clave"; },
    "Stay-Offline": function(d) { return "Permanecer fuera de línea"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "¡Gracias por usar la última versión (" + d.version + ") de 1Password!"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "No ha funcionado. Comprueba tu contraseña e inténtalo nuevamente."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "Esta tarjeta se ha bloqueado o cancelado, utilice otra tarjeta."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "Esta entrada se eliminará de inmediato."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Este elemento se eliminará inmediatamente. Puedes recuperar elementos durante un tiempo limitado en 1Password.com."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "Para guardar un elemento en este cuenta necesitas vincularlo a la cuenta de correo electrónico enmascarado previamente seleccionada"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "El sensor de Touch ID no está disponible actualmente."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "El sensor de Touch ID no está disponible en estos momentos. Tendrás que iniciar sesión con la contraseña de tu cuenta."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "No se ha podido añadir el campo de contraseña de un solo uso al elemento"; },
    "Unable-to-copy-generated-password": function(d) { return "No se ha podido copiar la contraseña generada"; },
    "Unable-to-fill-generated-password": function(d) { return "No se ha podido rellenar la contraseña generada"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "No se ha podido generar ni copiar la contraseña de un solo uso"; },
    "Unable-to-save-generated-password": function(d) { return "No se ha podido guardar la contraseña generada"; },
    "Unable-to-unlock-account-": function(d) { return "No se puede desbloquear la cuenta."; },
    "Unknown-Device": function(d) { return "Dispositivo desconocido"; },
    Unlock: function(d) { return "Desbloquear"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "Desbloquear cuenta desde la aplicación de escritorio"; },
    "Use-Suggested-Password": function(d) { return "Usar contraseña sugerida"; },
    "Vendor-Card": function(d) { return "Tarjeta de proveedor"; },
    "Watchtower-Alert": function(d) { return "Alerta de Watchtower"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "Hemos detectado otras versiones de esta extensión instaladas. ¿Te gustaría deshabilitar " + d.extensionNames + "?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "Puedes elegir vincular una cuenta familiar existente o crear una nueva."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "Te pedimos que vuelvas a introducir tu contraseña porque " + d.browserName + " tiene una actualización disponible."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "Estás fuera de línea. Para acceder a tu cuenta, comprueba tu conexión a internet y vuelve a intentarlo."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "Has alcanzado el límite diario de creación de correos electrónicos enmascarados para la cuenta seleccionada."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "Has alcanzado el límite máximo de creación de correos electrónicos enmascarados para la cuenta seleccionada."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "No podrás guardar los cambios hasta que inicies sesión y estés en línea."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "No podrás guardar cambios hasta que termines de iniciar sesión."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "Solo podrás usar una contraseña generada si inicias sesión y estás en línea."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "Tu acceso de cuenta se ha pausado temporalmente. Comprueba tu correo electrónico para más información."; },
    "Your-account-is-offline": function(d) { return "Tu cuenta está fuera de línea"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "Tu cuenta se ha suspendido. Ponte en contacto con el organizador de tu familia o el administrador del equipo para más información."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "Tu cuenta no se ha verificado. Inicia sesión en Fastmail para verificar la cuenta seleccionada."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "Tu sesión ha caducado. 1Password en el navegador quiere que vuelvas a iniciar sesión para autenticarte."; },
    "one-time-password": function(d) { return "contraseña de un solo uso"; },
    "your-browser": function(d) { return "tu navegador"; },
    "unit.B": function(d) { return d.size + " bytes"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "Inicio de sesión"; },
    "category.002": function(d) { return "Tarjeta de crédito"; },
    "category.003": function(d) { return "Nota segura"; },
    "category.004": function(d) { return "Identidad"; },
    "category.005": function(d) { return "Contraseña"; },
    "category.006": function(d) { return "Documento"; },
    "category.100": function(d) { return "Licencia de software"; },
    "category.101": function(d) { return "Cuenta bancaria"; },
    "category.102": function(d) { return "Base de datos"; },
    "category.103": function(d) { return "Licencia de conducir"; },
    "category.104": function(d) { return "Licencia de exteriores"; },
    "category.105": function(d) { return "Membresía"; },
    "category.106": function(d) { return "Pasaporte"; },
    "category.107": function(d) { return "Programa de beneficios"; },
    "category.108": function(d) { return "Número de la Seguridad Social"; },
    "category.109": function(d) { return "Red WiFi"; },
    "category.110": function(d) { return "Servidor"; },
    "category.111": function(d) { return "Cuenta de correo electrónico"; },
    "categories.001": function(d) { return "Inicios de sesión"; },
    "categories.002": function(d) { return "Tarjetas de crédito"; },
    "categories.003": function(d) { return "Notas seguras"; },
    "categories.004": function(d) { return "Identidades"; },
    "categories.005": function(d) { return "Contraseñas"; },
    "categories.006": function(d) { return "Documentos"; },
    "categories.100": function(d) { return "Licencias de software"; },
    "categories.101": function(d) { return "Cuentas bancarias"; },
    "categories.102": function(d) { return "Bases de datos"; },
    "categories.103": function(d) { return "Licencias de conducir"; },
    "categories.104": function(d) { return "Licencias de exteroriores"; },
    "categories.105": function(d) { return "Membresías"; },
    "categories.106": function(d) { return "Pasaportes"; },
    "categories.107": function(d) { return "Programas de beneficios"; },
    "categories.108": function(d) { return "Números de la Seguridad Social"; },
    "categories.109": function(d) { return "Redes WiFi"; },
    "categories.110": function(d) { return "Servidores"; },
    "categories.111": function(d) { return "Cuentas de correo electrónico"; },
    "button.save": function(d) { return "Guardar"; },
    "button.cancel": function(d) { return "Cancelar"; },
    "unexpected-error": function(d) { return "Se ha producido un error inesperado. Ponte en <NAME_EMAIL>"; }
  },
  fr: {
    "--accountName---was-added-to-1Password-": function(d) { return "« " + d.accountName + " » a été ajouté à 1Password."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "« " + d.itemName + " » a été enregistré dans « " + d.vaultName + " »."; },
    "-Internal--Report-feedback": function(d) { return "[Interne] Signaler un retour d'informations"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " est hors ligne."; },
    "-count--Items-archived": function(d) { return d.count + " éléments archivés"; },
    "-count--Items-deleted": function(d) { return d.count + " éléments supprimés"; },
    "-count--Items-favorited": function(d) { return d.count + " éléments mis en favori"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " éléments supprimés des favoris"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.deviceName + " près de " + d.location + " tente de se connecter à votre compte " + d.accountName + "."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.deviceName + " près de " + d.location + " tente de se connecter à votre compte " + d.accountName + ". Ouvrez 1Password dans la barre d'outils de votre navigateur pour l'afficher."; },
    "-fileName--downloaded": function(d) { return d.fileName + " téléchargé"; },
    "-itemType--saved": function(d) { return d.itemType + " enregistré"; },
    "1Password-8-Required": function(d) { return "1Password 8 requis"; },
    "1Password-account-added": function(d) { return "Compte 1Password ajouté"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password n'arrive pas à ouvrir sa base de données car il n'y a pas suffisament d'espace de disponible sur le disque pour " + d.browser + ". Libérez de l'espace pour utiliser 1Password."; },
    "1Password-is-up-to-date": function(d) { return "1Password est à jour"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password ne fera plus de suggestions de remplissage automatique sur cette page.\n\nLa liste des pages masquées est mémorisée dans ce navigateur web. Vous pouvez effacer cette liste à tout moment dans les paramètres."; },
    "A-database-error-occurred-": function(d) { return "Une erreur de base de données s'est produite."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "Un site web a été ajouté à Watchtower dans " + d.count + " de vos comptes."; },
    "API-Key": function(d) { return "Clé API"; },
    "Add-account-": function(d) { return "Ajouter un compte…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Tous ces éléments seront supprimés immédiatement. Vous pouvez récupérer les éléments pour une durée limitée sur 1Password.com."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "Une erreur s'est produite lors de la génération de l'e-mail masqué."; },
    "An-error-occurred--Please-try-again-": function(d) { return "Une erreur s’est produite. Veuillez réessayer."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "Une erreur s'est produite lors de l'activation et du remplissage de l'e-mail masqué."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Une erreur inattendue s'est produite. <NAME_EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "Une erreur inconnue s’est produite."; },
    "Any-Website": function(d) { return "Tout site web"; },
    Archive: function(d) { return "Archive"; },
    "Archive--count--items-": function(d) { return "Archiver " + d.count + " éléments ?"; },
    "Archived---title--": function(d) { return "« " + d.title + " » archivé"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "Les éléments archivés n'apparaîtront pas dans les suggestions de remplissage des sites Web ou des applications."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "Êtes-vous sûr de vouloir archiver « " + d.itemName + " » ?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "Êtes-vous certain de vouloir supprimer « " + d.itemName + " » ?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "Êtes-vous sûr de vouloir supprimer " + plural(d.count, 0, fr, { one: number(d.count, "count") + " élément", other: number(d.count, "count") + " éléments" }) + " ?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "Êtes-vous sûr de vouloir supprimer ce mot de passe?"; },
    "Authentication-Required": function(d) { return "Authentification requise"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "L'authentification a été interrompue ou a dépassé le temps imparti. Veuillez réessayer."; },
    Benchmarking: function(d) { return "Banc d'essai"; },
    Cancel: function(d) { return "Annuler"; },
    "Collect-Page-Structure": function(d) { return "Collecter la structure de la page"; },
    "Continue-Signing-In": function(d) { return "Continuer à se connecter"; },
    "Copied--fieldTitle-": function(d) { return d.fieldTitle + " copié"; },
    "Copied-field": function(d) { return "Champ copié"; },
    "Copied-generated-password": function(d) { return "Mot de passe généré copié"; },
    "Copied-item-JSON": function(d) { return "Élément JSON copié"; },
    "Copied-item-UUID": function(d) { return "UUID de l'élément copié"; },
    "Copied-item-link": function(d) { return "Lien de l'élément copié"; },
    "Copied-one-time-password": function(d) { return "Mot de passe à usage unique copié"; },
    "Corporate-Card": function(d) { return "Carte d'entreprise"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "Impossible de se connecter à l'application 1Password. Essayez de déverrouiller à nouveau."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "L'authentification n'a pas pu être effectuée. Mettez à jour 1Password pour ordinateur et réessayez."; },
    "Create-Brex-vendor-card-": function(d) { return "Créer une carte de vendeur Brex…"; },
    "Create-Masked-Email-": function(d) { return "Créer un e-mail masqué…"; },
    "Create-Privacy-Card-": function(d) { return "Créer une carte Privacy…"; },
    "Create-SSH-Key-": function(d) { return "Créer une clé SSH…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "La création et la modification des clés SSH nécessitent 1Password 8."; },
    "Credit-Card": function(d) { return "Carte de crédit"; },
    "Credit-card": function(d) { return "Carte de crédit"; },
    Delete: function(d) { return "Supprimer"; },
    "Deleted---title--": function(d) { return "« " + d.title + " » supprimé"; },
    "Disable-Extensions": function(d) { return "Désactiver les extensions"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "Désactiver les autres versions de 1Password dans le navigateur ?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "Désactivation de la connexion automatique sur « " + d.title + " »"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "Désactivation de la connexion automatique sur " + d.count + " éléments"; },
    "Disk-space-too-low": function(d) { return "Espace disque insuffisant"; },
    "Do-you-have-an-existing-account-": function(d) { return "Avez-vous un compte existant ?"; },
    "Don-t-Save-in-1Password": function(d) { return "Ne pas enregistrer dans 1Password"; },
    "Download-failed": function(d) { return "Échec du téléchargement"; },
    "Duo-Authentication-Required": function(d) { return "Authentification Duo requise"; },
    "Email-blocked": function(d) { return "E-mail bloqué"; },
    "Email-unblocked": function(d) { return "E-mail débloqué"; },
    Employee: function(d) { return "Employé"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "Activation de la connexion automatique sur « " + d.title + " »"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "Activation de la connexion automatique sur " + d.count + " éléments"; },
    "Enter-Account-Password": function(d) { return "Saisir le mot de passe du compte"; },
    "Failed-to-copy-field": function(d) { return "Échec de la copie du champ"; },
    "Failed-to-delete-password": function(d) { return "Impossible de supprimer le mot de passe"; },
    "Favorited---title--": function(d) { return "« " + d.title + " » mis en favori"; },
    "Fill-Masked-Email-": function(d) { return "Remplir l'e-mail masqué…"; },
    "Get-Help": function(d) { return "Obtenir de l'aide"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "Le délai de grâce pour la migration de la méthode d'authentification a expiré. Veuillez vous reconnecter."; },
    Help: function(d) { return "Aide"; },
    "Hide-on-this-page": function(d) { return "Masquer sur cette page"; },
    Identity: function(d) { return "Identité"; },
    Item: function(d) { return "Élément"; },
    "Learn-More-": function(d) { return "En savoir plus…"; },
    "Link-Existing": function(d) { return "Lier au fichier existant"; },
    Lock: function(d) { return "Verrouiller"; },
    Login: function(d) { return "Connexion"; },
    Managed: function(d) { return "Géré"; },
    "New-app-or-browser-found": function(d) { return "Nouvelle application ou nouveau navigateur trouvé"; },
    "New-item-saved-in-1Password-": function(d) { return "Nouvel élément enregistré dans 1Password."; },
    "No-QR-code-found-for-2FA": function(d) { return "Aucun code QR trouvé pour l'authentification à deux facteurs"; },
    "No-accounts-found-": function(d) { return "Aucun compte trouvé."; },
    OK: function(d) { return "OK"; },
    Ok: function(d) { return "OK"; },
    Personal: function(d) { return "Personnel"; },
    "Physical-Card": function(d) { return "Carte physique"; },
    Private: function(d) { return "Privé"; },
    "Removed---title---from-favorites": function(d) { return "« " + d.title + " » supprimé des favoris"; },
    "Report-Issue": function(d) { return "Signaler un problème"; },
    "Report-error-": function(d) { return "Rapport d'erreur…"; },
    Save: function(d) { return "Enregistrer"; },
    Security: function(d) { return "Sécurité"; },
    Settings: function(d) { return "Paramètres"; },
    Shared: function(d) { return "Partagé"; },
    "Sign-Up": function(d) { return "S'inscrire"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "Connectez-vous à votre compte 1Password " + d.accountName + " et authentifiez-vous avec Duo pour continuer."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "Connectez-vous à votre compte 1Password (" + d.accountName + ") pour continuer."; },
    "Sign-in-with--authProvider-": function(d) { return "Se connecter avec " + d.authProvider; },
    "Sign-in-with-passkey": function(d) { return "Se connecter avec la clé d'accès"; },
    "Stay-Offline": function(d) { return "Rester hors ligne"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "Merci d'utiliser la dernière version (" + d.version + ") de 1Password !"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "Cette action n’a pas fonctionné. Vérifiez votre mot de passe et réessayez."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "Cette carte a été verrouillée ou est expirée, veuillez utiliser une autre carte."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "Cette entrée sera supprimée immédiatement."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Cet élément sera supprimé immédiatement. Vous pouvez récupérer les éléments pour une durée limitée sur 1Password.com."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "Pour enregistrer un élément dans ce compte, vous devez l'associer au compte de l'e-mail masqué sélectionné précédemment"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "Le capteur Touch ID est actuellement indisponible."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "Le capteur Touch ID est actuellement indisponible. Vous devez vous connecter en utilisant le mot de passe de votre compte."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "Impossible d'ajouter un champ de mot de passe à usage unique à l'élément"; },
    "Unable-to-copy-generated-password": function(d) { return "Impossible de copier le mot de passe généré"; },
    "Unable-to-fill-generated-password": function(d) { return "Impossible de remplir le mot de passe généré"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "Impossible de générer et de copier le mot de passe à usage unique"; },
    "Unable-to-save-generated-password": function(d) { return "Impossible d'enregistrer le mot de passe généré"; },
    "Unable-to-unlock-account-": function(d) { return "Impossible de déverrouiller le compte."; },
    "Unknown-Device": function(d) { return "Appareil inconnu"; },
    Unlock: function(d) { return "Déverrouiller"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "Déverrouiller le compte à partir de l'application de bureau"; },
    "Use-Suggested-Password": function(d) { return "Utiliser le mot de passe suggéré"; },
    "Vendor-Card": function(d) { return "Carte de vendeur"; },
    "Watchtower-Alert": function(d) { return "Alerte Watchtower"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "Nous avons détecté d'autres versions de cette extension. Voulez-vous désactiver " + d.extensionNames + " ?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "Vous pouvez choisir d'associer un compte famille existant ou d'en créer un nouveau."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "Vous devez ressaisir votre mot de passe car une mise à jour est disponible pour " + d.browserName + "."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "Vous êtes hors ligne. Pour accéder à votre compte, vérifiez votre connexion Internet et réessayez."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "Vous avez atteint la limite quotidienne de création d'e-mail masqué pour le compte sélectionné."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "Vous avez atteint le quota maximum de création d'e-mail masqué pour le compte sélectionné."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "Vous ne pourrez pas enregistrer les modifications tant que vous ne serez pas connecté et en ligne."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "Vous ne pourrez pas enregistrer les modifications tant que vous n'aurez pas fini de vous connecter."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "Vous ne pourrez pas utiliser le mot de passe généré tant que vous n'êtes pas connecté et en ligne."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "L'accès à votre compte a été temporairement suspendu. Veuillez consulter votre boîte de réception pour plus de détails."; },
    "Your-account-is-offline": function(d) { return "Votre compte est hors ligne"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "Votre compte est suspendu. Contactez votre organisateur de famille ou administrateur de groupe de travail pour plus d'informations."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "Votre compte n'est pas vérifié, connectez-vous à Fastmail pour vérifier le compte sélectionné."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "Votre session a expiré. 1Password dans le navigateur vous demande de vous connecter à nouveau pour vous authentifier."; },
    "one-time-password": function(d) { return "mot de passe à usage unique"; },
    "your-browser": function(d) { return "votre navigateur"; },
    "unit.B": function(d) { return d.size + " octets"; },
    "unit.KB": function(d) { return d.size + " ko"; },
    "unit.MB": function(d) { return d.size + " Mo"; },
    "unit.GB": function(d) { return d.size + " Go"; },
    "unit.TB": function(d) { return d.size + " To"; },
    "unit.PB": function(d) { return d.size + " Po"; },
    "category.001": function(d) { return "Connexion"; },
    "category.002": function(d) { return "Carte de crédit"; },
    "category.003": function(d) { return "Note sécurisée"; },
    "category.004": function(d) { return "Identité"; },
    "category.005": function(d) { return "Mot de passe"; },
    "category.006": function(d) { return "Document"; },
    "category.100": function(d) { return "Licence de logiciel"; },
    "category.101": function(d) { return "Compte bancaire"; },
    "category.102": function(d) { return "Base de données"; },
    "category.103": function(d) { return "Permis de conduire"; },
    "category.104": function(d) { return "Licence extérieure"; },
    "category.105": function(d) { return "Abonnement"; },
    "category.106": function(d) { return "Passeport"; },
    "category.107": function(d) { return "Carte de fidélité"; },
    "category.108": function(d) { return "Numéro de sécurité sociale"; },
    "category.109": function(d) { return "Réseau WiFi"; },
    "category.110": function(d) { return "Serveur"; },
    "category.111": function(d) { return "Comptes de messagerie"; },
    "categories.001": function(d) { return "Identifiants"; },
    "categories.002": function(d) { return "Cartes de crédit"; },
    "categories.003": function(d) { return "Notes sécurisées"; },
    "categories.004": function(d) { return "Identités"; },
    "categories.005": function(d) { return "Mots de passe"; },
    "categories.006": function(d) { return "Documents"; },
    "categories.100": function(d) { return "Licences de logiciel"; },
    "categories.101": function(d) { return "Comptes bancaires"; },
    "categories.102": function(d) { return "Bases de données"; },
    "categories.103": function(d) { return "Permis de conduire"; },
    "categories.104": function(d) { return "Licences extérieures"; },
    "categories.105": function(d) { return "Abonnements"; },
    "categories.106": function(d) { return "Passeports"; },
    "categories.107": function(d) { return "Cartes de fidélité"; },
    "categories.108": function(d) { return "N° de sécurité sociale"; },
    "categories.109": function(d) { return "Réseaux WiFi"; },
    "categories.110": function(d) { return "Serveurs"; },
    "categories.111": function(d) { return "Comptes de messagerie"; },
    "button.save": function(d) { return "Enregistrer"; },
    "button.cancel": function(d) { return "Annuler"; },
    "unexpected-error": function(d) { return "Une erreur inattendue s'est produite. <NAME_EMAIL>"; }
  },
  it: {
    "--accountName---was-added-to-1Password-": function(d) { return "\"" + d.accountName + "\" è stato aggiunto a 1Password."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "\"" + d.itemName + "\" è stato salvato in \"" + d.vaultName + "\"."; },
    "-Internal--Report-feedback": function(d) { return "[Interno] Segnala un feedback"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " è offline."; },
    "-count--Items-archived": function(d) { return d.count + " elementi archiviati"; },
    "-count--Items-deleted": function(d) { return d.count + " elementi eliminati"; },
    "-count--Items-favorited": function(d) { return d.count + " elementi preferiti"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " elementi rimossi dai preferiti"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.deviceName + " nei pressi di " + d.location + " sta tentando di accedere al tuo account " + d.accountName + "."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.deviceName + " nei pressi di " + d.location + " sta tentando di accedere al tuo account " + d.accountName + ". Apri 1Password nella barra degli strumenti del tuo browser per visualizzare."; },
    "-fileName--downloaded": function(d) { return d.fileName + " scaricato"; },
    "-itemType--saved": function(d) { return d.itemType + " salvato"; },
    "1Password-8-Required": function(d) { return "1Password 8 richiesto"; },
    "1Password-account-added": function(d) { return "Account 1Password aggiunto"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password non è in grado di aprire il database perché non c'è abbastanza spazio su disco disponibile per " + d.browser + ". Libera un po' di spazio per utilizzare 1Password."; },
    "1Password-is-up-to-date": function(d) { return "1Password è aggiornato"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password non proporrà più suggerimenti di riempimento automatico su questa pagina.\n\nL’elenco delle pagine nascoste è memorizzato in questo browser web. Puoi cancellarlo in qualsiasi momento dalle impostazioni."; },
    "A-database-error-occurred-": function(d) { return "Si è verificato un errore del database."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "Un sito web è stato aggiunto a Watchtower in " + d.count + " dei tuoi account."; },
    "API-Key": function(d) { return "Chiave API"; },
    "Add-account-": function(d) { return "Aggiungi account…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Tutti questi elementi saranno immediatamente rimossi. Puoi recuperarli per un periodo di tempo limitato su 1Password.com."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "Si è verificato un errore durante la generazione dell'email mascherata."; },
    "An-error-occurred--Please-try-again-": function(d) { return "Si è verificato un errore. Riprova."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "Si è verificato un errore durante l'abilitazione e la compilazione dell'email mascherata."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Si è verificato un errore imprevisto. Contatta <EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "Si è verificato un errore sconosciuto."; },
    "Any-Website": function(d) { return "Qualsiasi sito web"; },
    Archive: function(d) { return "Archivio"; },
    "Archive--count--items-": function(d) { return "Archiviare " + d.count + " elementi?"; },
    "Archived---title--": function(d) { return "“" + d.title + "” archiviato"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "Gli elementi archiviati non vengono visualizzati nei suggerimenti di compilazione per i siti web o nelle app."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "Sei sicuro di voler archiviare “" + d.itemName + "”?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "Sei sicuro di voler eliminare “" + d.itemName + "”?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "Sei sicuro di voler eliminare " + plural(d.count, 0, it, { one: number(d.count, "count") + " elemento", other: number(d.count, "count") + " elementi" }) + "?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "Sei sicuro di voler eliminare questa password?"; },
    "Authentication-Required": function(d) { return "Autenticazione richiesta"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "L'autenticazione è stata interrotta o ha superato il limite di tempo. Riprova."; },
    Benchmarking: function(d) { return "Analisi comparativa"; },
    Cancel: function(d) { return "Annulla"; },
    "Collect-Page-Structure": function(d) { return "Raccogli la struttura della pagina"; },
    "Continue-Signing-In": function(d) { return "Procedi con l'accesso"; },
    "Copied--fieldTitle-": function(d) { return d.fieldTitle + " copiato"; },
    "Copied-field": function(d) { return "Campo copiato"; },
    "Copied-generated-password": function(d) { return "Password generata copiata"; },
    "Copied-item-JSON": function(d) { return "Elemento JSON copiato"; },
    "Copied-item-UUID": function(d) { return "Elemento UUID copiato"; },
    "Copied-item-link": function(d) { return "Link dell'elemento copiato"; },
    "Copied-one-time-password": function(d) { return "Password monouso copiata"; },
    "Corporate-Card": function(d) { return "Carta di credito aziendale"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "Impossibile effettuare la connessione all'app 1Password. Prova a sbloccare di nuovo."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "Impossibile completare l'autenticazione. Aggiorna 1Password per desktop e riprova."; },
    "Create-Brex-vendor-card-": function(d) { return "Crea carta venditore Brex…"; },
    "Create-Masked-Email-": function(d) { return "Crea email mascherata…"; },
    "Create-Privacy-Card-": function(d) { return "Crea carta Privacy…"; },
    "Create-SSH-Key-": function(d) { return "Crea chiave SSH…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "La creazione e la modifica di chiavi SSH richiede 1Password 8."; },
    "Credit-Card": function(d) { return "Carta di credito"; },
    "Credit-card": function(d) { return "Carta di credito"; },
    Delete: function(d) { return "Elimina"; },
    "Deleted---title--": function(d) { return "“" + d.title + "” eliminato"; },
    "Disable-Extensions": function(d) { return "Disattiva estensioni"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "Disattivare le altre versioni di 1Password nel browser?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "Disabilita l’accesso automatico a “" + d.title + "”"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "Disabilita l’accesso automatico a “" + d.count + "” elementi"; },
    "Disk-space-too-low": function(d) { return "Spazio su disco insufficiente"; },
    "Do-you-have-an-existing-account-": function(d) { return "Hai già un account?"; },
    "Don-t-Save-in-1Password": function(d) { return "Non salvare in 1Password"; },
    "Download-failed": function(d) { return "Download non riuscito"; },
    "Duo-Authentication-Required": function(d) { return "Autenticazione Duo necessaria"; },
    "Email-blocked": function(d) { return "Email bloccata"; },
    "Email-unblocked": function(d) { return "Email sbloccata"; },
    Employee: function(d) { return "Dipendente"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "Abilita l’accesso automatico a “" + d.title + "”"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "Abilita l’accesso automatico a “" + d.count + "” elementi"; },
    "Enter-Account-Password": function(d) { return "Inserisci la password dell’account"; },
    "Failed-to-copy-field": function(d) { return "Impossibile copiare il campo"; },
    "Failed-to-delete-password": function(d) { return "Impossibile eliminare la password"; },
    "Favorited---title--": function(d) { return "“" + d.title + "” aggiunto ai preferiti"; },
    "Fill-Masked-Email-": function(d) { return "Compila email mascherata…"; },
    "Get-Help": function(d) { return "Chiedi aiuto"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "Il periodo di tolleranza per la migrazione del metodo di autenticazione è scaduto. Effettua nuovamente l’accesso."; },
    Help: function(d) { return "Aiuto"; },
    "Hide-on-this-page": function(d) { return "Nascondi su questa pagina"; },
    Identity: function(d) { return "Identità"; },
    Item: function(d) { return "Elemento"; },
    "Learn-More-": function(d) { return "Ulteriori informazioni…"; },
    "Link-Existing": function(d) { return "Collega Esistente"; },
    Lock: function(d) { return "Blocca"; },
    Login: function(d) { return "Accesso"; },
    Managed: function(d) { return "Gestito"; },
    "New-app-or-browser-found": function(d) { return "Nuova app o browser trovati"; },
    "New-item-saved-in-1Password-": function(d) { return "Nuovo elemento salvato in 1Password."; },
    "No-QR-code-found-for-2FA": function(d) { return "Nessun codice QR trovato per 2FA"; },
    "No-accounts-found-": function(d) { return "Nessun account trovato."; },
    OK: function(d) { return "OK"; },
    Ok: function(d) { return "Ok"; },
    Personal: function(d) { return "Personale"; },
    "Physical-Card": function(d) { return "Carta fisica"; },
    Private: function(d) { return "Privato"; },
    "Removed---title---from-favorites": function(d) { return "“" + d.title + "” rimosso dai preferiti"; },
    "Report-Issue": function(d) { return "Segnala il problema"; },
    "Report-error-": function(d) { return "Segnala errore…"; },
    Save: function(d) { return "Salva"; },
    Security: function(d) { return "Sicurezza"; },
    Settings: function(d) { return "Impostazioni"; },
    Shared: function(d) { return "Condiviso"; },
    "Sign-Up": function(d) { return "Registrati"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "Accedi al tuo account 1Password (" + d.accountName + ") e autenticati con Duo per continuare."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "Accedi al tuo account 1Password (" + d.accountName + ") per continuare."; },
    "Sign-in-with--authProvider-": function(d) { return "Accedi con " + d.authProvider; },
    "Sign-in-with-passkey": function(d) { return "Accedi con la chiave"; },
    "Stay-Offline": function(d) { return "Rimani offline"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "Grazie per aver utilizzato l'ultima versione (" + d.version + ") di 1Password!"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "Qualcosa non ha funzionato. Controlla la tua password e riprova."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "Questa carta è stata bloccata/cancellata, utilizza un'altra carta."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "Questa voce verrà rimossa immediatamente."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Questo elemento sarà immediatamente rimosso. Puoi recuperare gli elementi per un periodo di tempo limitato su 1Password.com."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "Per salvare un elemento in questo account, è necessario collegarlo all'account dell'email mascherata selezionato in precedenza"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "Il sensore Touch ID non è al momento disponibile."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "Il sensore Touch ID non è al momento disponibile. È necessario accedere utilizzando la password del tuo account."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "Impossibile aggiungere il campo della password monouso all'elemento"; },
    "Unable-to-copy-generated-password": function(d) { return "Impossibile copiare la password generata"; },
    "Unable-to-fill-generated-password": function(d) { return "Impossibile inserire la password generata"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "Impossibile generare e copiare la password monouso"; },
    "Unable-to-save-generated-password": function(d) { return "Impossibile salvare la password generata"; },
    "Unable-to-unlock-account-": function(d) { return "Impossibile sbloccare l'account."; },
    "Unknown-Device": function(d) { return "Dispositivo sconosciuto"; },
    Unlock: function(d) { return "Sblocca"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "Sblocca l'account dall'app desktop"; },
    "Use-Suggested-Password": function(d) { return "Usa password suggerita"; },
    "Vendor-Card": function(d) { return "Carta del venditore"; },
    "Watchtower-Alert": function(d) { return "Avviso Watchtower"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "Abbiamo rilevato altre versioni di questa estensione installate. Desideri disattivare " + d.extensionNames + "?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "Puoi scegliere di collegare un account familiare esistente o crearne uno nuovo."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "Ti viene chiesto di inserire nuovamente la password perché " + d.browserName + " ha un aggiornamento disponibile."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "Sei offline. Per accedere al tuo account, controlla la tua connessione internet e riprova."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "Hai raggiunto il limite giornaliero per la creazione di email mascherate per l'account selezionato."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "Hai raggiunto la quota massima per la creazione di email mascherate per l'account selezionato."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "Non sarai in grado di salvare le modifiche fino a quando non avrai effettuato l'accesso e sarai online."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "Non sarai in grado di salvare le modifiche finché non avrai completato l'accesso."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "Non potrai usare una password generata finché non avrai eseguito l’accesso e sarai online."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "L’accesso al tuo account è stato temporaneamente sospeso. Controlla la tua email per maggiori dettagli."; },
    "Your-account-is-offline": function(d) { return "Il tuo account è offline"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "Il tuo account è sospeso. Contatta l'organizzatore della famiglia o l'amministratore del team per ulteriori informazioni."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "Il tuo account non è verificato. Accedi a Fastmail per verificare l'account selezionato."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "La tua sessione è scaduta. Per autenticarti a 1Password nel browser, devi accedere di nuovo."; },
    "one-time-password": function(d) { return "password monouso"; },
    "your-browser": function(d) { return "il tuo browser"; },
    "unit.B": function(d) { return d.size + " byte"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "Login"; },
    "category.002": function(d) { return "Carta di credito"; },
    "category.003": function(d) { return "Nota sicura"; },
    "category.004": function(d) { return "Identità"; },
    "category.005": function(d) { return "Password"; },
    "category.006": function(d) { return "Documento"; },
    "category.100": function(d) { return "Licenza software"; },
    "category.101": function(d) { return "Conto bancario"; },
    "category.102": function(d) { return "Database"; },
    "category.103": function(d) { return "Patente di guida"; },
    "category.104": function(d) { return "Licenza outdoor"; },
    "category.105": function(d) { return "Iscrizione"; },
    "category.106": function(d) { return "Passaporto"; },
    "category.107": function(d) { return "Programma a premi"; },
    "category.108": function(d) { return "Codice fiscale"; },
    "category.109": function(d) { return "Rete WiFi"; },
    "category.110": function(d) { return "Server"; },
    "category.111": function(d) { return "Account email"; },
    "categories.001": function(d) { return "Login"; },
    "categories.002": function(d) { return "Carte di credito"; },
    "categories.003": function(d) { return "Note sicure"; },
    "categories.004": function(d) { return "Identità"; },
    "categories.005": function(d) { return "Password"; },
    "categories.006": function(d) { return "Documenti"; },
    "categories.100": function(d) { return "Licenze software"; },
    "categories.101": function(d) { return "Conti bancari"; },
    "categories.102": function(d) { return "Database"; },
    "categories.103": function(d) { return "Patenti di guida"; },
    "categories.104": function(d) { return "Licenze outdoor"; },
    "categories.105": function(d) { return "Iscrizioni"; },
    "categories.106": function(d) { return "Passaporti"; },
    "categories.107": function(d) { return "Programmi a premi"; },
    "categories.108": function(d) { return "Codici fiscali"; },
    "categories.109": function(d) { return "Reti WiFi"; },
    "categories.110": function(d) { return "Server"; },
    "categories.111": function(d) { return "Account email"; },
    "button.save": function(d) { return "Salva"; },
    "button.cancel": function(d) { return "Annulla"; },
    "unexpected-error": function(d) { return "Si è verificato un errore imprevisto. Contatta <EMAIL>"; }
  },
  ja: {
    "--accountName---was-added-to-1Password-": function(d) { return "「" + d.accountName + "」が1Passwordに追加されました。"; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "「" + d.itemName + "」は「" + d.vaultName + "」に保存されました。"; },
    "-Internal--Report-feedback": function(d) { return "［内部］フィードバックを報告する"; },
    "-accountName--is-offline-": function(d) { return d.accountName + "はオフラインです。"; },
    "-count--Items-archived": function(d) { return d.count + "個のアイテムがアーカイブされました"; },
    "-count--Items-deleted": function(d) { return d.count + "個のアイテムが削除されました"; },
    "-count--Items-favorited": function(d) { return d.count + "個のアイテムがお気に入りに登録されました"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + "個のアイテムがお気に入りから削除されました"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.location + "の近くの" + d.deviceName + "があなたの" + d.accountName + "アカウントにサインインしようとしています。"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.location + "の近くの" + d.deviceName + "があなたの" + d.accountName + "アカウントにサインインしようとしています。ブラウザのツールバーで1Passwordを開いて表示します。"; },
    "-fileName--downloaded": function(d) { return d.fileName + "がダウンロードされました"; },
    "-itemType--saved": function(d) { return d.itemType + " が保存されました"; },
    "1Password-8-Required": function(d) { return "1Password 8必須"; },
    "1Password-account-added": function(d) { return "1Passwordアカウントが追加されました"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password がデータベースを開けません。" + d.browser + " に十分な空きディスク容量がありません。1Password を使用するには空き容量を増やしてください。"; },
    "1Password-is-up-to-date": function(d) { return "1Password は最新の状態です"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Passwordはこのページで自動入力候補を表示しなくなります。\n\n非表示ページのリストはこのウェブブラウザに記憶されます。設定でいつでもリストを消去できます。"; },
    "A-database-error-occurred-": function(d) { return "データベースエラーが発生しました。"; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "あなたの" + d.count + "つのアカウントのWatchtowerにウェブサイトが追加されました。"; },
    "API-Key": function(d) { return "API キー"; },
    "Add-account-": function(d) { return "アカウントを追加…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "これらのアイテムは即座に削除されます。1Password.comで期間限定でアイテムを復元することができます。"; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "Masked Emailの生成中にエラーが発生しました。"; },
    "An-error-occurred--Please-try-again-": function(d) { return "エラーが発生しました。もう一度やり直してください。"; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "Masked Emailを有効にして入力する際にエラーが発生しました。"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "予期せぬエラーが発生しました。*********************までお問い合わせください"; },
    "An-unknown-error-has-occurred-": function(d) { return "不明なエラーが発生しました。"; },
    "Any-Website": function(d) { return "あらゆるウェブサイト"; },
    Archive: function(d) { return "アーカイブ"; },
    "Archive--count--items-": function(d) { return d.count + "アイテムをアーカイブしますか？"; },
    "Archived---title--": function(d) { return "「" + d.title + "」をアーカイブしました"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "アーカイブされたアイテムは、ウェブサイトやアプリの候補に表示されなくなります。"; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "「" + d.itemName + "」をアーカイブしますか？"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "「" + d.itemName + " 」を削除しますか？"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return plural(d.count, 0, ja, { other: number(d.count, "count") + "件のアイテム" }) + "を削除してもよろしいですか？"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "このパスワードを削除してもよろしいですか？"; },
    "Authentication-Required": function(d) { return "認証が必要です"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "認証が中断されたか、タイムアウトになりました。もう一度やり直してください。"; },
    Benchmarking: function(d) { return "ベンチマーキング"; },
    Cancel: function(d) { return "キャンセル"; },
    "Collect-Page-Structure": function(d) { return "ページ構造を収集"; },
    "Continue-Signing-In": function(d) { return "サインインを続ける"; },
    "Copied--fieldTitle-": function(d) { return d.fieldTitle + "をコピーしました"; },
    "Copied-field": function(d) { return "コピーしたフィールド"; },
    "Copied-generated-password": function(d) { return "生成されたパスワードをコピーしました"; },
    "Copied-item-JSON": function(d) { return "アイテムJSONをコピーしました"; },
    "Copied-item-UUID": function(d) { return "アイテムUUIDをコピーしました"; },
    "Copied-item-link": function(d) { return "アイテムリンクをコピーしました"; },
    "Copied-one-time-password": function(d) { return "ワンタイムパスワードをコピーしました"; },
    "Corporate-Card": function(d) { return "法人カード"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "1Passwordアプリに接続できませんでした。再度ロック解除をお試しください。"; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "認証を完了できませんでした。1Password for desktopをアップデートして、もう一度試してみてください。"; },
    "Create-Brex-vendor-card-": function(d) { return "Brexベンダーカードを作成…"; },
    "Create-Masked-Email-": function(d) { return "マスクアドレスを作成…"; },
    "Create-Privacy-Card-": function(d) { return "プライバシーカードを作成…"; },
    "Create-SSH-Key-": function(d) { return "SSHキーの作成…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "SSHキーの作成・編集には1Password 8が必要です。"; },
    "Credit-Card": function(d) { return "クレジットカード"; },
    "Credit-card": function(d) { return "クレジットカード"; },
    Delete: function(d) { return "削除"; },
    "Deleted---title--": function(d) { return "「" + d.title + "」を削除しました"; },
    "Disable-Extensions": function(d) { return "拡張機能を無効にする"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "ブラウザで他のバージョンの1Passwordを無効にしますか？"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "「" + d.title + "」の自動サインインを無効にしました"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return d.count + "件のアイテムの自動サインインを無効にしました"; },
    "Disk-space-too-low": function(d) { return "ディスクの空き容量が少なすぎます。"; },
    "Do-you-have-an-existing-account-": function(d) { return "既存のアカウントをお持ちですか?"; },
    "Don-t-Save-in-1Password": function(d) { return "1Passwordに保存しない"; },
    "Download-failed": function(d) { return "ダウンロード失敗"; },
    "Duo-Authentication-Required": function(d) { return "Duo認証が必要です"; },
    "Email-blocked": function(d) { return "メールをブロックしました"; },
    "Email-unblocked": function(d) { return "メールをブロック解除しました"; },
    Employee: function(d) { return "従業員"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "「" + d.title + "」の自動サインインを有効にしました"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return d.count + "件のアイテムの自動サインインを有効にしました"; },
    "Enter-Account-Password": function(d) { return "アカウントパスワードを入力"; },
    "Failed-to-copy-field": function(d) { return "フィールドをコピーできませんでした"; },
    "Failed-to-delete-password": function(d) { return "パスワードを削除できませんでした"; },
    "Favorited---title--": function(d) { return "「" + d.title + "」をお気に入りに登録しました"; },
    "Fill-Masked-Email-": function(d) { return "Masked Emailを入力…"; },
    "Get-Help": function(d) { return "ヘルプを得る"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "認証方法の移行猶予期間が終了しました。再度サインインしてください。"; },
    Help: function(d) { return "ヘルプ"; },
    "Hide-on-this-page": function(d) { return "このページに表示しない"; },
    Identity: function(d) { return "個人情報"; },
    Item: function(d) { return "アイテム"; },
    "Learn-More-": function(d) { return "詳細情報…"; },
    "Link-Existing": function(d) { return "Link Existing"; },
    Lock: function(d) { return "ロック"; },
    Login: function(d) { return "ログイン"; },
    Managed: function(d) { return "管理"; },
    "New-app-or-browser-found": function(d) { return "新しいアプリまたはブラウザが見つかりました"; },
    "New-item-saved-in-1Password-": function(d) { return "1Passwordに新規アイテムが保存されました。"; },
    "No-QR-code-found-for-2FA": function(d) { return "2FAのQRコードが見つかりません"; },
    "No-accounts-found-": function(d) { return "アカウントは見つかりませんでした。"; },
    OK: function(d) { return "OK"; },
    Ok: function(d) { return "OK"; },
    Personal: function(d) { return "個人"; },
    "Physical-Card": function(d) { return "物理的カード"; },
    Private: function(d) { return "プライベート"; },
    "Removed---title---from-favorites": function(d) { return "「" + d.title + "」をお気に入りから削除しました"; },
    "Report-Issue": function(d) { return "問題を報告"; },
    "Report-error-": function(d) { return "エラーを報告する…"; },
    Save: function(d) { return "保存"; },
    Security: function(d) { return "セキュリティ"; },
    Settings: function(d) { return "設定"; },
    Shared: function(d) { return "共有"; },
    "Sign-Up": function(d) { return "サインアップ"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "続行するには、1Passwordアカウント（" + d.accountName + "）にサインインして、Duoで認証してください。"; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "続行するには 続行するには アカウント (" + d.accountName + ") にサインインしてください。"; },
    "Sign-in-with--authProvider-": function(d) { return d.authProvider + "でサインイン"; },
    "Sign-in-with-passkey": function(d) { return "パスキーでサインイン"; },
    "Stay-Offline": function(d) { return "オフラインのまま"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "1Password の最新バージョン (" + d.version + ") をご利用いただきありがとうございます！"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "うまくいきませんでした。パスワードを確認して、もう一度試してください。"; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "このカードはロック/解約されています。別のカードをご利用ください。"; },
    "This-entry-will-be-removed-immediately-": function(d) { return "このエントリーは直ちに削除されます。"; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "これらのアイテムは即座に削除されます。1Password.comで、期間限定でアイテムを復元することができます。"; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "このアカウントで項目を保存するには、前に選択したマスクアドレスのアカウントとリンクする必要があります"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "Touch IDセンサーは現在使用できません。"; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "Touch IDセンサーは現在使用できません。アカウントパスワードを使ってサインインする必要があります。"; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "アイテムにワンタイムパスワードフィールドを追加できません"; },
    "Unable-to-copy-generated-password": function(d) { return "生成されたパスワードをコピーできません"; },
    "Unable-to-fill-generated-password": function(d) { return "生成されたパスワードを入力できません"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "ワンタイムパスワードを生成およびコピーできません"; },
    "Unable-to-save-generated-password": function(d) { return "生成されたパスワードを保存できません"; },
    "Unable-to-unlock-account-": function(d) { return "アカウントのロックを解除することができません。"; },
    "Unknown-Device": function(d) { return "不明なデバイス"; },
    Unlock: function(d) { return "ロック解除"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "デスクトップアプリケーションからアカウントのロックを解除する"; },
    "Use-Suggested-Password": function(d) { return "推奨のパスワードを使用"; },
    "Vendor-Card": function(d) { return "ベンダーカード"; },
    "Watchtower-Alert": function(d) { return "Watchtowerアラート"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "この拡張機能の他のバージョンがインストールされていることを検出しました。" + d.extensionNames + "を無効にしますか？"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "既存のファミリーアカウントをリンクするか、新しいアカウントを作成するかを選択できます。"; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return d.browserName + "にアップデートがあるため、パスワードを再入力するよう求められます。"; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "オフラインです。アカウントにアクセスするには、インターネット接続を確認し、再度お試しください。"; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "選択したアカウントのマスクアドレス作成の1日の上限に達しました。"; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "選択したアカウントのマスクアドレス作成の上限に達しました。"; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "サインインしてオンラインになるまで、変更を保存することはできません。"; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "サインインが完了するまで、変更を保存することはできません。"; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "生成されたパスワードは、サインインしてオンラインになるまで使用することができません。"; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "アカウントへのアクセスが一時的に停止されました。詳細はメールをご確認ください。"; },
    "Your-account-is-offline": function(d) { return "あなたのアカウントはオフラインです"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "アカウントが一時停止されています。詳細については、ファミリーオーガナイザーまたはチーム管理者までお問い合わせください。"; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "アカウントが認証されていません。Fastmailにサインインして、選択したアカウントを認証してください。"; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "セッションの有効期限が切れました。ブラウザの1Passwordは、認証のために再度サインインするよう求めています。"; },
    "one-time-password": function(d) { return "ワンタイムパスワード"; },
    "your-browser": function(d) { return "お使いのブラウザ"; },
    "unit.B": function(d) { return d.size + " バイト"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "ログイン"; },
    "category.002": function(d) { return "クレジットカード"; },
    "category.003": function(d) { return "セキュアノート"; },
    "category.004": function(d) { return "個人情報"; },
    "category.005": function(d) { return "パスワード"; },
    "category.006": function(d) { return "文書"; },
    "category.100": function(d) { return "ソフトウェアライセンス"; },
    "category.101": function(d) { return "銀行口座"; },
    "category.102": function(d) { return "データベース"; },
    "category.103": function(d) { return "運転免許証"; },
    "category.104": function(d) { return "屋外ライセンス"; },
    "category.105": function(d) { return "会員数"; },
    "category.106": function(d) { return "パスポート"; },
    "category.107": function(d) { return "報酬プログラム"; },
    "category.108": function(d) { return "個人番号"; },
    "category.109": function(d) { return "WiFiネットワーク"; },
    "category.110": function(d) { return "サーバー"; },
    "category.111": function(d) { return "メールアカウント"; },
    "categories.001": function(d) { return "ログイン"; },
    "categories.002": function(d) { return "クレジットカード"; },
    "categories.003": function(d) { return "セキュアノート"; },
    "categories.004": function(d) { return "個人情報"; },
    "categories.005": function(d) { return "パスワード"; },
    "categories.006": function(d) { return "文書"; },
    "categories.100": function(d) { return "ソフトウェアライセンス"; },
    "categories.101": function(d) { return "銀行口座"; },
    "categories.102": function(d) { return "データベース"; },
    "categories.103": function(d) { return "運転免許証"; },
    "categories.104": function(d) { return "屋外ライセンス"; },
    "categories.105": function(d) { return "会員数"; },
    "categories.106": function(d) { return "パスポート"; },
    "categories.107": function(d) { return "報酬プログラム"; },
    "categories.108": function(d) { return "個人番号"; },
    "categories.109": function(d) { return "WiFiネットワーク"; },
    "categories.110": function(d) { return "サーバー"; },
    "categories.111": function(d) { return "メールアカウント"; },
    "button.save": function(d) { return "保存"; },
    "button.cancel": function(d) { return "キャンセル"; },
    "unexpected-error": function(d) { return "予期せぬエラーが発生しました。*********************までご連絡ください"; }
  },
  ko: {
    "--accountName---was-added-to-1Password-": function(d) { return "\"" + d.accountName + "\"이(가) 1Password에 추가되었습니다."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "\"" + d.itemName + "\"이(가) \"" + d.vaultName + "\"에 저장되었습니다."; },
    "-Internal--Report-feedback": function(d) { return "[내부] 보고 피드백"; },
    "-accountName--is-offline-": function(d) { return d.accountName + "은(는) 오프라인 상태입니다."; },
    "-count--Items-archived": function(d) { return d.count + "개의 항목 아카이브 완료"; },
    "-count--Items-deleted": function(d) { return d.count + "개의 항목 삭제 완료"; },
    "-count--Items-favorited": function(d) { return d.count + "개의 항목 즐겨찾기 추가 완료"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + "개의 항목 즐겨찾기에서 제거 완료"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.location + " 근처의 " + d.deviceName + "이(가) 회원님의 " + d.accountName + " 계정에 로그인하려고 합니다."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.location + " 근처의 " + d.deviceName + "이(가) 회원님의 " + d.accountName + " 계정에 로그인하려고 합니다. 브라우저의 도구 모음에서 1Password를 열고 확인해 보세요."; },
    "-fileName--downloaded": function(d) { return d.fileName + " 다운로드 완료"; },
    "-itemType--saved": function(d) { return d.itemType + " 저장 완료"; },
    "1Password-8-Required": function(d) { return "1Password 8 필요"; },
    "1Password-account-added": function(d) { return "1Password 계정이 추가됨"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return d.browser + "에서 사용할 수 있는 디스크 공간이 충분하지 않아서 1Password가 데이터베이스를 열 수 없습니다. 1Password를 사용하려면 여유 공간을 확보하세요."; },
    "1Password-is-up-to-date": function(d) { return "1Password가 최신 상태입니다"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password가 더 이상 이 페이지에서 자동으로 채우기 제안을 하지 않습니다.\n\n숨겨진 페이지의 목록은 이 웹 브라우저에 기억됩니다. 언제든지 설정에서 목록을 지울 수 있습니다."; },
    "A-database-error-occurred-": function(d) { return "데이터베이스 오류가 발생했습니다."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "웹사이트가 계정 " + d.count + "개의 Watchtower에 추가되었습니다."; },
    "API-Key": function(d) { return "API 키"; },
    "Add-account-": function(d) { return "계정 추가…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "이 항목들은 모두 영구적으로 제거됩니다. 제한된 시간 동안 1Password.com에서 항목들을 복구할 수 있습니다."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "마스킹된 이메일을 생성하는 중 오류가 발생했습니다."; },
    "An-error-occurred--Please-try-again-": function(d) { return "오류가 발생했습니다. 다시 시도하세요."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "마스킹된 이메일을 활성화하고 채우는 중 오류가 발생했습니다."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "예기치 않은 오류가 발생했습니다. *********************으로 문의해 주세요"; },
    "An-unknown-error-has-occurred-": function(d) { return "알 수 없는 오류가 발생했습니다."; },
    "Any-Website": function(d) { return "모든 웹사이트"; },
    Archive: function(d) { return "아카이브"; },
    "Archive--count--items-": function(d) { return d.count + "개 항목을 아카이브하시겠습니까?"; },
    "Archived---title--": function(d) { return "“" + d.title + "” 아카이브 완료"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "아카이브된 항목은 웹사이트 또는 앱에 대한 자동 입력 제안 항목으로 표시되지 않습니다."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "“" + d.itemName + "”을(를) 아카이브하시겠습니까?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "“" + d.itemName + "”을(를) 삭제하시겠습니까?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return plural(d.count, 0, ko, { other: number(d.count, "count") + "개의 항목" }) + "을 삭제하시겠습니까?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "정말로 이 암호를 삭제 하시겠습니까?"; },
    "Authentication-Required": function(d) { return "인증이 필요합니다"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "인증이 중단되었거나 시간이 초과되었습니다. 다시 시도하세요."; },
    Benchmarking: function(d) { return "벤치마킹"; },
    Cancel: function(d) { return "취소"; },
    "Collect-Page-Structure": function(d) { return "페이지 구조 수집"; },
    "Continue-Signing-In": function(d) { return "계속 로그인"; },
    "Copied--fieldTitle-": function(d) { return d.fieldTitle + " 복사 완료"; },
    "Copied-field": function(d) { return "필드 복사 완료"; },
    "Copied-generated-password": function(d) { return "생성된 비밀번호 복사 완료"; },
    "Copied-item-JSON": function(d) { return "항목 JSON 복사 완료"; },
    "Copied-item-UUID": function(d) { return "항목 UUID 복사 완료"; },
    "Copied-item-link": function(d) { return "항목 링크 복사 완료"; },
    "Copied-one-time-password": function(d) { return "일회용 비밀번호 복사 완료"; },
    "Corporate-Card": function(d) { return "법인 카드"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "1Password 앱에 연결하지 못했습니다. 다시 잠금 해제를 시도해 보세요."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "인증을 완료하지 못했습니다. 데스크톱용 1Password를 업데이트하고 다시 시도하세요."; },
    "Create-Brex-vendor-card-": function(d) { return "Brex 벤더 카드 만들기…"; },
    "Create-Masked-Email-": function(d) { return "마스킹된 이메일 생성…"; },
    "Create-Privacy-Card-": function(d) { return "Privacy 카드 만들기…"; },
    "Create-SSH-Key-": function(d) { return "SSH 키 생성…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "SSH 키를 생성하고 편집하려면 1Password 8이 필요합니다."; },
    "Credit-Card": function(d) { return "신용 카드"; },
    "Credit-card": function(d) { return "신용카드"; },
    Delete: function(d) { return "삭제"; },
    "Deleted---title--": function(d) { return "“" + d.title + "” 삭제 완료"; },
    "Disable-Extensions": function(d) { return "확장 프로그램 비활성화"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "브라우저에서 다른 버전의 1Password를 비활성화할까요?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "“" + d.title + "”에서 자동으로 로그인을 비활성화함"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return d.count + "개 항목에서 자동으로 로그인을 비활성화함"; },
    "Disk-space-too-low": function(d) { return "디스크 공간이 너무 부족합니다"; },
    "Do-you-have-an-existing-account-": function(d) { return "기존 계정이 있으세요?"; },
    "Don-t-Save-in-1Password": function(d) { return "1Password에 저장하지 않음"; },
    "Download-failed": function(d) { return "다운로드 실패"; },
    "Duo-Authentication-Required": function(d) { return "Duo 인증 필요"; },
    "Email-blocked": function(d) { return "이메일이 차단됨"; },
    "Email-unblocked": function(d) { return "이메일이 차단 해제됨"; },
    Employee: function(d) { return "직원"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "“" + d.title + "”에서 자동으로 로그인을 활성화함"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return d.count + "개 항목에서 자동으로 로그인을 활성화함"; },
    "Enter-Account-Password": function(d) { return "계정 비밀번호를 입력하세요"; },
    "Failed-to-copy-field": function(d) { return "필드를 복사하지 못했습니다"; },
    "Failed-to-delete-password": function(d) { return "비밀번호를 삭제하지 못했습니다"; },
    "Favorited---title--": function(d) { return "즐겨찾기에 “" + d.title + "” 추가 완료"; },
    "Fill-Masked-Email-": function(d) { return "마스킹된 이메일 채우기…"; },
    "Get-Help": function(d) { return "도움말 보기"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "인증 방법 이전에 대한 유예 기간이 만료되었습니다. 다시 로그인하세요."; },
    Help: function(d) { return "도움말"; },
    "Hide-on-this-page": function(d) { return "이 페이지에서 숨기기"; },
    Identity: function(d) { return "신분 정보"; },
    Item: function(d) { return "항목"; },
    "Learn-More-": function(d) { return "자세히 알아보기…"; },
    "Link-Existing": function(d) { return "기존 데이터에 연결"; },
    Lock: function(d) { return "잠금"; },
    Login: function(d) { return "로그인 정보"; },
    Managed: function(d) { return "관리됨"; },
    "New-app-or-browser-found": function(d) { return "새로운 앱 또는 브라우저가 발견되었습니다"; },
    "New-item-saved-in-1Password-": function(d) { return "1Password에 새 항목이 저장되었습니다."; },
    "No-QR-code-found-for-2FA": function(d) { return "2단계 인증용 QR 코드를 찾을 수 없습니다"; },
    "No-accounts-found-": function(d) { return "계정을 찾을 수 없습니다."; },
    OK: function(d) { return "확인"; },
    Ok: function(d) { return "확인"; },
    Personal: function(d) { return "개인"; },
    "Physical-Card": function(d) { return "실물 카드"; },
    Private: function(d) { return "비공개"; },
    "Removed---title---from-favorites": function(d) { return "즐겨찾기에서 “" + d.title + "” 제거 완료"; },
    "Report-Issue": function(d) { return "문제 신고"; },
    "Report-error-": function(d) { return "오류 신고…"; },
    Save: function(d) { return "저장"; },
    Security: function(d) { return "보안"; },
    Settings: function(d) { return "설정"; },
    Shared: function(d) { return "공유"; },
    "Sign-Up": function(d) { return "회원 가입"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "계속하려면 회원님의 1Password 계정(" + d.accountName + ")에 로그인하고 Duo를 통해 인증하세요."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "계속하려면 회원님의 1Password 계정(" + d.accountName + ")에 로그인하세요."; },
    "Sign-in-with--authProvider-": function(d) { return d.authProvider + "(으)로 로그인"; },
    "Sign-in-with-passkey": function(d) { return "패스 키로 로그인"; },
    "Stay-Offline": function(d) { return "오프라인 상태 유지"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "1Password의 최신 버전(" + d.version + ")을 실행해 주셔서 감사합니다!"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "성공하지 못했습니다. 비밀번호를 확인한 후 다시 시도하세요."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "이 카드는 잠기거나 해지되었습니다. 다른 카드를 사용하세요."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "이 항목은 즉시 제거됩니다."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "이 항목은 즉시 제거됩니다. 제한된 시간 동안 1Password.com에서 항목을 복구할 수 있습니다."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "이 계정에 항목을 저장하려면 이전에 선택한 마스킹된 이메일 계정에 연결해야 합니다"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "현재 Touch ID 센서를 사용할 수 없습니다."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "현재 Touch ID 센서를 사용할 수 없습니다. 계정 비밀번호를 사용하여 로그인해야 합니다."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "일회용 비밀번호 필드를 항목에 추가할 수 없습니다"; },
    "Unable-to-copy-generated-password": function(d) { return "생성된 비밀번호를 복사할 수 없습니다"; },
    "Unable-to-fill-generated-password": function(d) { return "생성된 비밀번호를 자동 입력할 수 없습니다"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "일회용 비밀번호를 생성 및 복사할 수 없습니다"; },
    "Unable-to-save-generated-password": function(d) { return "생성된 비밀번호를 저장할 수 없습니다"; },
    "Unable-to-unlock-account-": function(d) { return "계정을 잠금 해제할 수 없습니다."; },
    "Unknown-Device": function(d) { return "알 수 없는 장치"; },
    Unlock: function(d) { return "잠금 해제"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "데스크톱 앱에서 계정 잠금 해제"; },
    "Use-Suggested-Password": function(d) { return "제안된 비밀번호 사용"; },
    "Vendor-Card": function(d) { return "벤더 카드"; },
    "Watchtower-Alert": function(d) { return "Watchtower 경고"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "이 확장 프로그램의 다른 버전이 발견되었습니다. " + d.extensionNames + "을(를) 비활성화하시겠습니까?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "기존 가족 계정을 연결하거나 새로운 계정을 만드실 수 있습니다."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return d.browserName + "에 사용 가능한 업데이트가 있어서 비밀번호를 다시 입력하라는 요청을 받았습니다."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "현재 오프라인 상태입니다. 계정에 액세스하려면 인터넷 연결을 확인한 후 다시 시도하세요."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "선택된 계정에 대하여 마스킹된 이메일 생성 일일 한도에 도달했습니다."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "선택된 계정에 대하여 마스킹된 이메일 생성 한도에 도달했습니다."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "로그인하고 온라인 상태일 때까지 변경 내용을 저장할 수 없습니다."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "로그인을 완료할 때까지 변경 사항을 저장할 수 없습니다."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "로그인하고 온라인 상태일 때까지 생성된 비밀번호를 사용할 수 없습니다."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "회원님의 계정은 일시적으로 정지되었습니다. 이메일에서 자세한 사항을 확인하세요."; },
    "Your-account-is-offline": function(d) { return "계정이 오프라인 상태입니다"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "회원님의 계정은 일시 중지되었습니다. 자세한 사항은 가족 관리자 또는 팀 관리자에게 문의하세요."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "귀하의 계정은 인증되지 않았습니다. Fastmail에 로그인하여 선택된 계정을 인증하세요."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "세션이 만료되었습니다. 브라우저에서 1Password를 사용하려면 다시 로그인하고 인증해야 합니다."; },
    "one-time-password": function(d) { return "일회용 비밀번호"; },
    "your-browser": function(d) { return "사용 중인 브라우저"; },
    "unit.B": function(d) { return d.size + " 바이트"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "로그인"; },
    "category.002": function(d) { return "신용카드"; },
    "category.003": function(d) { return "보안 노트"; },
    "category.004": function(d) { return "신원 정보"; },
    "category.005": function(d) { return "비밀번호"; },
    "category.006": function(d) { return "문서"; },
    "category.100": function(d) { return "소프트웨어 라이선스"; },
    "category.101": function(d) { return "은행 계좌"; },
    "category.102": function(d) { return "데이터베이스"; },
    "category.103": function(d) { return "운전 면허"; },
    "category.104": function(d) { return "수렵 면허"; },
    "category.105": function(d) { return "회원권"; },
    "category.106": function(d) { return "여권"; },
    "category.107": function(d) { return "마일리지 프로그램"; },
    "category.108": function(d) { return "주민등록번호"; },
    "category.109": function(d) { return "WiFi 네트워크"; },
    "category.110": function(d) { return "서버"; },
    "category.111": function(d) { return "이메일 계정"; },
    "categories.001": function(d) { return "로그인 정보"; },
    "categories.002": function(d) { return "신용카드"; },
    "categories.003": function(d) { return "보안 노트"; },
    "categories.004": function(d) { return "신원 정보"; },
    "categories.005": function(d) { return "비밀번호"; },
    "categories.006": function(d) { return "문서"; },
    "categories.100": function(d) { return "소프트웨어 라이선스"; },
    "categories.101": function(d) { return "은행 계좌"; },
    "categories.102": function(d) { return "데이터베이스"; },
    "categories.103": function(d) { return "운전 면허"; },
    "categories.104": function(d) { return "수렵 면허"; },
    "categories.105": function(d) { return "회원권"; },
    "categories.106": function(d) { return "여권"; },
    "categories.107": function(d) { return "마일리지 프로그램"; },
    "categories.108": function(d) { return "주민등록번호"; },
    "categories.109": function(d) { return "WiFi 네트워크"; },
    "categories.110": function(d) { return "서버"; },
    "categories.111": function(d) { return "이메일 계정"; },
    "button.save": function(d) { return "저장"; },
    "button.cancel": function(d) { return "취소"; },
    "unexpected-error": function(d) { return "예기치 않은 오류가 발생했습니다. *********************으로 문의해 주세요"; }
  },
  nl: {
    "--accountName---was-added-to-1Password-": function(d) { return d.accountName + " is toegevoegd aan 1Password."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return d.itemName + " is opgeslagen in " + d.vaultName + "."; },
    "-Internal--Report-feedback": function(d) { return "[Intern] Feedback rapporteren"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " is offline."; },
    "-count--Items-archived": function(d) { return d.count + " items gearchiveerd"; },
    "-count--Items-deleted": function(d) { return d.count + " items verwijderd"; },
    "-count--Items-favorited": function(d) { return d.count + " items als favoriet gemarkeerd"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " items uit favorieten verwijderd"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.deviceName + " in de buurt van " + d.location + " probeert in te loggen op je " + d.accountName + "-account."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.deviceName + " in de buurt van " + d.location + " probeert in te loggen op je " + d.accountName + "-account. Open 1Password in de werkbalk van je browser om deze poging te bekijken."; },
    "-fileName--downloaded": function(d) { return d.fileName + " gedownload"; },
    "-itemType--saved": function(d) { return d.itemType + " opgeslagen"; },
    "1Password-8-Required": function(d) { return "1Password 8 vereist"; },
    "1Password-account-added": function(d) { return "1Password-account toegevoegd"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password kan zijn database niet openen omdat er niet genoeg opslagruimte beschikbaar is voor " + d.browser + ". Maak wat ruimte vrij om 1Password te gebruiken."; },
    "1Password-is-up-to-date": function(d) { return "1Password is bijgewerkt"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password doet op deze pagina geen suggesties meer voor automatisch aanvullen.\n\nDe lijst met verborgen pagina's staat opgeslagen in deze webbrowser. Je kunt deze opschonen onder instellingen."; },
    "A-database-error-occurred-": function(d) { return "Er is een databasefout opgetreden."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "Er is een webpagina toegevoegd aan Watchtower in " + d.count + " van van je accounts."; },
    "API-Key": function(d) { return "API-sleutel"; },
    "Add-account-": function(d) { return "Account toevoegen…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Al deze items worden direct verwijderd. Je kunt items voor een beperkte tijd herstellen op 1Password.com."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "Er is een fout opgetreden bij het genereren van de gemaskeerde e-mail."; },
    "An-error-occurred--Please-try-again-": function(d) { return "Er is een fout opgetreden. Probeer het nog eens."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "Er is een fout opgetreden bij het inschakelen en invullen van de gemaskeerde e-mail."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Er is een onverwachte fout opgetreden. Neem contact <NAME_EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "Er is een onbekend fout opgetreden."; },
    "Any-Website": function(d) { return "Alle websites"; },
    Archive: function(d) { return "Archief"; },
    "Archive--count--items-": function(d) { return d.count + " items archiveren?"; },
    "Archived---title--": function(d) { return "{title} gearchiveerd"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "Gearchiveerde items worden niet getoond in invulsuggesties voor websites of in apps."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "Weet je zeker dat je {itemName} wilt archiveren?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "Weet je zeker dat je {itemName} wilt verwijderen?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "Weet je zeker dat je " + plural(d.count, 0, nl, { one: number(d.count, "count") + " item", other: number(d.count, "count") + " items" }) + " wilt verwijderen?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "Weet je zeker dat je dit wachtwoord wilt verwijderen?"; },
    "Authentication-Required": function(d) { return "Authenticatie vereist"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "Authenticatie onderbroken of er is een time-out opgetreden. Probeer het opnieuw."; },
    Benchmarking: function(d) { return "Benchmarking"; },
    Cancel: function(d) { return "Annuleren"; },
    "Collect-Page-Structure": function(d) { return "Paginastructuur ophalen"; },
    "Continue-Signing-In": function(d) { return "Doorgaan met inloggen"; },
    "Copied--fieldTitle-": function(d) { return d.fieldTitle + " gekopieerd"; },
    "Copied-field": function(d) { return "Veld gekopieerd"; },
    "Copied-generated-password": function(d) { return "Gegenereerd wachtwoord gekopieerd"; },
    "Copied-item-JSON": function(d) { return "JSON van item gekopieerd"; },
    "Copied-item-UUID": function(d) { return "UUID van item gekopieerd"; },
    "Copied-item-link": function(d) { return "Itemlink gekopieerd"; },
    "Copied-one-time-password": function(d) { return "Eenmalig wachtwoord gekopieerd"; },
    "Corporate-Card": function(d) { return "Bedrijfskaart"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "Kan niet verbinden met de 1Password-app. Probeer opnieuw te ontgrendelen."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "Kon de authenticatiemail niet voltooien. Werk 1Password voor desktop bij en probeer het opnieuw."; },
    "Create-Brex-vendor-card-": function(d) { return "Brex-leverancierskaart aanmaken…"; },
    "Create-Masked-Email-": function(d) { return "Gemaskeerde e-mail aanmaken…"; },
    "Create-Privacy-Card-": function(d) { return "Privékaart aanmaken…"; },
    "Create-SSH-Key-": function(d) { return "SSH-key aanmaken…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "Je hebt 1Password 8 nodig om SSH-keys aan te maken en te bewerken."; },
    "Credit-Card": function(d) { return "Creditcard"; },
    "Credit-card": function(d) { return "Creditcard"; },
    Delete: function(d) { return "Verwijderen"; },
    "Deleted---title--": function(d) { return "{title} verwijderd"; },
    "Disable-Extensions": function(d) { return "Extensies uitschakelen"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "Anders versies van 1Password in de browser uitschakelen?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "Automatisch inloggen uitgeschakeld voor \"" + d.title + "\""; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "Automatisch inloggen uitgeschakeld voor " + d.count + " items"; },
    "Disk-space-too-low": function(d) { return "Te weinig schijfruimte"; },
    "Do-you-have-an-existing-account-": function(d) { return "Heb je een bestaand account?"; },
    "Don-t-Save-in-1Password": function(d) { return "Niet bewaren in 1Password"; },
    "Download-failed": function(d) { return "Download mislukt"; },
    "Duo-Authentication-Required": function(d) { return "Duo-authenticatie vereist"; },
    "Email-blocked": function(d) { return "E-mail geblokkeerd"; },
    "Email-unblocked": function(d) { return "E-mail gedeblokkeerd"; },
    Employee: function(d) { return "Medewerker"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "Automatisch inloggen ingeschakeld voor \"" + d.title + "\""; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "Automatisch inloggen ingeschakeld voor " + d.count + " items"; },
    "Enter-Account-Password": function(d) { return "Accountwachtwoord invoeren"; },
    "Failed-to-copy-field": function(d) { return "Veld kopiëren mislukt"; },
    "Failed-to-delete-password": function(d) { return "Wachtwoord verwijderen is mislukt"; },
    "Favorited---title--": function(d) { return d.title + " als favoriet toegevoegd"; },
    "Fill-Masked-Email-": function(d) { return "Gemaskeerd e-mailadres invullen…"; },
    "Get-Help": function(d) { return "Krijg hulp"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "Respijtperiode voor migratie van authenticatiemethode is verlopen. Log opnieuw in."; },
    Help: function(d) { return "Ondersteuning"; },
    "Hide-on-this-page": function(d) { return "Verbergen op deze pagina"; },
    Identity: function(d) { return "Identiteit"; },
    Item: function(d) { return "Item"; },
    "Learn-More-": function(d) { return "Meer informatie…"; },
    "Link-Existing": function(d) { return "Bestaande koppelen"; },
    Lock: function(d) { return "Vergrendelen"; },
    Login: function(d) { return "Login"; },
    Managed: function(d) { return "Beheerd"; },
    "New-app-or-browser-found": function(d) { return "Nieuwe app of browser gevonden"; },
    "New-item-saved-in-1Password-": function(d) { return "Nieuw item opgeslagen in 1Password."; },
    "No-QR-code-found-for-2FA": function(d) { return "Geen QR-code gevonden voor 2FA"; },
    "No-accounts-found-": function(d) { return "Geen accounts gevonden."; },
    OK: function(d) { return "OK"; },
    Ok: function(d) { return "Ok"; },
    Personal: function(d) { return "Persoonlijk"; },
    "Physical-Card": function(d) { return "Fysieke kaart"; },
    Private: function(d) { return "Privé"; },
    "Removed---title---from-favorites": function(d) { return d.title + " uit favorieten verwijderd"; },
    "Report-Issue": function(d) { return "Probleem rapporteren"; },
    "Report-error-": function(d) { return "Rapportfout…"; },
    Save: function(d) { return "Opslaan"; },
    Security: function(d) { return "Beveiliging"; },
    Settings: function(d) { return "Instellingen"; },
    Shared: function(d) { return "Gedeeld"; },
    "Sign-Up": function(d) { return "Aanmelden"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "Log in op je 1Password-account (" + d.accountName + ") en bevestig met Duo om verder te gaan."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "Log in op je 1Password-account (" + d.accountName + ") om verder te gaan."; },
    "Sign-in-with--authProvider-": function(d) { return "Inloggen met " + d.authProvider; },
    "Sign-in-with-passkey": function(d) { return "Log in met passkey"; },
    "Stay-Offline": function(d) { return "Offline blijven"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "Bedankt voor het gebruiken van de meest recente versie (" + d.version + ") van 1Password!"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "Er gaat iets mis. Controleer je wachtwoord en probeer het opnieuw."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "Deze kaart is vergrendeld/opgezegd. Gebruik een andere kaart."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "Deze invoer wordt onmiddellijk verwijderd."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Dit item wordt meteen verwijderd. Je kunt items gedurende beperkte tijd herstellen op 1Password.com."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "Om een item op te slaan in dit account moet je het linken aan een eerder geselecteerd gemaskeerd e-mail-account"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "De Touch ID-sensor is momenteel niet beschikbaar."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "De Touch ID-sensor is momenteel niet beschikbaar. Log in met je accountwachtwoord."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "Kan eenmalig wachtwoordveld niet toevoegen aan item"; },
    "Unable-to-copy-generated-password": function(d) { return "Kan gegenereerd wachtwoord niet kopiëren"; },
    "Unable-to-fill-generated-password": function(d) { return "Kon gegenereerd wachtwoord niet invullen"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "Kan eenmalig wachtwoord niet genereren en kopiëren"; },
    "Unable-to-save-generated-password": function(d) { return "Kan gegenereerd wachtwoord niet opslaan"; },
    "Unable-to-unlock-account-": function(d) { return "Account ontgrendelen mislukt."; },
    "Unknown-Device": function(d) { return "Onbekend apparaat"; },
    Unlock: function(d) { return "Ontgrendelen"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "Ontgrendel account via de desktopapp"; },
    "Use-Suggested-Password": function(d) { return "Voorgesteld wachtwoord gebruiken"; },
    "Vendor-Card": function(d) { return "Leverancierskaart"; },
    "Watchtower-Alert": function(d) { return "Watchtower-melding"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "We hebben andere geïnstalleerde versies van deze extensie gedetecteerd. Wil je " + d.extensionNames + " uitschakelen?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "Je kan ervoor kiezen een bestaand familieaccount te koppelen of om een nieuw account te maken."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "Je moet je wachtwoord opnieuw invoeren omdat " + d.browserName + " een update beschikbaar heeft."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "Je bent offline. Controleer je internetverbinding en probeer opnieuw je account te openen."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "Je hebt de dagelijkse limiet voor het aanmaken van gemaskeerde e-mail bereikt voor het geselecteerde account."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "Je hebt het dagelijkse maximumquota voor het aanmaken van gemaskeerde e-mail bereikt voor het geselecteerde account."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "Je kunt niets opslaan tot je bent ingelogd en bent verbonden met internet."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "Je kunt je wijzigingen niet opslaan tot je bent ingelogd."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "Je kunt een gegenereerd wachtwoord pas gebruiken wanneer je bent ingelogd en online bent."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "Je accounttoegang is tijdelijk gepauzeerd. Controleer je e-mail voor meer informatie."; },
    "Your-account-is-offline": function(d) { return "Je account is offline"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "Je account is opgeschort. Neem voor meer informatie contact op met je gezinsorganisator of teambeheerder."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "Je account is niet geverifieerd. Log in bij Fastmail om het geselecteerde account te verifiëren."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "Je sessie is verlopen. Log opnieuw in om 1Password in de browser te verifiëren."; },
    "one-time-password": function(d) { return "eenmalig wachtwoord"; },
    "your-browser": function(d) { return "jouw browser"; },
    "unit.B": function(d) { return d.size + " bytes"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "Login"; },
    "category.002": function(d) { return "Creditcard"; },
    "category.003": function(d) { return "Beveiligde notitie"; },
    "category.004": function(d) { return "Identiteit"; },
    "category.005": function(d) { return "Wachtwoord"; },
    "category.006": function(d) { return "Document"; },
    "category.100": function(d) { return "Softwarelicentie"; },
    "category.101": function(d) { return "Bankrekening"; },
    "category.102": function(d) { return "Database"; },
    "category.103": function(d) { return "Rijbewijs"; },
    "category.104": function(d) { return "Jachtvergunning"; },
    "category.105": function(d) { return "Lidmaatschap"; },
    "category.106": function(d) { return "Paspoort"; },
    "category.107": function(d) { return "Beloningsprogramma"; },
    "category.108": function(d) { return "Burgerservicenummer"; },
    "category.109": function(d) { return "Wifi-netwerk"; },
    "category.110": function(d) { return "Server"; },
    "category.111": function(d) { return "E-mailaccount"; },
    "categories.001": function(d) { return "Logins"; },
    "categories.002": function(d) { return "Creditcards"; },
    "categories.003": function(d) { return "Beveiligde notities"; },
    "categories.004": function(d) { return "Identiteiten"; },
    "categories.005": function(d) { return "Wachtwoorden"; },
    "categories.006": function(d) { return "Documenten"; },
    "categories.100": function(d) { return "Softwarelicenties"; },
    "categories.101": function(d) { return "Bankrekeningen"; },
    "categories.102": function(d) { return "Databases"; },
    "categories.103": function(d) { return "Rijbewijzen"; },
    "categories.104": function(d) { return "Jachtvergunningen"; },
    "categories.105": function(d) { return "Lidmaatschappen"; },
    "categories.106": function(d) { return "Paspoorten"; },
    "categories.107": function(d) { return "Beloningsprogramma's"; },
    "categories.108": function(d) { return "Burgerservicenummers"; },
    "categories.109": function(d) { return "Wifi-netwerken"; },
    "categories.110": function(d) { return "Servers"; },
    "categories.111": function(d) { return "E-mailaccounts"; },
    "button.save": function(d) { return "Opslaan"; },
    "button.cancel": function(d) { return "Annuleren"; },
    "unexpected-error": function(d) { return "Er is een onverwachte fout opgetreden. Neem contact <NAME_EMAIL>"; }
  },
  pt: {
    "--accountName---was-added-to-1Password-": function(d) { return "\"" + d.accountName + "\" foi adicionada ao 1Password."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "\"" + d.itemName + "\" foi salvo em \"" + d.vaultName + "\"."; },
    "-Internal--Report-feedback": function(d) { return "[Interno] Relatar feedback"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " está offline."; },
    "-count--Items-archived": function(d) { return d.count + " itens arquivados"; },
    "-count--Items-deleted": function(d) { return d.count + " itens excluídos"; },
    "-count--Items-favorited": function(d) { return d.count + " itens adicionados aos favoritos"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " itens removidos dos favoritos"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return d.deviceName + " perto de " + d.location + " está tentando fazer login na sua conta " + d.accountName + "."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return d.deviceName + " perto de " + d.location + " está tentando fazer login na sua conta " + d.accountName + ". Abra 1Password na barra de ferramentas do seu navegador para visualizar."; },
    "-fileName--downloaded": function(d) { return d.fileName + " downloaded"; },
    "-itemType--saved": function(d) { return d.itemType + " salvo"; },
    "1Password-8-Required": function(d) { return "É necessário o 1Password 8"; },
    "1Password-account-added": function(d) { return "Conta do 1Password adicionada"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "O 1Password não pode abrir seu banco de dados porque não há espaço em disco suficiente disponível para o " + d.browser + ". Para usar o 1Password, libere algum espaço."; },
    "1Password-is-up-to-date": function(d) { return "O 1Password está atualizado"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "O 1Password não fará mais sugestões de preenchimento automático nesta página.\n\nA lista de páginas ocultas é lembrada neste navegador da web. Você pode limpar a lista a qualquer momento nas configurações."; },
    "A-database-error-occurred-": function(d) { return "Ocorreu um erro no banco de dados."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "Foi adicionado um site ao Watchtower em " + d.count + " de suas contas."; },
    "API-Key": function(d) { return "Chave da API"; },
    "Add-account-": function(d) { return "Adicionar conta…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Todos esses itens serão removidos imediatamente. Você pode recuperar itens por um tempo limitado em 1Password.com."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "Ocorreu um erro na geração do Email Oculto."; },
    "An-error-occurred--Please-try-again-": function(d) { return "Ocorreu um erro. Tente novamente."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "Ocorreu um erro na ativação e no preenchimento do Email Oculto."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Ocorreu um erro inesperado. Entre em <NAME_EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "Ocorreu um erro desconhecido."; },
    "Any-Website": function(d) { return "Qualquer site"; },
    Archive: function(d) { return "Arquivar"; },
    "Archive--count--items-": function(d) { return "Arquivar " + d.count + " itens?"; },
    "Archived---title--": function(d) { return "Arquivou “" + d.title + "”"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "Os itens arquivados não serão exibidos nas sugestões de preenchimento em sites e aplicativos."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "Deseja mesmo arquivar \"" + d.itemName + "”?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "Deseja mesmo excluir \"" + d.itemName + "”?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "Deseja mesmo excluir " + plural(d.count, 0, pt, { one: number(d.count, "count") + " item", other: number(d.count, "count") + " itens" }) + "?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "Você tem certeza que deseja excluir esta senha?"; },
    "Authentication-Required": function(d) { return "É necessário autenticar"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "A autenticação foi interrompida ou o tempo limite se esgotou. Tente novamente."; },
    Benchmarking: function(d) { return "Análise comparativa"; },
    Cancel: function(d) { return "Cancelar"; },
    "Collect-Page-Structure": function(d) { return "Coletar estrutura da página"; },
    "Continue-Signing-In": function(d) { return "Prossiga com o acesso"; },
    "Copied--fieldTitle-": function(d) { return "Copiou " + d.fieldTitle; },
    "Copied-field": function(d) { return "Campo copiado"; },
    "Copied-generated-password": function(d) { return "Copiou senha gerada"; },
    "Copied-item-JSON": function(d) { return "Copiado JSON do item"; },
    "Copied-item-UUID": function(d) { return "Copiada UUID do item"; },
    "Copied-item-link": function(d) { return "Copiado link do item"; },
    "Copied-one-time-password": function(d) { return "Copiou senha de uso único"; },
    "Corporate-Card": function(d) { return "Cartão corporativo"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "Não foi possível conectar no aplicativo 1Password. Tente desbloquear novamente."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "Não foi possível concluir a autenticação. Atualize o 1Password para desktop e tente novamente."; },
    "Create-Brex-vendor-card-": function(d) { return "Criar cartão de fornecedor Brex…"; },
    "Create-Masked-Email-": function(d) { return "Criar email oculto…"; },
    "Create-Privacy-Card-": function(d) { return "Criar cartão Privacy…"; },
    "Create-SSH-Key-": function(d) { return "Criar chave SSH…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "A criação e edição de chaves SSH exigem o 1Password 8."; },
    "Credit-Card": function(d) { return "Cartão de crédito"; },
    "Credit-card": function(d) { return "Cartão de crédito"; },
    Delete: function(d) { return "Excluir"; },
    "Deleted---title--": function(d) { return "Excluiu “" + d.title + "”"; },
    "Disable-Extensions": function(d) { return "Desabilitar extensões"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "Desativar outras versões do 1Password no navegador?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "Login automático desativado em “" + d.title + "”"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "Login automático desativado em " + d.count + " itens"; },
    "Disk-space-too-low": function(d) { return "Espaço em disco insuficiente"; },
    "Do-you-have-an-existing-account-": function(d) { return "Você já tem conta?"; },
    "Don-t-Save-in-1Password": function(d) { return "Não salvar no 1Password"; },
    "Download-failed": function(d) { return "A transferência falhou"; },
    "Duo-Authentication-Required": function(d) { return "É necessário autenticar no Duo"; },
    "Email-blocked": function(d) { return "Email bloqueado"; },
    "Email-unblocked": function(d) { return "Email desbloqueado"; },
    Employee: function(d) { return "Funcionário"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "Login automático ativado em “" + d.title + "”"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "Login automático ativado em " + d.count + " itens"; },
    "Enter-Account-Password": function(d) { return "Digite a senha da conta"; },
    "Failed-to-copy-field": function(d) { return "Falha ao copiar o campo"; },
    "Failed-to-delete-password": function(d) { return "Não foi possível excluir a senha"; },
    "Favorited---title--": function(d) { return "Incluiu “" + d.title + "” nos favoritos"; },
    "Fill-Masked-Email-": function(d) { return "Preencher email oculto…"; },
    "Get-Help": function(d) { return "Receber ajuda"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "O período de carência para migração do método de autenticação expirou. Entre novamente."; },
    Help: function(d) { return "Ajuda"; },
    "Hide-on-this-page": function(d) { return "Ocultar nesta página"; },
    Identity: function(d) { return "Identidade"; },
    Item: function(d) { return "Item"; },
    "Learn-More-": function(d) { return "Saiba mais…"; },
    "Link-Existing": function(d) { return "Link existente"; },
    Lock: function(d) { return "Bloquear"; },
    Login: function(d) { return "Dados de acesso"; },
    Managed: function(d) { return "Gerenciou"; },
    "New-app-or-browser-found": function(d) { return "Novo aplicativo ou navegador encontrado"; },
    "New-item-saved-in-1Password-": function(d) { return "Novo item salvo no 1Password."; },
    "No-QR-code-found-for-2FA": function(d) { return "Não foi encontrado um código QR para 2FA"; },
    "No-accounts-found-": function(d) { return "Nenhuma conta encontrada."; },
    OK: function(d) { return "OK"; },
    Ok: function(d) { return "Ok"; },
    Personal: function(d) { return "Pessoal"; },
    "Physical-Card": function(d) { return "Cartão físico"; },
    Private: function(d) { return "Privado"; },
    "Removed---title---from-favorites": function(d) { return "Removeu “" + d.title + "” dos favoritos"; },
    "Report-Issue": function(d) { return "Reportar problema"; },
    "Report-error-": function(d) { return "Reportar erro…"; },
    Save: function(d) { return "Salvar"; },
    Security: function(d) { return "Segurança"; },
    Settings: function(d) { return "Configurações"; },
    Shared: function(d) { return "Compartilhado"; },
    "Sign-Up": function(d) { return "Inscrever-se"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "Acesse sua conta do 1Password (" + d.accountName + ") e autentique com o Duo para continuar."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "Acesse sua conta do 1Password (" + d.accountName + ") para continuar."; },
    "Sign-in-with--authProvider-": function(d) { return "Entrar com " + d.authProvider; },
    "Sign-in-with-passkey": function(d) { return "Entrar com passkey"; },
    "Stay-Offline": function(d) { return "Ficar offline"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "Obrigado por utilizar a versão mais recente (" + d.version + ") do 1Password!"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "Não funcionou. Verifique a senha e tente novamente."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "Este cartão foi bloqueado/cancelado. Utilize outro cartão."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "Esta informação será removida imediatamente."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Este item será removido imediatamente. Você pode recuperar itens por tempo limitado em 1Password.com."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "Para salvar um item nessa conta é preciso vinculá-la à conta de email oculta selecionada anteriormente"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "O sensor Touch ID não está disponível no momento."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "O sensor Touch ID não está disponível no momento. Você precisa fazer login usando a senha da sua conta."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "Não foi possível incluir um campo de senha de uso único no item"; },
    "Unable-to-copy-generated-password": function(d) { return "Não foi possível copiar a senha gerada"; },
    "Unable-to-fill-generated-password": function(d) { return "Não foi possível preencher com a senha gerada"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "Não foi possível gerar e copiar uma senha de uso único"; },
    "Unable-to-save-generated-password": function(d) { return "Não foi possível salvar a senha gerada"; },
    "Unable-to-unlock-account-": function(d) { return "Não foi possível desbloquear a conta."; },
    "Unknown-Device": function(d) { return "Dispositivo desconhecido"; },
    Unlock: function(d) { return "Desbloquear"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "Desbloquear conta no aplicativo para computador"; },
    "Use-Suggested-Password": function(d) { return "Usar senha sugerida"; },
    "Vendor-Card": function(d) { return "Cartão de fornecedor"; },
    "Watchtower-Alert": function(d) { return "Alerta do Watchtower"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "Detectamos outras versões desta extensão instaladas. Gostaria de desabilitar " + d.extensionNames + "?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "Você pode optar por vincular uma conta familiar existente ou criar outra conta."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "Você está sendo solicitado a reinserir sua senha porque " + d.browserName + " tem uma atualização disponível."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "Você está offline. Para acessar sua conta, verifique sua conexão de internet e tente novamente."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "Você atingiu o limite diário de criação de Emails Ocultos na conta selecionada."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "Você atingiu a cota máxima de criação de Emails Ocultos na conta selecionada."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "Você não poderá salvar as alterações até que esteja na conta e online."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "Você não poderá salvar as alterações até concluir o acesso."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "Você não poderá utilizar a senha gerada até você entrar na conta e estar online."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "O acesso à sua conta foi temporariamente pausado. Verifique seu e-mail para mais detalhes."; },
    "Your-account-is-offline": function(d) { return "Sua conta está offline"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "Sua conta está suspensa. Entre em contato com o organizador da família ou o administrador da equipe para mais informações."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "Sua conta não foi confirmada. Acesse o Fastmail para confirmar a conta selecionada."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "Sua sessão expirou. O 1Password no navegador gostaria que você entrasse na conta novamente para autenticar."; },
    "one-time-password": function(d) { return "senha de uso único"; },
    "your-browser": function(d) { return "seu navegador"; },
    "unit.B": function(d) { return d.size + " bytes"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "Início de sessão"; },
    "category.002": function(d) { return "Cartão de Crédito"; },
    "category.003": function(d) { return "Nota segura"; },
    "category.004": function(d) { return "Identidade"; },
    "category.005": function(d) { return "Senha"; },
    "category.006": function(d) { return "Documento"; },
    "category.100": function(d) { return "Licença de software"; },
    "category.101": function(d) { return "Conta bancária"; },
    "category.102": function(d) { return "Banco de dados"; },
    "category.103": function(d) { return "Carteira de motorista"; },
    "category.104": function(d) { return "Licença de caça"; },
    "category.105": function(d) { return "Subscrição"; },
    "category.106": function(d) { return "Passaporte"; },
    "category.107": function(d) { return "Programa de fidelidade"; },
    "category.108": function(d) { return "Número da previdência social"; },
    "category.109": function(d) { return "Rede WiFi"; },
    "category.110": function(d) { return "Servidor"; },
    "category.111": function(d) { return "Conta de e-mail"; },
    "categories.001": function(d) { return "Logins"; },
    "categories.002": function(d) { return "Cartões de crédito"; },
    "categories.003": function(d) { return "Notas seguras"; },
    "categories.004": function(d) { return "Identidades"; },
    "categories.005": function(d) { return "Senhas"; },
    "categories.006": function(d) { return "Documentos"; },
    "categories.100": function(d) { return "Licenças de software"; },
    "categories.101": function(d) { return "Contas bancárias"; },
    "categories.102": function(d) { return "Bancos de dados"; },
    "categories.103": function(d) { return "Carteiras de motorista"; },
    "categories.104": function(d) { return "Licenças de caça"; },
    "categories.105": function(d) { return "Subscrições"; },
    "categories.106": function(d) { return "Passaportes"; },
    "categories.107": function(d) { return "Programas de fidelidade"; },
    "categories.108": function(d) { return "Números de Previdência Social"; },
    "categories.109": function(d) { return "Redes Wi-Fi"; },
    "categories.110": function(d) { return "Servidores"; },
    "categories.111": function(d) { return "Conta de E-mail"; },
    "button.save": function(d) { return "Guardar"; },
    "button.cancel": function(d) { return "Cancelar"; },
    "unexpected-error": function(d) { return "Ocorreu um erro inesperado. Entre em <NAME_EMAIL>"; }
  },
  ru: {
    "--accountName---was-added-to-1Password-": function(d) { return "Аккаунт «" + d.accountName + "» был добавлен в 1Password."; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "Элемент \"" + d.itemName + "\" был сохранен в сейфе \"" + d.vaultName + "\"."; },
    "-Internal--Report-feedback": function(d) { return "[Внутренняя информация] Сообщить отзыв"; },
    "-accountName--is-offline-": function(d) { return "Аккаунт " + d.accountName + " — не в сети."; },
    "-count--Items-archived": function(d) { return d.count + " элемента(ов) помещены в архив"; },
    "-count--Items-deleted": function(d) { return d.count + " элемента(ов) удалены"; },
    "-count--Items-favorited": function(d) { return "Элементы в списке Избранное: " + d.count; },
    "-count--Items-removed-from-favorites": function(d) { return "Элементы удалены из списка Избранное: " + d.count; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return "Устройство " + d.deviceName + " (около этого места: " + d.location + ") пытается войти в ваш аккаунт " + d.accountName + "."; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return "Устройство " + d.deviceName + " (около этого места: " + d.location + ") пытается войти в ваш аккаунт " + d.accountName + ". Чтобы посмотреть детали, откройте 1Password на панели инструментов своего браузера."; },
    "-fileName--downloaded": function(d) { return "Скачано: " + d.fileName; },
    "-itemType--saved": function(d) { return "Сохранено: " + d.itemType; },
    "1Password-8-Required": function(d) { return "Требуется 1Password 8"; },
    "1Password-account-added": function(d) { return "Аккаунт 1Password добавлен"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password не может открыть базу данных, так как для " + d.browser + " недостаточно места на диске. Для использования 1Password освободите место на диске."; },
    "1Password-is-up-to-date": function(d) { return "Вы используете последнюю версию 1Password"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password больше не будет предлагать автоматически заполнить данные на этой странице.\n\nСписок скрытых страниц запоминается в этом веб-браузере. Очистить список можно в Настройках в любой момент."; },
    "A-database-error-occurred-": function(d) { return "Произошла ошибка базы данных."; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "Сайт добавлен в Watchtower на " + d.count + " ваших аккаунтов."; },
    "API-Key": function(d) { return "API-ключ"; },
    "Add-account-": function(d) { return "Добавить аккаунт…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Все эти элементы будут удалены прямо сейчас. В течение некоторого времени вы еще сможете восстановить их на 1Password.com."; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "При создании замаскированного адреса эл. почты произошла ошибка."; },
    "An-error-occurred--Please-try-again-": function(d) { return "Произошла непредвиденная ошибка. Пожалуйста, попробуйте еще раз."; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "При активации и заполнении замаскированного адреса эл. почты произошла ошибка."; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "Возникла непредвиденная ошибка. Пожалуйста, свяжитесь с нами: <EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "Произошла неизвестная ошибка."; },
    "Any-Website": function(d) { return "Любой веб-сайт"; },
    Archive: function(d) { return "Поместить в архив"; },
    "Archive--count--items-": function(d) { return "Переместить " + d.count + " элементов в архив?"; },
    "Archived---title--": function(d) { return "Помещено в архив: «" + d.title + "»"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "Помещенные в архив элементы не будут отображаться в предложениях по заполнению форм на сайтах или в приложениях."; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "Вы действительно хотите поместить в архив «" + d.itemName + "»?"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "Вы действительно хотите удалить «" + d.itemName + "»?"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "Вы уверены, что хотите удалить " + plural(d.count, 0, ru, { one: number(d.count, "count") + " элемент", few: number(d.count, "count") + " элемента", many: number(d.count, "count") + " элементов", other: number(d.count, "count") + " элемента(ов)" }) + "?"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "Вы уверены, что хотите удалить этот пароль?"; },
    "Authentication-Required": function(d) { return "Требуется аутентификация"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "Истек лимит времени на процесс аутентификации или процесс был прерван. Пожалуйста, попробуйте еще раз."; },
    Benchmarking: function(d) { return "Контрольные параметры"; },
    Cancel: function(d) { return "Отмена"; },
    "Collect-Page-Structure": function(d) { return "Собрать структуру страницы"; },
    "Continue-Signing-In": function(d) { return "Продолжите вход в аккаунт"; },
    "Copied--fieldTitle-": function(d) { return "Скопировано: " + d.fieldTitle; },
    "Copied-field": function(d) { return "Скопированное поле"; },
    "Copied-generated-password": function(d) { return "Скопирован сгенерированный пароль"; },
    "Copied-item-JSON": function(d) { return "Скопирован элемент JSON"; },
    "Copied-item-UUID": function(d) { return "Скопирован UUID элемента"; },
    "Copied-item-link": function(d) { return "Скопирована ссылка на элемент"; },
    "Copied-one-time-password": function(d) { return "Скопирован одноразовый пароль"; },
    "Corporate-Card": function(d) { return "Корпоративная карта"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "Не удалось подключиться к приложению 1Password. Попробуйте разблокировать еще раз."; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "Не удалось завершить аутентификацию. Обновите 1Password для ПК и повторите попытку."; },
    "Create-Brex-vendor-card-": function(d) { return "Создать карту поставщика Brex…"; },
    "Create-Masked-Email-": function(d) { return "Создать замаскированный адрес эл. почты…"; },
    "Create-Privacy-Card-": function(d) { return "Создать карту Privacy…"; },
    "Create-SSH-Key-": function(d) { return "Создать SSH-ключ…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "Для создания и редактирования SSH-ключей требуется 1Password 8."; },
    "Credit-Card": function(d) { return "Банковская карта"; },
    "Credit-card": function(d) { return "Банковская карта"; },
    Delete: function(d) { return "Удалить"; },
    "Deleted---title--": function(d) { return "Удалено: «" + d.title + "»"; },
    "Disable-Extensions": function(d) { return "Отключить расширения"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "Отключить другие версии 1Password в браузере?"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "Отключен автоматический вход в аккаунт на «" + d.title + "»"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "Отключен автоматический вход в аккаунт на элементах " + d.count; },
    "Disk-space-too-low": function(d) { return "На диске слишком мало места"; },
    "Do-you-have-an-existing-account-": function(d) { return "У вас уже есть существующий аккаунт?"; },
    "Don-t-Save-in-1Password": function(d) { return "Не сохранять в 1Password"; },
    "Download-failed": function(d) { return "Ошибка загрузки"; },
    "Duo-Authentication-Required": function(d) { return "Требуется авторизация Duo"; },
    "Email-blocked": function(d) { return "Эл. почта заблокирована"; },
    "Email-unblocked": function(d) { return "Эл. почта разблокирована"; },
    Employee: function(d) { return "Сотрудник(-ца)"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "Включен автоматический вход в аккаунт на «" + d.title + "»"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "Включен автоматический вход в аккаунт на элементах " + d.count; },
    "Enter-Account-Password": function(d) { return "Введите пароль для аккаунта"; },
    "Failed-to-copy-field": function(d) { return "Не удалось скопировать поле"; },
    "Failed-to-delete-password": function(d) { return "Не удалось удалить пароль"; },
    "Favorited---title--": function(d) { return "“" + d.title + "”: добавлено в список Избранное"; },
    "Fill-Masked-Email-": function(d) { return "Ввести замаскированный адрес эл. почты…"; },
    "Get-Help": function(d) { return "Помощь"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "Дополнительное время для переноса метода аутентификации истекло. Пожалуйста, войдите в аккаунт снова."; },
    Help: function(d) { return "Справка"; },
    "Hide-on-this-page": function(d) { return "Скрыть на этой странице"; },
    Identity: function(d) { return "Контакт"; },
    Item: function(d) { return "Элемент"; },
    "Learn-More-": function(d) { return "Подробнее…"; },
    "Link-Existing": function(d) { return "Привязать существующий"; },
    Lock: function(d) { return "Заблокировать"; },
    Login: function(d) { return "Логин"; },
    Managed: function(d) { return "Управляемый"; },
    "New-app-or-browser-found": function(d) { return "Найдено новое приложение или браузер"; },
    "New-item-saved-in-1Password-": function(d) { return "Новый элемент сохранен в 1Password."; },
    "No-QR-code-found-for-2FA": function(d) { return "QR код для двухфакторной аутентификации не найден"; },
    "No-accounts-found-": function(d) { return "Аккаунты не найдены."; },
    OK: function(d) { return "OK"; },
    Ok: function(d) { return "ОК"; },
    Personal: function(d) { return "Личный"; },
    "Physical-Card": function(d) { return "Физическая карта"; },
    Private: function(d) { return "Частный"; },
    "Removed---title---from-favorites": function(d) { return "“" + d.title + "”: удален из списка Избранное"; },
    "Report-Issue": function(d) { return "Сообщить о проблеме"; },
    "Report-error-": function(d) { return "Отчет об ошибке…"; },
    Save: function(d) { return "Сохранить"; },
    Security: function(d) { return "Безопасность"; },
    Settings: function(d) { return "Настройки"; },
    Shared: function(d) { return "Общий"; },
    "Sign-Up": function(d) { return "Регистрация"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "Войдите в свой аккаунт 1Password (" + d.accountName + ") и подтвердите в Duo, чтобы продолжить."; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "Войдите в свой аккаунт 1Password (" + d.accountName + "), чтобы продолжить."; },
    "Sign-in-with--authProvider-": function(d) { return "Войти в систему c " + d.authProvider; },
    "Sign-in-with-passkey": function(d) { return "Войдите в аккаунт с passkey"; },
    "Stay-Offline": function(d) { return "Использовать в режиме офлайн"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "Благодарим за использование новейшей версии (" + d.version + ") 1Password!"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "Это не сработало. Проверьте свой пароль и попробуйте еще раз."; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "Эта карта заблокирована/ограничена. Пожалуйста, используйте другую карту."; },
    "This-entry-will-be-removed-immediately-": function(d) { return "Эта запись будет немедленно удалена."; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "Этот элемент будет немедленно удален.Некоторое время элементы еще можно будет восстановить на 1Password.com."; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "Чтобы сохранить элемент в аккаунте, вам необходимо связать его с ранее выбранным аккаунтом замаскированной эл. почты"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "Сенсор Touch ID в настоящее время недоступен."; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "Сенсор Touch ID в настоящее время недоступен. Вам необходимо войти в систему с помощью пароля от аккаунта."; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "Невозможно добавить к элементу поле для одноразового пароля"; },
    "Unable-to-copy-generated-password": function(d) { return "Не удается скопировать сгенерированный пароль"; },
    "Unable-to-fill-generated-password": function(d) { return "Не удается ввести сгенерированный пароль"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "Невозможно сгенерировать и скопировать одноразовый пароль"; },
    "Unable-to-save-generated-password": function(d) { return "Не удается сохранить сгенерированный пароль"; },
    "Unable-to-unlock-account-": function(d) { return "Разблокировать аккаунт не удалось."; },
    "Unknown-Device": function(d) { return "Неизвестное устройство"; },
    Unlock: function(d) { return "Разблокировать"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "Разблокировать аккаунт из приложения для ПК"; },
    "Use-Suggested-Password": function(d) { return "Использовать предложенный пароль"; },
    "Vendor-Card": function(d) { return "Карта поставщика"; },
    "Watchtower-Alert": function(d) { return "Предупреждение от Watchtower"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "Мы обнаружили другие установленные версии этого расширения. Хотите отключить расширение " + d.extensionNames + "?"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "Вы можете связать существующий семейный аккаунт или создать новый."; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "Вам будет предложено повторно ввести свой пароль, потому что для браузера " + d.browserName + " доступно обновление."; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "Вы не в сети. Чтобы перейти в свой аккаунт, проверьте подключение к Интернету и повторите попытку."; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "Вы достигли дневного лимита на создание замаскированных адресов эл. почты для выбранного аккаунта."; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "Вы достигли лимита на создание замаскированных адресов эл. почты для выбранного аккаунта."; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "Вы не сможете сохранить изменения, пока не войдете в аккаунт и не подключитесь к сети."; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "Вы не сможете сохранить изменения, пока не войдете в аккаунт."; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "Вы не сможете использовать сгенерированный пароль, пока не войдете в аккаунт и не подключитесь к сети."; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "Доступ к вашему аккаунту приостановлен. Проверьте свою эл. почту для получения более подробной информации."; },
    "Your-account-is-offline": function(d) { return "Ваш аккаунт не в сети"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "Действие вашего аккаунта было приостановлено. Пожалуйста, свяжитесь с организатором семейного аккаунта или администратором команды для получения подробной информации."; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "Ваш аккаунт не подтвержден. Войдите в Fastmail, чтобы подтвердить выбранный аккаунт."; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "Время сеанса истекло. Необходимо снова войти в аккаунт 1Password в браузере для аутентификации."; },
    "one-time-password": function(d) { return "одноразовый пароль"; },
    "your-browser": function(d) { return "ваш браузер"; },
    "unit.B": function(d) { return d.size + " B"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "Логин"; },
    "category.002": function(d) { return "Банковская карта"; },
    "category.003": function(d) { return "Защищённая заметка"; },
    "category.004": function(d) { return "Личность"; },
    "category.005": function(d) { return "Пароль"; },
    "category.006": function(d) { return "Документ"; },
    "category.100": function(d) { return "Лицензия на ПО"; },
    "category.101": function(d) { return "Банковский счёт"; },
    "category.102": function(d) { return "База данных"; },
    "category.103": function(d) { return "Водительские права"; },
    "category.104": function(d) { return "Охотничья лицензия"; },
    "category.105": function(d) { return "Членство"; },
    "category.106": function(d) { return "Паспорт"; },
    "category.107": function(d) { return "Программа лояльности"; },
    "category.108": function(d) { return "Номер социального страхования"; },
    "category.109": function(d) { return "Сеть Wi-Fi"; },
    "category.110": function(d) { return "Сервер"; },
    "category.111": function(d) { return "Учётная запись эл. почты"; },
    "categories.001": function(d) { return "Логины"; },
    "categories.002": function(d) { return "Банковские карты"; },
    "categories.003": function(d) { return "Защищённые заметки"; },
    "categories.004": function(d) { return "Личности"; },
    "categories.005": function(d) { return "Пароли"; },
    "categories.006": function(d) { return "Документы"; },
    "categories.100": function(d) { return "Лицензии на ПО"; },
    "categories.101": function(d) { return "Банковские счета"; },
    "categories.102": function(d) { return "Базы данных"; },
    "categories.103": function(d) { return "Водительские права"; },
    "categories.104": function(d) { return "Охотничьи лицензии"; },
    "categories.105": function(d) { return "Членства"; },
    "categories.106": function(d) { return "Паспорта"; },
    "categories.107": function(d) { return "Программы лояльности"; },
    "categories.108": function(d) { return "Номера социального страхования"; },
    "categories.109": function(d) { return "Сети Wi-Fi"; },
    "categories.110": function(d) { return "Серверы"; },
    "categories.111": function(d) { return "Учётные записи эл. почты"; },
    "button.save": function(d) { return "Сохранить"; },
    "button.cancel": function(d) { return "Отмена"; },
    "unexpected-error": function(d) { return "Возникла непредвиденная ошибка. Пожалуйста, свяжитесь с нами по адресу <EMAIL>"; }
  },
  "zh-CN": {
    "--accountName---was-added-to-1Password-": function(d) { return "“" + d.accountName + "”被添加到 1Password 中。"; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "“" + d.itemName + "”被保存在“" + d.vaultName + "”中。"; },
    "-Internal--Report-feedback": function(d) { return "[内部] 报告反馈"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " 已离线。"; },
    "-count--Items-archived": function(d) { return d.count + " 个项目已存档"; },
    "-count--Items-deleted": function(d) { return d.count + " 个项目已删除"; },
    "-count--Items-favorited": function(d) { return d.count + " 个项目已收藏"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " 个项目已移出收藏"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return "在 " + d.location + " 附近的 " + d.deviceName + " 正尝试登录你的 " + d.accountName + " 帐户。"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return "在 " + d.location + " 附近的 " + d.deviceName + " 正尝试登录你的 " + d.accountName + " 帐户。在浏览器工具栏中打开 1Password 以查看。"; },
    "-fileName--downloaded": function(d) { return d.fileName + " 已下载"; },
    "-itemType--saved": function(d) { return d.itemType + " 已保存"; },
    "1Password-8-Required": function(d) { return "需要 1Password 8"; },
    "1Password-account-added": function(d) { return "1Password 帐户已添加"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password 无法打开数据库，因为 " + d.browser + " 可用的磁盘空间不足。要继续使用 1Password，请释放更多空间。"; },
    "1Password-is-up-to-date": function(d) { return "1Password 已是最新版本"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password 将不再在此页面提供自动填充建议。\n\n此网络浏览器会记住隐藏页面的列表。您可以随时在设置中清除该列表。"; },
    "A-database-error-occurred-": function(d) { return "发生数据库错误。"; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "一个网站已添加到您 " + d.count + " 个帐户的瞭望塔中。"; },
    "API-Key": function(d) { return "API Key"; },
    "Add-account-": function(d) { return "添加帐户…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "所有这些项目将被立即移除。您可以在一定时间内从 1Password.com 恢复项目。"; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "生成此蒙面电子邮件时发生错误。"; },
    "An-error-occurred--Please-try-again-": function(d) { return "发生错误。请重试。"; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "启用并填充蒙面电子邮件时发生错误。"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "发生意外错误。请联系 <EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "发生未知错误。"; },
    "Any-Website": function(d) { return "任何网站"; },
    Archive: function(d) { return "归档"; },
    "Archive--count--items-": function(d) { return "归档 " + d.count + " 个项目？"; },
    "Archived---title--": function(d) { return "已存档“" + d.title + "”"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "存档的项目不会在网站和应用的填写建议中显示。"; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "您确定要存档“" + d.itemName + "”吗？"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "您确定要删除“" + d.itemName + "”吗？"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "确定要删除 " + plural(d.count, 0, en, { other: number(d.count, "count") + " 个项目" }) + "吗？"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "确定删除该密码？"; },
    "Authentication-Required": function(d) { return "需要身份验证"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "验证中断或超时。请再试一次。"; },
    Benchmarking: function(d) { return "基准化分析"; },
    Cancel: function(d) { return "取消"; },
    "Collect-Page-Structure": function(d) { return "收集页面结构"; },
    "Continue-Signing-In": function(d) { return "继续登录"; },
    "Copied--fieldTitle-": function(d) { return "已复制 " + d.fieldTitle; },
    "Copied-field": function(d) { return "复制的字段"; },
    "Copied-generated-password": function(d) { return "已复制生成的密码"; },
    "Copied-item-JSON": function(d) { return "已复制项目 JSON"; },
    "Copied-item-UUID": function(d) { return "已复制项目 UUID"; },
    "Copied-item-link": function(d) { return "已复制项目链接"; },
    "Copied-one-time-password": function(d) { return "已复制一次性密码"; },
    "Corporate-Card": function(d) { return "公司卡"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "未能连接到 1Password 应用。请再次尝试解锁。"; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "未能完成验证。请更新桌面版 1Password 后再试一次。"; },
    "Create-Brex-vendor-card-": function(d) { return "创建 Brex 供应商卡片…"; },
    "Create-Masked-Email-": function(d) { return "创建蒙面电子邮件…"; },
    "Create-Privacy-Card-": function(d) { return "创建 Privacy 卡片…"; },
    "Create-SSH-Key-": function(d) { return "创建 SSH 密钥…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "创建和编辑 SSH 密钥需要 1Password 8。"; },
    "Credit-Card": function(d) { return "信用卡"; },
    "Credit-card": function(d) { return "信用卡"; },
    Delete: function(d) { return "删除"; },
    "Deleted---title--": function(d) { return "已删除“" + d.title + "”"; },
    "Disable-Extensions": function(d) { return "禁用扩展"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "在浏览器中禁用其他版本的 1Password 吗？"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "已禁用“" + d.title + "”的自动登录功能"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "已禁用 " + d.count + " 个项目的自动登录功能"; },
    "Disk-space-too-low": function(d) { return "磁盘空间不足"; },
    "Do-you-have-an-existing-account-": function(d) { return "你是否拥有已存在的帐户？"; },
    "Don-t-Save-in-1Password": function(d) { return "不要在 1Password 中保存"; },
    "Download-failed": function(d) { return "下载失败"; },
    "Duo-Authentication-Required": function(d) { return "需要 Duo 验证"; },
    "Email-blocked": function(d) { return "电子邮件已被封锁"; },
    "Email-unblocked": function(d) { return "电子邮件已解封"; },
    Employee: function(d) { return "员工"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "已启用“" + d.title + "”的自动登录功能"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "已启用 " + d.count + " 个项目的自动登录功能"; },
    "Enter-Account-Password": function(d) { return "输入帐户密码"; },
    "Failed-to-copy-field": function(d) { return "复制字段失败"; },
    "Failed-to-delete-password": function(d) { return "删除密码失败"; },
    "Favorited---title--": function(d) { return "已收藏“" + d.title + "”"; },
    "Fill-Masked-Email-": function(d) { return "填充蒙面电子邮件…"; },
    "Get-Help": function(d) { return "获取帮助"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "身份验证方式迁移的宽限期已过。请重新登录。"; },
    Help: function(d) { return "帮助"; },
    "Hide-on-this-page": function(d) { return "在此页面隐藏"; },
    Identity: function(d) { return "身份标识"; },
    Item: function(d) { return "项目"; },
    "Learn-More-": function(d) { return "了解更多…"; },
    "Link-Existing": function(d) { return "链接现有"; },
    Lock: function(d) { return "锁定"; },
    Login: function(d) { return "登录信息"; },
    Managed: function(d) { return "管理的"; },
    "New-app-or-browser-found": function(d) { return "找到新应用或浏览器"; },
    "New-item-saved-in-1Password-": function(d) { return "1Password 保存了新项目。"; },
    "No-QR-code-found-for-2FA": function(d) { return "未找到两步验证的二维码"; },
    "No-accounts-found-": function(d) { return "未找到帐户。"; },
    OK: function(d) { return "好"; },
    Ok: function(d) { return "好"; },
    Personal: function(d) { return "个人"; },
    "Physical-Card": function(d) { return "实体卡片"; },
    Private: function(d) { return "私人"; },
    "Removed---title---from-favorites": function(d) { return "已将“" + d.title + "”移出收藏"; },
    "Report-Issue": function(d) { return "报告问题"; },
    "Report-error-": function(d) { return "报告错误…"; },
    Save: function(d) { return "保存"; },
    Security: function(d) { return "安全"; },
    Settings: function(d) { return "设置"; },
    Shared: function(d) { return "共享"; },
    "Sign-Up": function(d) { return "注册"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "登录您的 1Password 帐户（" + d.accountName + "）并通过 Duo 验证以继续。"; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "登录您的 1Password 帐户（" + d.accountName + "）以继续。"; },
    "Sign-in-with--authProvider-": function(d) { return "通过 " + d.authProvider + " 登录"; },
    "Sign-in-with-passkey": function(d) { return "通过密钥登录"; },
    "Stay-Offline": function(d) { return "保持离线"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "感谢您使用 1Password 的最新版本（" + d.version + "）！"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "此操作无效。请更改密码并重试。"; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "这张卡片已被锁定/停用，请使用其他卡片。"; },
    "This-entry-will-be-removed-immediately-": function(d) { return "此条目将被立即移除。"; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "此项目将被立即移除。您可以在一定时间内从 1Password.com 恢复项目。"; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "要在此帐户中保存项目，你需要将其链接到之前选择的蒙面电子邮件帐户"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "触控 ID 传感器当前不可用。"; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "触控 ID 传感器当前不可用。您需要使用帐户密码登录。"; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "未能向项目添加一次性密码字段"; },
    "Unable-to-copy-generated-password": function(d) { return "无法复制生成的密码"; },
    "Unable-to-fill-generated-password": function(d) { return "无法填充生成的密码"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "未能生成并复制一次性密码"; },
    "Unable-to-save-generated-password": function(d) { return "无法保存生成的密码"; },
    "Unable-to-unlock-account-": function(d) { return "无法解锁帐户。"; },
    "Unknown-Device": function(d) { return "未知设备"; },
    Unlock: function(d) { return "解锁"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "从桌面应用解锁帐户"; },
    "Use-Suggested-Password": function(d) { return "使用建议的密码"; },
    "Vendor-Card": function(d) { return "供应商卡片"; },
    "Watchtower-Alert": function(d) { return "瞭望塔警报"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "检测到已安装此扩展的其他版本。是否要禁用 " + d.extensionNames + "？"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "你可以选择与已存在的家庭版帐户链接，或创建新帐户。"; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "你被要求再次输入密码，因为 " + d.browserName + " 有可用更新。"; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "您处于离线状态。要访问您的帐户，请检查您的互联网连接并重试。"; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "您已达到为所选帐户创建蒙面电子邮件的每日限制。"; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "您已达到为所选帐户创建蒙面电子邮件的数量限制。"; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "你在登录并上线之前将无法更改此项目。"; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "你在完成登录之前将无法保存更改。"; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "在登录并上线之前你无法使用生成的密码。"; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "你的帐户访问权已被暂停。请查看电子邮件了解详情。"; },
    "Your-account-is-offline": function(d) { return "您的帐户已离线"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "你的帐户已暂停。请联系家庭组织者或团队管理员以了解详情。"; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "你的帐户未经验证。请登录 Fastmail 验证选择的帐户。"; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "会话已超时。浏览器中的 1Password 要求你重新登录以验证身份。"; },
    "one-time-password": function(d) { return "一次性密码"; },
    "your-browser": function(d) { return "你的浏览器"; },
    "unit.B": function(d) { return d.size + " 字节"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "登录"; },
    "category.002": function(d) { return "信用卡"; },
    "category.003": function(d) { return "安全备注"; },
    "category.004": function(d) { return "身份标识"; },
    "category.005": function(d) { return "密码："; },
    "category.006": function(d) { return "文档"; },
    "category.100": function(d) { return "软件许可证"; },
    "category.101": function(d) { return "银行账户"; },
    "category.102": function(d) { return "数据库"; },
    "category.103": function(d) { return "驾驶执照"; },
    "category.104": function(d) { return "户外许可证"; },
    "category.105": function(d) { return "会员"; },
    "category.106": function(d) { return "护照"; },
    "category.107": function(d) { return "奖励活动"; },
    "category.108": function(d) { return "社会保险号码"; },
    "category.109": function(d) { return "WiFi 网络"; },
    "category.110": function(d) { return "服务器"; },
    "category.111": function(d) { return "电子邮件账户"; },
    "categories.001": function(d) { return "登录信息"; },
    "categories.002": function(d) { return "信用卡"; },
    "categories.003": function(d) { return "安全备注"; },
    "categories.004": function(d) { return "身份标识"; },
    "categories.005": function(d) { return "密码"; },
    "categories.006": function(d) { return "文档"; },
    "categories.100": function(d) { return "软件许可"; },
    "categories.101": function(d) { return "银行账户"; },
    "categories.102": function(d) { return "数据库"; },
    "categories.103": function(d) { return "驾驶执照"; },
    "categories.104": function(d) { return "户外许可证"; },
    "categories.105": function(d) { return "会员信息"; },
    "categories.106": function(d) { return "护照"; },
    "categories.107": function(d) { return "奖励活动"; },
    "categories.108": function(d) { return "社会保险号码"; },
    "categories.109": function(d) { return "WiFi 网络"; },
    "categories.110": function(d) { return "服务器"; },
    "categories.111": function(d) { return "电子邮件账户"; },
    "button.save": function(d) { return "保存"; },
    "button.cancel": function(d) { return "取消"; },
    "unexpected-error": function(d) { return "发生意外错误。请联系 <EMAIL>"; }
  },
  "zh-TW": {
    "--accountName---was-added-to-1Password-": function(d) { return "「" + d.accountName + "」被添加到 1Password 中。"; },
    "--itemName---was-saved-in---vaultName---": function(d) { return "「" + d.itemName + "」被儲存在「" + d.vaultName + "」中。"; },
    "-Internal--Report-feedback": function(d) { return "[內部] 報告回饋"; },
    "-accountName--is-offline-": function(d) { return d.accountName + " 已離線。"; },
    "-count--Items-archived": function(d) { return d.count + " 個項目已封存"; },
    "-count--Items-deleted": function(d) { return d.count + " 個項目已刪除"; },
    "-count--Items-favorited": function(d) { return d.count + " 個項目已加入最愛"; },
    "-count--Items-removed-from-favorites": function(d) { return d.count + " 個項目已從最愛中移除"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account-": function(d) { return "在 " + d.location + " 附近的 " + d.deviceName + " 正嘗試登入你的 " + d.accountName + " 帳號。"; },
    "-deviceName--near--location--is-trying-to-sign-in-to-your--accountName--account--Open-1Password-in-your-browser-s-toolbar-to-view-": function(d) { return "在 " + d.location + " 附近的 " + d.deviceName + " 正嘗試登入你的 " + d.accountName + " 帳號。在瀏覽器工具列中開啟 1Password 以檢視。"; },
    "-fileName--downloaded": function(d) { return d.fileName + " 已下載"; },
    "-itemType--saved": function(d) { return d.itemType + " 已儲存"; },
    "1Password-8-Required": function(d) { return "需要 1Password 8"; },
    "1Password-account-added": function(d) { return "1Password 帳號已添加"; },
    "1Password-can-t-open-its-database-because-there-isn-t-enough-disk-space-available-for--browser---To-use-1Password--free-up-some-space-": function(d) { return "1Password 無法打開資料庫，因為 " + d.browser + " 可用的磁碟空間不足。要繼續使用 1Password，請釋放更多空間。"; },
    "1Password-is-up-to-date": function(d) { return "1Password 已是最新版本"; },
    "1Password-will-no-longer-make-autofill-suggestions-on-this-page---The-list-of-hidden-pages-is-remembered-in-this-web-browser--You-can-clear-the-list-at-any-time-in-settings-": function(d) { return "1Password 將不再在此頁面提供自動填入建議。\n\n此網頁瀏覽器會記住隱藏頁面的清單。您可以隨時在設定中清除該清單。"; },
    "A-database-error-occurred-": function(d) { return "發生資料庫錯誤。"; },
    "A-website-has-been-added-to-Watchtower-in--count--of-your-accounts-": function(d) { return "已將網站新增至您 " + d.count + " 個帳號的 Watchtower。"; },
    "API-Key": function(d) { return "API Key"; },
    "Add-account-": function(d) { return "新增帳號…"; },
    "All-of-these-items-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "所有項目將被立即移除。您可以在一定時間內從 1Password.com 還原項目。"; },
    "An-error-has-occurred-while-generating-the-Masked-Email-": function(d) { return "產生此蒙面電子郵件時發生錯誤。"; },
    "An-error-occurred--Please-try-again-": function(d) { return "發生錯誤。請重試。"; },
    "An-error-occurred-while-enabling-and-filling-the-Masked-Email-": function(d) { return "啟用並填充蒙面電子郵件時發生錯誤。"; },
    "An-unexpected-error-occurred--Please-contact-support-1password-com": function(d) { return "發生意外錯誤。請聯繫 <EMAIL>"; },
    "An-unknown-error-has-occurred-": function(d) { return "發生未知錯誤。"; },
    "Any-Website": function(d) { return "任何網站"; },
    Archive: function(d) { return "封存"; },
    "Archive--count--items-": function(d) { return "封存 " + d.count + " 個項目嗎？"; },
    "Archived---title--": function(d) { return "已封存「" + d.title + "」"; },
    "Archived-items-won-t-show-up-in-filling-suggestions-for-websites-or-in-apps-": function(d) { return "封存的項目不會在網站和應用程式的填寫建議中顯示。"; },
    "Are-you-sure-you-want-to-archive---itemName---": function(d) { return "您確定要封存「" + d.itemName + "」嗎？"; },
    "Are-you-sure-you-want-to-delete---itemName---": function(d) { return "確定要刪除「" + d.itemName + "」嗎？"; },
    "Are-you-sure-you-want-to-delete--count--plural--one----item--other----items---": function(d) { return "確定要刪除 " + plural(d.count, 0, en, { other: number(d.count, "count") + " 個項目" }) + "嗎？"; },
    "Are-you-sure-you-want-to-delete-this-password-": function(d) { return "您確定要刪除此密碼嗎？"; },
    "Authentication-Required": function(d) { return "需要身分驗證"; },
    "Authentication-was-interrupted-or-timed-out--Please-try-again-": function(d) { return "驗證中斷或超時。請再試一次。"; },
    Benchmarking: function(d) { return "基準化分析"; },
    Cancel: function(d) { return "取消"; },
    "Collect-Page-Structure": function(d) { return "收集頁面結構"; },
    "Continue-Signing-In": function(d) { return "繼續登入"; },
    "Copied--fieldTitle-": function(d) { return "已複製 " + d.fieldTitle; },
    "Copied-field": function(d) { return "複製的欄位"; },
    "Copied-generated-password": function(d) { return "已複製產生的密碼"; },
    "Copied-item-JSON": function(d) { return "已複製項目 JSON"; },
    "Copied-item-UUID": function(d) { return "已複製項目 UUID"; },
    "Copied-item-link": function(d) { return "已複製項目連結"; },
    "Copied-one-time-password": function(d) { return "已複製一次性密碼"; },
    "Corporate-Card": function(d) { return "公司卡"; },
    "Could-not-connect-to-the-1Password-App--Try-unlocking-again-": function(d) { return "未能連接到 1Password 應用程式。請再次嘗試解鎖。"; },
    "Couldn-t-complete-authentication--Update-1Password-for-desktop-and-try-again-": function(d) { return "未能完成驗證。請更新桌面版 1Password 後再試一次。"; },
    "Create-Brex-vendor-card-": function(d) { return "建立 Brex 供應商卡片…"; },
    "Create-Masked-Email-": function(d) { return "建立蒙面電子郵件…"; },
    "Create-Privacy-Card-": function(d) { return "建立 Privacy 卡片…"; },
    "Create-SSH-Key-": function(d) { return "建立 SSH 金鑰…"; },
    "Creating-and-editing-SSH-keys-requires-1Password-8-": function(d) { return "建立和編輯 SSH 金鑰需要 1Password 8。"; },
    "Credit-Card": function(d) { return "信用卡"; },
    "Credit-card": function(d) { return "信用卡"; },
    Delete: function(d) { return "刪除"; },
    "Deleted---title--": function(d) { return "已刪除「" + d.title + "」"; },
    "Disable-Extensions": function(d) { return "停用擴充元件"; },
    "Disable-other-versions-of-1Password-in-the-browser-": function(d) { return "在瀏覽器中停用其他版本的 1Password 嗎？"; },
    "Disabled-sign-in-automatically-on---title--": function(d) { return "已停用「" + d.title + "」的自動登入"; },
    "Disabled-sign-in-automatically-on--count--items": function(d) { return "已停用 " + d.count + " 個項目的自動登入"; },
    "Disk-space-too-low": function(d) { return "磁碟空間不足"; },
    "Do-you-have-an-existing-account-": function(d) { return "你是否擁有已存在的帳號？"; },
    "Don-t-Save-in-1Password": function(d) { return "不要在 1Password 中儲存"; },
    "Download-failed": function(d) { return "下載失敗"; },
    "Duo-Authentication-Required": function(d) { return "需要 Duo 驗證"; },
    "Email-blocked": function(d) { return "電子郵件已被封鎖"; },
    "Email-unblocked": function(d) { return "電子郵件已解封"; },
    Employee: function(d) { return "員工"; },
    "Enabled-sign-in-automatically-on---title--": function(d) { return "已開啟「" + d.title + "」的自動登入"; },
    "Enabled-sign-in-automatically-on--count--items": function(d) { return "已開啟 " + d.count + " 個項目的自動登入"; },
    "Enter-Account-Password": function(d) { return "輸入帳號密碼"; },
    "Failed-to-copy-field": function(d) { return "複製欄位失敗"; },
    "Failed-to-delete-password": function(d) { return "刪除密碼失敗"; },
    "Favorited---title--": function(d) { return "已將「" + d.title + "」加入最愛"; },
    "Fill-Masked-Email-": function(d) { return "填充蒙面電子郵件…"; },
    "Get-Help": function(d) { return "取得說明"; },
    "Grace-period-for-authentication-method-migration-has-expired--Please-sign-in-again-": function(d) { return "身份驗證方式移轉的寬限期已過。請重新登入。"; },
    Help: function(d) { return "説明"; },
    "Hide-on-this-page": function(d) { return "在此頁面上隱藏"; },
    Identity: function(d) { return "身份認證"; },
    Item: function(d) { return "項目"; },
    "Learn-More-": function(d) { return "進一步瞭解⋯"; },
    "Link-Existing": function(d) { return "連結現有文件"; },
    Lock: function(d) { return "上鎖"; },
    Login: function(d) { return "登入資訊"; },
    Managed: function(d) { return "管理的"; },
    "New-app-or-browser-found": function(d) { return "找到新應用程式或瀏覽器"; },
    "New-item-saved-in-1Password-": function(d) { return "1Password 儲存了新項目。"; },
    "No-QR-code-found-for-2FA": function(d) { return "未找到雙因素認證的 QR Code"; },
    "No-accounts-found-": function(d) { return "未找到帳號。"; },
    OK: function(d) { return "好"; },
    Ok: function(d) { return "好"; },
    Personal: function(d) { return "個人"; },
    "Physical-Card": function(d) { return "實體卡片"; },
    Private: function(d) { return "私人"; },
    "Removed---title---from-favorites": function(d) { return "已將「" + d.title + "」從最愛中移除"; },
    "Report-Issue": function(d) { return "回報問題"; },
    "Report-error-": function(d) { return "報告錯誤…"; },
    Save: function(d) { return "儲存"; },
    Security: function(d) { return "安全"; },
    Settings: function(d) { return "設定"; },
    Shared: function(d) { return "共用"; },
    "Sign-Up": function(d) { return "註冊"; },
    "Sign-in-to-your-1Password-account---accountName---and-authenticate-with-Duo-to-continue-": function(d) { return "登入您的 1Password 帳號（" + d.accountName + "）並透過 Duo 驗證以繼續。"; },
    "Sign-in-to-your-1Password-account---accountName---to-continue-": function(d) { return "登入您的 1Password 帳號（" + d.accountName + "）以繼續。"; },
    "Sign-in-with--authProvider-": function(d) { return "透過 " + d.authProvider + " 登入"; },
    "Sign-in-with-passkey": function(d) { return "透過通行密鑰登入"; },
    "Stay-Offline": function(d) { return "保持離線"; },
    "Thank-you-for-running-the-latest-version---version---of-1Password-": function(d) { return "感謝您使用 1Password 的最新版本（" + d.version + "）！"; },
    "That-didn-t-work--Check-your-password-and-try-again-": function(d) { return "此動作無效。請變更密碼並重試。"; },
    "This-card-has-been-locked-terminated--please-use-another-card-": function(d) { return "這張卡片已被鎖定/停用，請使用其他卡片。"; },
    "This-entry-will-be-removed-immediately-": function(d) { return "此條目將被立即移除。"; },
    "This-item-will-be-removed-immediately--You-can-recover-items-for-a-limited-time-on-1Password-com-": function(d) { return "此項目將被立即移除。您可以在一定時間內從 1Password.com 還原項目。"; },
    "To-save-an-item-in-this-account-you-need-to-link-it-to-the-previously-selected-Masked-Email-account": function(d) { return "要在此帳號中儲存項目，你需要將其連結到之前選擇的蒙面電子郵件帳號"; },
    "Touch-ID-sensor-is-currently-unavailable-": function(d) { return "Touch ID 感應器當前不可用。"; },
    "Touch-ID-sensor-is-currently-unavailable--You-need-to-sign-in-using-your-account-password-": function(d) { return "Touch ID 感應器當前不可用。您需要使用帳號密碼登入。"; },
    "Unable-to-add-one-time-password-field-to-item": function(d) { return "未能向項目添加一次性密碼欄位"; },
    "Unable-to-copy-generated-password": function(d) { return "無法複製產生的密碼"; },
    "Unable-to-fill-generated-password": function(d) { return "無法填入產生的密碼"; },
    "Unable-to-generate-and-copy-one-time-password": function(d) { return "未能產生並複制一次性密碼"; },
    "Unable-to-save-generated-password": function(d) { return "無法儲存產生的密碼"; },
    "Unable-to-unlock-account-": function(d) { return "無法解鎖帳號。"; },
    "Unknown-Device": function(d) { return "未知裝置"; },
    Unlock: function(d) { return "解鎖"; },
    "Unlock-account-from-the-desktop-app": function(d) { return "從桌上應用程式解鎖帳號"; },
    "Use-Suggested-Password": function(d) { return "使用推薦的密碼"; },
    "Vendor-Card": function(d) { return "供應商卡片"; },
    "Watchtower-Alert": function(d) { return "Watchtower 警告"; },
    "We-have-detected-other-versions-of-this-extension-installed--Would-you-like-to-disable--extensionNames--": function(d) { return "偵測到已安裝此擴充元件的其他版本。是否要停用 " + d.extensionNames + "？"; },
    "You-can-choose-to-link-an-existing-family-account-or-create-a-new-one-": function(d) { return "你可以選擇與已存在的家庭版帳號連結，或建立新帳號。"; },
    "You-re-asked-to-re-enter-your-password-because--browserName--has-an-update-available-": function(d) { return "你被要求再次輸入密碼，因為 " + d.browserName + " 有可用更新。"; },
    "You-re-offline--To-access-your-account--check-your-internet-connection-and-try-again-": function(d) { return "您處於離線狀態。要存取您的帳號，請檢查您的網路連線並重試。"; },
    "You-ve-reached-the-daily-limit-for-Masked-Email-creation-for-the-selected-account-": function(d) { return "您已達到為所選帳號建立蒙面電子郵件的每日限制。"; },
    "You-ve-reached-the-maximum-Masked-Email-creation-quota-for-the-selected-account-": function(d) { return "您已達到為所選帳號建立蒙面電子郵件的數量限制。"; },
    "You-won-t-be-able-to-save-changes-until-you-are-signed-in-and-online-": function(d) { return "你在登入並上線之前將無法變更此項目。"; },
    "You-won-t-be-able-to-save-changes-until-you-finish-signing-in-": function(d) { return "你在完成登入之前將無法儲存變更。"; },
    "You-won-t-be-able-to-use-a-generated-password-until-you-are-signed-in-and-online-": function(d) { return "在登入並上線之前你無法使用產生的密碼。"; },
    "Your-account-access-has-been-temporarily-paused--Check-your-email-for-more-details-": function(d) { return "你的帳號存取權已被暫停。請查閱電子郵件瞭解詳細資訊。"; },
    "Your-account-is-offline": function(d) { return "您的帳號已離線"; },
    "Your-account-is-suspended--Contact-your-family-organizer-or-team-administrator-for-more-information-": function(d) { return "你的帳號已暫停。請聯絡家庭組織者或團隊管理員以獲得更多資訊。"; },
    "Your-account-is-unverified--Sign-in-to-Fastmail-to-verify-the-selected-account-": function(d) { return "你的帳號未經驗證。請登入到 Fastmail 驗證選擇的帳號。"; },
    "Your-session-has-expired--1Password-in-the-browser-would-like-you-to-sign-in-again-to-authenticate-": function(d) { return "工作階段已過期。瀏覽器中的 1Password 要求你重新登入以驗證身份。"; },
    "one-time-password": function(d) { return "一次性密碼"; },
    "your-browser": function(d) { return "你的瀏覽器"; },
    "unit.B": function(d) { return d.size + " B"; },
    "unit.KB": function(d) { return d.size + " KB"; },
    "unit.MB": function(d) { return d.size + " MB"; },
    "unit.GB": function(d) { return d.size + " GB"; },
    "unit.TB": function(d) { return d.size + " TB"; },
    "unit.PB": function(d) { return d.size + " PB"; },
    "category.001": function(d) { return "登入"; },
    "category.002": function(d) { return "信用卡"; },
    "category.003": function(d) { return "安全備忘錄"; },
    "category.004": function(d) { return "身分認證"; },
    "category.005": function(d) { return "密碼"; },
    "category.006": function(d) { return "文件"; },
    "category.100": function(d) { return "軟體授權"; },
    "category.101": function(d) { return "銀行帳號"; },
    "category.102": function(d) { return "資料庫"; },
    "category.103": function(d) { return "駕駛執照"; },
    "category.104": function(d) { return "戶外許可標誌"; },
    "category.105": function(d) { return "會員資格"; },
    "category.106": function(d) { return "護照"; },
    "category.107": function(d) { return "積點回饋"; },
    "category.108": function(d) { return "社會保險號碼"; },
    "category.109": function(d) { return "WiFi 網路"; },
    "category.110": function(d) { return "伺服器"; },
    "category.111": function(d) { return "電子郵件帳號"; },
    "categories.001": function(d) { return "登入資訊"; },
    "categories.002": function(d) { return "信用卡"; },
    "categories.003": function(d) { return "安全備忘錄"; },
    "categories.004": function(d) { return "身分認證"; },
    "categories.005": function(d) { return "密碼"; },
    "categories.006": function(d) { return "文件"; },
    "categories.100": function(d) { return "軟體授權"; },
    "categories.101": function(d) { return "銀行帳號"; },
    "categories.102": function(d) { return "資料庫"; },
    "categories.103": function(d) { return "駕駛執照"; },
    "categories.104": function(d) { return "戶外許可標誌"; },
    "categories.105": function(d) { return "會員資格"; },
    "categories.106": function(d) { return "護照"; },
    "categories.107": function(d) { return "積點回饋"; },
    "categories.108": function(d) { return "社會保險號碼"; },
    "categories.109": function(d) { return "WiFi 網路"; },
    "categories.110": function(d) { return "伺服器"; },
    "categories.111": function(d) { return "電子郵件帳號"; },
    "button.save": function(d) { return "儲存"; },
    "button.cancel": function(d) { return "取消"; },
    "unexpected-error": function(d) { return "發生意外錯誤。請聯繫 <EMAIL>"; }
  }
}