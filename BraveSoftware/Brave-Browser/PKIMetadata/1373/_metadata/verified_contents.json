[{"description": "treehash per file", "signed_content": {"payload": "eyJjb250ZW50X2hhc2hlcyI6W3siYmxvY2tfc2l6ZSI6NDA5NiwiZGlnZXN0Ijoic2hhMjU2IiwiZmlsZXMiOlt7InBhdGgiOiJjcnMucGIiLCJyb290X2hhc2giOiJSMUVqajdsaEZFSU5sY19SOXdMRmcyNUp2a0Y0RWQza01SRFNTdWIyUGhJIn0seyJwYXRoIjoiY3RfY29uZmlnLnBiIiwicm9vdF9oYXNoIjoieFZZMzNvbTZLcWlDS2RPZkFqdUM1c2NkdGFyN0FldUNheXBIc3hPSHgxTSJ9LHsicGF0aCI6ImtwX3BpbnNsaXN0LnBiIiwicm9vdF9oYXNoIjoidkZYUkNuZzFsQk5xcGhXUEUtT3dxZ3FaMFNSM2RJMGhEQ1FadjRyaFUwWSJ9LHsicGF0aCI6Im1hbmlmZXN0Lmpzb24iLCJyb290X2hhc2giOiJSOGpEcGQyeHhteTllczlpZFV6QTRnMEFyZkcxX0RJZWVmVWdQTnJiVlk0In1dLCJmb3JtYXQiOiJ0cmVlaGFzaCIsImhhc2hfYmxvY2tfc2l6ZSI6NDA5Nn1dLCJpdGVtX2lkIjoiZWZuaW9qbG5qbmRtY2JpaWVlZ2tpY2Fkbm9lY2pqZWYiLCJpdGVtX3ZlcnNpb24iOiIxMzczIiwicHJvdG9jb2xfdmVyc2lvbiI6MX0", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "Cj4TpwxCcFADLdN9bel1sFcdrU9B4EvWRZYlsq02PMgAbk3J_S8lwOkP5-pwt68bAk-gClme04u_C8yUD2UVUPi-yIu0otG98uAZ5szwBzc9qOIqYJmU3cUb2ir_P70_WJS6d8-8wqc078si27P6JzeTxfZAcgWZerXeeUc_Q7KmfSw639Vw-AGCXGIxNwvy6rfQV7EtDxVsFgJKl_evUX8ngLe-vEJys0ZEw6rlLv7IlBhAWuwjBrKoQuVJbFFkOOkWfWTnprcWlPc9CY_IuSg9innJEYw5g9eVFjmhSKTTBDi-xG86hk5nB2vHI5hzN1zW_UYnLtZ5vRWKyhBcaLkzvZhEWGdoT7v0jvvjZeA08rXgkKckXrNF93XacT5uQkBL_Uo9ubDPMOS4hI4Nnhtp-AevCh2WEcYJEl9Fh-w-yvAPqiqJVJZdICsF35X6VJ7VF0kVo0iOJWoTwhpvAltIxtvhq6QYqEYO3yw8_SBi6xZafUct7k2Wifmtt5XYGnbqlHTCpo9M9adeemp1zeXsSZqkN6pWM00wGvelX1imIrdgEoi9XvxtaTtm93REvDdeBADsA8V3GeqyiWm8-axIt0xnRP4unhqFlQ0tlO7IhulhdEq8rHlUZneuGHlLhSygyrCsxyAbK7q5hMc_lKGzxjzLkvqli68TsPcSTjo"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "FULXkIknpBz3lfElIoe-MhvnI-sMUjyVPHUxNYvcwYuctZSBKX3ycA6JR5WnimcPXnuU_7v9XUOL3tk6kg5foFxNP0-eo4m5T9Jpu0q0OsEyZFd2ovUlcTsSrEvOCuyQdImTpOdfy_LRwU8V-5iAnLxHMkxwD6JsyIW-HQpOjZyDgbuIkAxJND1dpXeal5BrA2RqJKMafpH0nHHKEtAxrY6qzqFhdJ_GEsIEiGYrn2yQt3KmqCfrH-ljCO8tkkM8NAwoOm3yIY8K9-cMeOb9eKY3dDnP2Wdwch6bvGB6U5z1LN_7BrcKwSJmVHST2JwQYLDptTuhCIBNmmc7zhqoBA"}]}}]