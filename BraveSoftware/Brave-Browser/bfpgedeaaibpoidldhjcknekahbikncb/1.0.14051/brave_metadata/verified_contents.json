[{"description": "treehash per file", "signed_content": {"payload": "eyJjb250ZW50X2hhc2hlcyI6W3siYmxvY2tfc2l6ZSI6NDA5NiwiZGlnZXN0Ijoic2hhMjU2IiwiZmlsZXMiOlt7InBhdGgiOiJsaXN0LnR4dCIsInJvb3RfaGFzaCI6InJsdVNVRVFRQzBVaGNjNGtnUlo4R2JJU3VuQmRSRkFQRzhNcDF4QVI2V3MifV0sImZvcm1hdCI6InRyZWVoYXNoIiwiaGFzaF9ibG9ja19zaXplIjo0MDk2fV0sIml0ZW1faWQiOiJiZnBnZWRlYWFpYnBvaWRsZGhqY2tuZWthaGJpa25jYiIsIml0ZW1fdmVyc2lvbiI6IjEuMC4xNDA1MSIsInByb3RvY29sX3ZlcnNpb24iOjF9", "signatures": [{"protected": "eyJhbGciOiJSUzI1NiJ9", "header": {"kid": "webstore"}, "signature": "DeLWKdEhPQ7Z4OutBh3YjEtKaVx8Ooh9qHDrD-ug9D5H51pnArOJgefl-dfex6WyhwB-igc0lHddbeQdXRZQnGqC7TuT10bE_3ZmsCY_Dac1AwTisi2nD8e4R2KLqxzaNzNZyGMHxKjau57Vfes5WK4hktkf0HxhwZoXAd8lnTIgddKdhsbBmKPhlqxFGwaxBB6maH-g_x8-M5g8YvXnYVNg67qWJLAuaMD_7lRqZ8GWfhoXVD7oBTCwXit_Zqo9MN2zcQO1ZlB5H80jakDBRTxKfPF-3QUXiCHX3EQ0Lm9bkNi8-f2XCWJ1nxaA8GLSrdgxSyL20gXEc0FAILLYtg"}]}}]