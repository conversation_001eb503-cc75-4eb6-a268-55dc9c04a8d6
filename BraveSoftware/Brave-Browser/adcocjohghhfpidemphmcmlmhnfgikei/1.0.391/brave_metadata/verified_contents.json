[{"description": "treehash per file", "signed_content": {"payload": "eyJjb250ZW50X2hhc2hlcyI6W3siYmxvY2tfc2l6ZSI6NDA5NiwiZGlnZXN0Ijoic2hhMjU2IiwiZmlsZXMiOlt7InBhdGgiOiJsaXN0LnR4dCIsInJvb3RfaGFzaCI6ImFXNzRVS1g1WmhCcTlBOFdpSGVVdl8tMWJJdXhBZ1BhclNRZXlIOFlvSUUifV0sImZvcm1hdCI6InRyZWVoYXNoIiwiaGFzaF9ibG9ja19zaXplIjo0MDk2fV0sIml0ZW1faWQiOiJhZGNvY2pvaGdoaGZwaWRlbXBobWNtbG1obmZnaWtlaSIsIml0ZW1fdmVyc2lvbiI6IjEuMC4zOTEiLCJwcm90b2NvbF92ZXJzaW9uIjoxfQ", "signatures": [{"protected": "eyJhbGciOiJSUzI1NiJ9", "header": {"kid": "webstore"}, "signature": "Z9_lU3sUyxhX5B7Wab9R-mokIxbrwt2WXtsG3Xi5bL2WAdkHX9epKaOHWChWItDs2BQSrp5nrK9Cy9tUDpuC8NIdjiREG1tkTJy-9pPmgn_cG1gpFY0lHV16uJyQGwvMj2bRpa3c1FY_FWCxQu7jNdKMgw53Vk1nnj0F8RdlxKMHvcupZ5XCpug4cqjFAoP3-0jCi0I7HFi5LzOAodG72PejSCoMoOxRv_8i7X4FP1cLlLzZZg_-luIosAMSDVsCSgTGQ-jsEbiAvqtlwgtq3BTBf3RF0fXcbdQVtNehk9lwEvCrcaDiuBV_pSlW3TxiAXQZnRTGnWxxug0LxSRjmw"}]}}]