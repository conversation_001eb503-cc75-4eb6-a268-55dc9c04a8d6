[{"description": "treehash per file", "signed_content": {"payload": "eyJjb250ZW50X2hhc2hlcyI6W3siYmxvY2tfc2l6ZSI6NDA5NiwiZGlnZXN0Ijoic2hhMjU2IiwiZmlsZXMiOlt7InBhdGgiOiJsaXN0LnR4dCIsInJvb3RfaGFzaCI6ImNrdTR1NUJpNlhMb2NWSGJEdVByYUZETEdpTnB4WkRSTXN3Qm1nR01pSEUifV0sImZvcm1hdCI6InRyZWVoYXNoIiwiaGFzaF9ibG9ja19zaXplIjo0MDk2fV0sIml0ZW1faWQiOiJpb2RrcGRhZ2FwZGZrcGhsam5kZHBqbGxkYWRibG9tbyIsIml0ZW1fdmVyc2lvbiI6IjEuMC4xMzQwOSIsInByb3RvY29sX3ZlcnNpb24iOjF9", "signatures": [{"protected": "eyJhbGciOiJSUzI1NiJ9", "header": {"kid": "webstore"}, "signature": "ELrTqdMFM2nOJ9FNP0h9yYrzAfTbWihgB9uwQspjVDid6MK-mfss6avMDZwCiARACe1W96lZk6i_Kb_sAFsdcQwGOCOp6oXIKWMjIUO1fve417seEEln0b540sytpTip5vD8W8OSyorY3OkhO_00ea5mJv-QUzPInKFy5Kab3ejiFzETqYyBfDq3ojTze29p9zI9jIb4sY3YxQnUVwfB1MxyPwfVxCJmZnRJAqAhRD2sfCeuIMUUNg9ThIpNJl8rP7VDp7XawWiy-VG6E1KAk7S6LbdX8GiR0F-Urx2pelWBDQ06oWQ66kllxI1aWZF-jBgSa1ec-_esX0ESv0zhZw"}]}}]