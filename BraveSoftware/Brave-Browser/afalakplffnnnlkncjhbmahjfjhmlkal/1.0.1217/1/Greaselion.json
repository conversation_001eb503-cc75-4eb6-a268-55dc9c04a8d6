[{"urls": ["https://github.com/*", "https://www.github.com/*", "https://gist.github.com/*"], "scripts": ["scripts/brave_rewards/publisher/github/githubBase.bundle.js"], "preconditions": {"rewards-enabled": true}, "minimum_brave_version": "1.17"}, {"urls": ["https://github.com/*", "https://www.github.com/*", "https://gist.github.com/*"], "scripts": ["scripts/brave_rewards/publisher/github/githubAutoContribution.bundle.js"], "preconditions": {"auto-contribution-enabled": true}, "minimum_brave_version": "1.19"}, {"urls": ["https://old.reddit.com/*", "https://reddit.com/*", "https://www.reddit.com/*"], "scripts": ["scripts/brave_rewards/publisher/reddit/redditBase.bundle.js"], "preconditions": {"rewards-enabled": true}, "minimum_brave_version": "1.17"}, {"urls": ["https://old.reddit.com/*", "https://reddit.com/*", "https://www.reddit.com/*"], "scripts": ["scripts/brave_rewards/publisher/reddit/redditAutoContribution.bundle.js"], "preconditions": {"auto-contribution-enabled": true}, "minimum_brave_version": "1.19"}, {"urls": ["https://twitch.tv/*", "https://www.twitch.tv/*"], "scripts": ["scripts/brave_rewards/publisher/twitch/twitchBase.bundle.js"], "preconditions": {"rewards-enabled": true}, "minimum_brave_version": "1.19"}, {"urls": ["https://twitch.tv/*", "https://www.twitch.tv/*"], "scripts": ["scripts/brave_rewards/publisher/twitch/twitchAutoContribution.bundle.js"], "preconditions": {"auto-contribution-enabled": true}, "minimum_brave_version": "1.19"}, {"urls": ["https://twitter.com/*", "https://*.twitter.com/*", "https://x.com/*", "https://*.x.com/*"], "scripts": ["scripts/brave_rewards/publisher/twitter/twitterBase.bundle.js"], "preconditions": {"rewards-enabled": true}, "minimum_brave_version": "1.17"}, {"urls": ["https://twitter.com/*", "https://*.twitter.com/*", "https://x.com/*", "https://*.x.com/*"], "scripts": ["scripts/brave_rewards/publisher/twitter/twitterAutoContribution.bundle.js"], "preconditions": {"auto-contribution-enabled": true}, "minimum_brave_version": "1.19"}, {"urls": ["https://vimeo.com/*"], "scripts": ["scripts/brave_rewards/publisher/vimeo/vimeoBase.bundle.js"], "preconditions": {"rewards-enabled": true}, "minimum_brave_version": "1.18"}, {"urls": ["https://vimeo.com/*"], "scripts": ["scripts/brave_rewards/publisher/vimeo/vimeoAutoContribution.bundle.js"], "preconditions": {"auto-contribution-enabled": true}, "minimum_brave_version": "1.19"}, {"urls": ["https://m.youtube.com/*", "https://www.youtube.com/*"], "scripts": ["scripts/brave_rewards/publisher/youtube/youtubeBase.bundle.js"], "preconditions": {"rewards-enabled": true}}, {"urls": ["https://m.youtube.com/*", "https://www.youtube.com/*"], "scripts": ["scripts/brave_rewards/publisher/youtube/youtubeAutoContribution.bundle.js"], "preconditions": {"auto-contribution-enabled": true}, "minimum_brave_version": "1.19"}]