(()=>{"use strict";const e="mnojpmjdmbbfmejpflffifhffcmidifd";let t=null;const r=()=>t;let i=!1;const n="twitch",o="www.twitch.tv",a=()=>{const e=document.querySelector(".channel-info-content .tw-avatar [src]");return e&&e.getAttribute("src")||""},s=()=>document.querySelector("h1.tw-title"),c=()=>{const e=s();return e&&e.textContent||""},l=e=>e.replace(/^\/|\/[\s\S]*/g,""),d=new Set(["directory","downloads","jobs","p","search","turbo"]),u=()=>"videos"===l(location.pathname).toLowerCase(),m=e=>{if(!e)return"";const t=/^\/*videos\//i;if(t.test(location.pathname)){const r=location.pathname.replace(t,""),i=l(r);return i?`${n}_${e}_void_${i}`:""}return`${n}_${e}`},p=e=>e?`${n}#author:${e}`:"",h=()=>({mediaId:"",mediaKey:"",publisherUrl:`https://${o}`,publisherKey:o,publisherName:o,favIconUrl:""}),f=()=>{const e=(()=>{if(u()){const e=s();if(!e||!e.parentElement)return"";const t=e.parentElement.getAttribute("href");return t?l(t).toLowerCase():""}const e=l(location.pathname).toLowerCase();return d.has(e)?"":e})();return e?{mediaId:e,mediaKey:m(e),publisherUrl:`https://${o}/${e}`,publisherKey:p(e),publisherName:c(),favIconUrl:a()}:h()},b=new class{constructor(){this.previous=h()}async read(){let e=f();if(!e.mediaId&&!u())return this.previous=e,e;let{previous:t}=this;const r=()=>e.mediaId&&e.publisherName&&e.favIconUrl&&(e.mediaId&&t.mediaId===e.mediaId||e.publisherName!==t.publisherName&&e.favIconUrl!==t.favIconUrl);for(let t=0;t<5e3&&!r();t+=250)await new Promise(e=>setTimeout(e,250)),e=f();return this.previous=e,e}};let v="";const y=e=>{e&&"complete"===e.status&&location.href!==v&&(v=location.href,I())},I=()=>{b.read().then(e=>{const t=r();t&&t.postMessage({type:"SavePublisherVisit",mediaType:e.mediaId?n:"",data:{url:e.publisherUrl,publisherKey:e.publisherKey,publisherName:e.publisherName,mediaKey:e.mediaKey,favIconUrl:e.favIconUrl}})}).catch(e=>{const r=e?e.message:"";((e,r)=>{e&&t&&t.postMessage({type:"GreaselionError",mediaType:e,data:{errorMessage:r}})})(n,`Error reading publisher info: ${r})`)})};var w;chrome.extension.inIncognitoContext||(w=e=>{e?(((e,t)=>{if(i)return;i=!0;const n=r();n&&(n.postMessage({type:"RegisterOnUpdatedTab",mediaType:e}),n.onMessage.addListener(function(e){e.data&&"OnUpdatedTab"===e.type&&t(e.data.changeInfo)}))})(n,y),"complete"===document.readyState&&"visible"===document.visibilityState&&I(),document.addEventListener("visibilitychange",function(){"visible"===document.visibilityState&&I()}),console.info("Greaselion script loaded: twitchBase.ts")):console.error("Failed to initialize communications port")},t?w(!0):(chrome.runtime.sendMessage(e,{type:"SupportsGreaselion"},function(r){!chrome.runtime.lastError&&r&&r.supported&&(t=chrome.runtime.connect(e,{name:"Greaselion"}),w(!0))}),setTimeout(()=>{t||(t=chrome.runtime.connect("jidkidbbcafjabdphckchenhfomhnfma",{name:"Greaselion"}),w(!0))},100)))})();