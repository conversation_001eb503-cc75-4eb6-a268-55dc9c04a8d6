(()=>{"use strict";const e="mnojpmjdmbbfmejpflffifhffcmidifd";let t=null;const r=()=>t,n=(e,t)=>e&&t?`${e}_${t}`:"",i=(e,t)=>`${e}#channel:${t}`,o=(e,t,r)=>{if(e.length<t.length)return"";const n=e.indexOf(t);if(-1===n)return"";const i=n+t.length,o=e.indexOf(r,i);let a="";return o!==i?a=-1!==o&&o>i||-1!==o?e.substring(i,o):e.substring(i):""===r&&(a=e.substring(i)),a},a=()=>"complete"===document.readyState&&"visible"===document.visibilityState,s=(e,t)=>`${e}: ${t.statusText} (${t.status})`,c=e=>(new DOMParser).parseFromString(e,"text/html").documentElement.textContent||"",l="vimeo",u="vimeo.com",h=e=>{if(!e)return"";const t=o(e,'"display_name":"','"');if(!t)return"";let r=null;try{r=JSON.parse(`{"brave_publisher":"${t}"}`)}catch(e){throw new Error(`Error parsing publisher name from video page: ${e}`)}return c(r.brave_publisher)},m=e=>{if(!e)return"";let t=o(e,'<meta property="al:ios:url" content="vimeo://app.vimeo.com/users/','"');return t||(t=o(e,'<meta property="al:android:url" content="vimeo://app.vimeo.com/users/','"'),t||o(e,'<link rel="canonical" href="/','"'))},d=async()=>{const e=location.href,t=await fetch(e);if(!t.ok){const e=s("Publisher request failed",t);throw new Error(e)}const r=await t.text();if(!r)throw new Error("Publisher response empty");let a="",u="",d=m(r);if(d)a=(e=>{if(!e)return"";let t=h(e);return t||(t=o(e,'<meta property="og:title" content="','"')),c(t)})(r),a||(a=d);else{if(a=h(r),!a)throw new Error("Invalid publisher name");if(d=(e=>e?o(e,'"creator_id":',","):"")(r),!d)throw new Error("Invalid user id");const e=(e=>e?o(e,'<link rel="canonical" href="https://vimeo.com/','"'):"")(r);if(!e)throw new Error("Invalid video id")}return u=n(l,d),{url:e,publisherKey:i(l,d),publisherName:a,mediaKey:u,favIconUrl:""}},p=()=>{(e=>{if(!e)return!1;const t=["/","/about","/blog","/enterprise","/help","/jobs","/live","/log_in","/ondemand","/ott","/purchases","/search","/settings","/site_map","/stats","/stock","/upgrade","/upload","/videoschool","/watch","/watchlater"];if(t.includes(e)||t.includes(e+"/"))return!0;if(e.startsWith("/channels/staffpicks/"))return!1;const r=["/blog/","/categories/","/channels/","/features/","/help/","/manage/","/ott/","/settings/","/solutions/","/stock/"];for(const t of r)if(e.startsWith(t))return!0;return!1})(new URL(location.href).pathname)?(()=>{const e=`https://${u}`,t=u,n=u,i=r();i&&i.postMessage({type:"SavePublisherVisit",mediaType:"",data:{url:e,publisherKey:t,publisherName:n,favIconUrl:""}})})():f().then(e=>{const t=r();if(!t)throw new Error("Invalid port");t.postMessage({type:"SavePublisherVisit",mediaType:l,data:{url:e.url,publisherKey:e.publisherKey,publisherName:e.publisherName,mediaKey:e.mediaKey,favIconUrl:e.favIconUrl}})}).catch(e=>{throw new Error(`Failed to retrieve publisher data: ${e}`)})},f=async()=>{return(e=location.pathname)&&/^\/\d+$/.test(e)?(async()=>{const e=location.href,t=encodeURI(e),r=await fetch(`https://vimeo.com/api/oembed.json?url=${t}`);if(!r.ok)return d();const o=await r.json();if(!o)return d();const a=o.author_url;if(!a)return d();const c=o.author_name;if(!c)throw new Error("Invalid publisher name");const u=o.video_id;if(!u||0===u)return d();const h=await fetch(a);if(!h.ok){const e=s("Publisher request failed",h);throw new Error(e)}const p=await h.text(),f=m(p);if(!f)throw new Error("Invalid user id");const b=i(l,f),v=n(l,u.toString());if(!v)throw new Error("Invalid media key");const w=(e=>e?`https://i.vimeocdn.com/portrait/${e}_300x300.webp`:"")(f);return{url:a,publisherKey:b,publisherName:c,mediaKey:v,favIconUrl:w}})():d();var e};let b=!1,v="";const w=e=>{e&&(e.url||"complete"===e.status)&&location.href!==v&&(v=location.href,p())};var y;chrome.extension.inIncognitoContext||(y=e=>{e?(a()?p():document.addEventListener("readystatechange",function(){a()&&setTimeout(()=>{p()},200)}),document.addEventListener("visibilitychange",function(){"visible"===document.visibilityState&&p()}),((e,t)=>{if(b)return;b=!0;const n=r();n&&(n.postMessage({type:"RegisterOnUpdatedTab",mediaType:e}),n.onMessage.addListener(function(e){e.data&&"OnUpdatedTab"===e.type&&t(e.data.changeInfo)}))})(l,w)):console.error("Failed to initialize communications port")},t?y(!0):(chrome.runtime.sendMessage(e,{type:"SupportsGreaselion"},function(r){!chrome.runtime.lastError&&r&&r.supported&&(t=chrome.runtime.connect(e,{name:"Greaselion"}),y(!0))}),setTimeout(()=>{t||(t=chrome.runtime.connect("jidkidbbcafjabdphckchenhfomhnfma",{name:"Greaselion"}),y(!0))},100)),console.info("Greaselion script loaded: vimeo.ts"))})();