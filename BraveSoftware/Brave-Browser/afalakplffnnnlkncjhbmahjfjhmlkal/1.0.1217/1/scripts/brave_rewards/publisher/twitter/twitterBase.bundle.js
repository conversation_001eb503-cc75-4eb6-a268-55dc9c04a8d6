(()=>{"use strict";const e="mnojpmjdmbbfmejpflffifhffcmidifd";let t=null;const n=()=>t,r=()=>"complete"===document.readyState&&"visible"===document.visibilityState,s=["authorization","x-csrf-token","x-guest-token"],o=/[; ]_twitter_sess=([^\s;]*)/;let a=null,i={};const l=e=>{if(!e)return null;const t=e.match(o);return t?unescape(t[1]):null},u="twitter",c="twitter.com",m=["https://api."+location.hostname+"/1.1/*"],d=["requestHeaders","extraHeaders"],h=new class{constructor(e){this.values=new Map,this.maxEntries=e}get(e){if(!e)return null;const t=this.values.get(e);return t?(this.values.delete(e),this.values.set(e,t),t):null}put(e,t){if(this.values.size>=this.maxEntries){const e=this.values.keys().next().value;this.values.delete(e)}this.values.set(e,t)}}(128),f=(e,t)=>{if(!e||!t)return;const r=t.id_str,s=`${u}#channel:${r}`,o=e,a=(d=e,(m=u)&&d?`${m}_${d}`:""),i=t.profile_image_url_https.replace("_normal",""),l=((e,t)=>e?t?`https://twitter.com/intent/user?user_id=${t}&screen_name=${e}`:`https://twitter.com/${e}/`:"")(e,r),c=n();var m,d;c&&c.postMessage({type:"SavePublisherVisit",mediaType:u,data:{url:l,publisherKey:s,publisherName:o,mediaKey:a,favIconUrl:i}})},p=()=>{const e=new URL(location.href);(e=>{if(["/","/about","/home","/login","/logout","/messages","/privacy","/search","/settings","/tos"].includes(e))return!0;const t=["/account/","/compose/","/explore","/hashtag/","/i/","/messages/","/notifications","/settings/","/who_to_follow/","/?login","/?logout"];for(const n of t)if(e.startsWith(n))return!0;return!1})(e.pathname)?(()=>{const e=`https://${c}`,t=c,r=c,s=n();s&&s.postMessage({type:"SavePublisherVisit",mediaType:"",data:{url:e,publisherKey:t,publisherName:r,mediaKey:"",favIconUrl:""}})})():(e=>{const t=(e=>{const t=new URLSearchParams(e.search);if(!t)return"";const n=t.get("screen_name");if(n)return unescape(n);if(!e.pathname)return"";const r=e.pathname.split("/").filter(e=>e);return r&&0!==r.length?r[0]:""})(e);if(!t)return;const n=h.get(t);n?f(t,n):new Promise((e,t)=>{const n=document.createElement("script");n.textContent="\n\n(async function() {\n\n  async function pollFor(fn, opts) {\n    const startTime = Date.now()\n    while (Date.now() - startTime < opts.timeout) {\n      const result = fn()\n      if (result) {\n        return result\n      }\n      await new Promise((resolve) => setTimeout(resolve, opts.interval))\n    }\n    console.log('Polling timeout occurred')\n    return null\n  }\n\n  function getElementStore(elem) {\n    if (!elem) {\n      return null\n    }\n    for (const name of Object.getOwnPropertyNames(elem)) {\n      if (name.startsWith('__reactProps$')) {\n        let store = null\n        try { store = elem[name].children.props.store }\n        catch {}\n        if (store && typeof store.getState === 'function') {\n          return store\n        }\n      }\n    }\n    return null\n  }\n\n  function findStore(elem, depth = 0) {\n    if (!elem) {\n      return null\n    }\n    let store = getElementStore(elem)\n    if (store) {\n      return store\n    }\n    if (depth === 4) {\n      return null\n    }\n    for (let child of elem.children) {\n      store = findStore(child, depth + 1)\n      if (store) {\n        return store\n      }\n    }\n    return null\n  }\n\n  let stateStore = null\n\n  function getStore() {\n    if (!stateStore) {\n      stateStore = findStore(document.getElementById('react-root'))\n    }\n    return stateStore\n  }\n\n  function getUserFromState(state, screenName) {\n    const userEntities = state.entities.users.entities\n    for (let [key, value] of Object.entries(userEntities)) {\n      if (value.screen_name.toLocaleLowerCase() ===\n          screenName.toLocaleLowerCase()) {\n        return {\n          siteID: key,\n          imageURL: String(value.profile_image_url_https || '')\n        }\n      }\n    }\n    return null\n  }\n\n  function getUserByScreenName(screenName) {\n    const store = getStore()\n    if (!store) {\n      return null\n    }\n    try {\n      return getUserFromState(store.getState(), screenName)\n    } catch (e) {\n      console.error('Error attempting to get user state', e)\n    }\n    return null\n  }\n\n  function getScreenNameFromPath(path) {\n    let match = path.match(/^\\/([^\\/]+)(\\/|\\/status\\/[\\s\\S]+)?$/)\n    if (match) {\n      return match[1]\n    }\n    return null\n  }\n\n  function getUserFromPath(path) {\n    const screenName = getScreenNameFromPath(path)\n    if (screenName) {\n      const user = getUserByScreenName(screenName)\n      if (user) {\n        return user\n      }\n    }\n    return null\n  }\n\n  const user = await pollFor(() => getUserFromPath(location.pathname), {\n    interval: 600,\n    timeout: 8000\n  })\n\n  document.dispatchEvent(new CustomEvent('rewards-publisher-detected', {\n    detail: { user }\n  }))\n\n})()\n\n",document.head.appendChild(n);const r=n=>{const{user:s}=n.detail;s?(e({id_str:s.siteID,profile_image_url_https:s.imageURL}),document.removeEventListener("rewards-publisher-detected",r)):t(new Error("Unable to find user data in state store"))};document.addEventListener("rewards-publisher-detected",r),document.head.removeChild(n)}).then(e=>{h.put(t,e),f(t,e)}).catch(e=>{console.error(`Failed to fetch user details for ${t}: ${e.message}`)})})(e)};let g=!1,v=!1;const y=(e,t)=>{e===u&&t&&t.requestHeaders&&(e=>{if(!e)return!1;let t={};for(const n of e)if("Cookie"===n.name){const e=l(n.value);e!==a&&(a=e,t={})}else(s.includes(n.name)||n.name.startsWith("x-twitter-"))&&(t[n.name]=n.value);return"yes"!==t["x-twitter-active-user"]&&(t["x-twitter-active-user"]="yes"),!((e,t)=>{const n=Object.getOwnPropertyNames(e),r=Object.getOwnPropertyNames(t);if(n.length!==r.length)return!1;for(let r=0;r<n.length;r++){const s=n[r];if(e[s]!==t[s])return!1}return!0})(i,t)&&(i=t,!0)})(t.requestHeaders)&&p()},w=e=>{e&&e.url&&p()};var S;chrome.extension.inIncognitoContext||(S=e=>{e?(r()?p():document.addEventListener("readystatechange",function(){r()&&p()}),document.addEventListener("visibilitychange",function(){"visible"===document.visibilityState&&p()}),((e,t,r,s)=>{if(v)return;v=!0;const o=n();o&&(o.postMessage({type:"RegisterOnSendHeadersWebRequest",mediaType:e,data:{urlPatterns:t,extra:r}}),o.onMessage.addListener(function(e){e.data&&"OnSendHeadersWebRequest"===e.type&&s(e.mediaType,e.data.details)}))})(u,m,d,y),((e,t)=>{if(g)return;g=!0;const r=n();r&&(r.postMessage({type:"RegisterOnUpdatedTab",mediaType:e}),r.onMessage.addListener(function(e){e.data&&"OnUpdatedTab"===e.type&&t(e.data.changeInfo)}))})(u,w)):console.error("Failed to initialize communications port")},t?S(!0):(chrome.runtime.sendMessage(e,{type:"SupportsGreaselion"},function(n){!chrome.runtime.lastError&&n&&n.supported&&(t=chrome.runtime.connect(e,{name:"Greaselion"}),S(!0))}),setTimeout(()=>{t||(t=chrome.runtime.connect("jidkidbbcafjabdphckchenhfomhnfma",{name:"Greaselion"}),S(!0))},100)),console.info("Greaselion script loaded: twitterBase.ts"))})();