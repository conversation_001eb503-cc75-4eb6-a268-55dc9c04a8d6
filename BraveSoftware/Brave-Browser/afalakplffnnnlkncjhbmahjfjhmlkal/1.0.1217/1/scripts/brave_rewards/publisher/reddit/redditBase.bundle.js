(()=>{"use strict";const e="mnojpmjdmbbfmejpflffifhffcmidifd";let t=null;const r=()=>t,n=(e,r)=>{e&&t&&t.postMessage({type:"GreaselionError",mediaType:e,data:{errorMessage:r}})},i=()=>"complete"===document.readyState&&"visible"===document.visibilityState,o="reddit",a="reddit.com",s=(e,t)=>{if(!e)return"";let r="www";return t&&(r="old"),`https://${r}.reddit.com/user/${e}/`},c=()=>{const e=new URL(location.href);(e=>{if(["/","/coins","/contact","/login","/premium"].includes(e))return!0;const t=["/dev/","/help/","/r/","/wiki/"];for(const r of t)if(e.startsWith(r))return!0;return!1})(e.pathname)?(()=>{const e=`https://www.${a}`,t=a,n=a,i=r();i&&i.postMessage({type:"SavePublisherVisit",mediaType:"",data:{url:e,publisherKey:t,publisherName:n,mediaKey:"",favIconUrl:""}})})():(e=>{const t=(e=>{if(!e.pathname||!e.pathname.startsWith("/user/"))return"";const t=e.pathname.split("/").filter(e=>e);return t&&0!==t.length?t.length<2?"":t[1]:""})(e);if(!t)return void n(o,"Invalid screen name");const i=(e=>e.hostname.startsWith("old.")||e.hostname.startsWith("np."))(e);(async(e,t)=>{if(!e)throw new Error("Invalid parameters");const r=await(async(e,t)=>{if(!e)throw new Error("Invalid parameters");const r=s(e,t);if(!r)throw new Error("Invalid profile url");const n=r+"about.json",i=await fetch(n);if(!i.ok){const e=((e,t)=>`Profile request failed: ${t.statusText} (${t.status})`)(0,i);throw new Error(e)}const o=await i.text();if(!o)throw new Error("Profile response is empty");const a=JSON.parse(o);if(!a)throw new Error("Unable to parse profile response");if("t2"!==a.kind||!a.data)throw new Error("Unexpected profile response data");return a.data})(e,t);return{user:{id:r.id,screenName:e,fullName:r.name,favIconUrl:r.icon_img},post:{id:"",timestamp:"",text:""}}})(t,i).then(e=>{const i=e.user.id,a=((e,t)=>`${e}#channel:${t}`)(o,i),c=e.user.fullName;if(!c)return void n(o,"Invalid publisher name");const d=((e,t)=>e&&t?`${e}_${t}`:"")(o,t),l=e.user.favIconUrl,m=s(t,!1),u=r();u&&u.postMessage({type:"SavePublisherVisit",mediaType:o,data:{url:m,publisherKey:a,publisherName:c,mediaKey:d,favIconUrl:l}})})})(e)};let d=!1,l="";const m=e=>{e&&(e.url||"complete"===e.status)&&location.href!==l&&(l=location.href,c())};var u;chrome.extension.inIncognitoContext||(u=e=>{e?(i()?c():document.addEventListener("readystatechange",function(){i()&&c()}),document.addEventListener("visibilitychange",function(){"visible"===document.visibilityState&&c()}),((e,t)=>{if(d)return;d=!0;const n=r();n&&(n.postMessage({type:"RegisterOnUpdatedTab",mediaType:e}),n.onMessage.addListener(function(e){e.data&&"OnUpdatedTab"===e.type&&t(e.data.changeInfo)}))})(o,m)):console.error("Failed to initialize communications port")},t?u(!0):(chrome.runtime.sendMessage(e,{type:"SupportsGreaselion"},function(r){!chrome.runtime.lastError&&r&&r.supported&&(t=chrome.runtime.connect(e,{name:"Greaselion"}),u(!0))}),setTimeout(()=>{t||(t=chrome.runtime.connect("jidkidbbcafjabdphckchenhfomhnfma",{name:"Greaselion"}),u(!0))},100)),console.info("Greaselion script loaded: redditBase.ts"))})();