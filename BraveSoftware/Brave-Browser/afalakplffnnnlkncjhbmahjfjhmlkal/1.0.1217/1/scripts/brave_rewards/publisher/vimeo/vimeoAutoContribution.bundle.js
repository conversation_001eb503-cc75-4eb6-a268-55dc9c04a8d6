(()=>{"use strict";const e="mnojpmjdmbbfmejpflffifhffcmidifd";let t=null;const r=()=>t,n=(e,t)=>e&&t?`${e}_${t}`:"",i=(e,t)=>`${e}#channel:${t}`,o=(e,t,r)=>{if(e.length<t.length)return"";const n=e.indexOf(t);if(-1===n)return"";const i=n+t.length,o=e.indexOf(r,i);let a="";return o!==i?a=-1!==o&&o>i||-1!==o?e.substring(i,o):e.substring(i):""===r&&(a=e.substring(i)),a},a=(e,t)=>`${e}: ${t.statusText} (${t.status})`,s=e=>(new DOMParser).parseFromString(e,"text/html").documentElement.textContent||"";let c=!1;const l="vimeo",u=e=>{if(!e)return"";const t=o(e,'"display_name":"','"');if(!t)return"";let r=null;try{r=JSON.parse(`{"brave_publisher":"${t}"}`)}catch(e){throw new Error(`Error parsing publisher name from video page: ${e}`)}return s(r.brave_publisher)},d=e=>{if(!e)return"";let t=o(e,'<meta property="al:ios:url" content="vimeo://app.vimeo.com/users/','"');return t||(t=o(e,'<meta property="al:android:url" content="vimeo://app.vimeo.com/users/','"'),t||o(e,'<link rel="canonical" href="/','"'))},m=async()=>{const e=location.href,t=await fetch(e);if(!t.ok){const e=a("Publisher request failed",t);throw new Error(e)}const r=await t.text();if(!r)throw new Error("Publisher response empty");let c="",m="",h=d(r);if(h)c=(e=>{if(!e)return"";let t=u(e);return t||(t=o(e,'<meta property="og:title" content="','"')),s(t)})(r),c||(c=h);else{if(c=u(r),!c)throw new Error("Invalid publisher name");if(h=(e=>e?o(e,'"creator_id":',","):"")(r),!h)throw new Error("Invalid user id");const e=(e=>e?o(e,'<link rel="canonical" href="https://vimeo.com/','"'):"")(r);if(!e)throw new Error("Invalid video id")}return m=n(l,h),{url:e,publisherKey:i(l,h),publisherName:c,mediaKey:m,favIconUrl:""}};let h=0,f="";const p=new Set,b=()=>{h=Date.now()},w=e=>{e&&(e.url||"complete"===e.status)&&location.href!==f&&(f=location.href,b())},v=()=>{(async()=>{return(e=location.pathname)&&/^\/\d+$/.test(e)?(async()=>{const e=location.href,t=encodeURI(e),r=await fetch(`https://vimeo.com/api/oembed.json?url=${t}`);if(!r.ok)return m();const o=await r.json();if(!o)return m();const s=o.author_url;if(!s)return m();const c=o.author_name;if(!c)throw new Error("Invalid publisher name");const u=o.video_id;if(!u||0===u)return m();const h=await fetch(s);if(!h.ok){const e=a("Publisher request failed",h);throw new Error(e)}const f=await h.text(),p=d(f);if(!p)throw new Error("Invalid user id");const b=i(l,p),w=n(l,u.toString());if(!w)throw new Error("Invalid media key");const v=(e=>e?`https://i.vimeocdn.com/portrait/${e}_300x300.webp`:"")(p);return{url:s,publisherKey:b,publisherName:c,mediaKey:w,favIconUrl:v}})():m();var e})().then(e=>{const t=!p.has(e.mediaKey);t&&p.add(e.mediaKey);const i=e.mediaKey.replace(`${l}_`,""),o=Math.round((Date.now()-h)/1e3);((e,t,i,o)=>{if(!t)return;const a=r();a&&a.postMessage({type:"MediaDurationMetadata",mediaType:e,data:{mediaKey:n(e,t),duration:i,firstVisit:o}})})(l,i,o,t)}).catch(e=>{throw new Error(`Failed to retrieve publisher data: ${e}`)})};var y;chrome.extension.inIncognitoContext||(y=e=>{e?(document.addEventListener("visibilitychange",function(){"visible"===document.visibilityState?b():v()}),"visible"===document.visibilityState&&b(),((e,t)=>{if(c)return;c=!0;const n=r();n&&(n.postMessage({type:"RegisterOnUpdatedTab",mediaType:e}),n.onMessage.addListener(function(e){e.data&&"OnUpdatedTab"===e.type&&t(e.data.changeInfo)}))})(l,w)):console.error("Failed to initialize communications port")},t?y(!0):(chrome.runtime.sendMessage(e,{type:"SupportsGreaselion"},function(r){!chrome.runtime.lastError&&r&&r.supported&&(t=chrome.runtime.connect(e,{name:"Greaselion"}),y(!0))}),setTimeout(()=>{t||(t=chrome.runtime.connect("jidkidbbcafjabdphckchenhfomhnfma",{name:"Greaselion"}),y(!0))},100)),console.info("Greaselion script loaded: vimeoAutoContribution.ts"))})();