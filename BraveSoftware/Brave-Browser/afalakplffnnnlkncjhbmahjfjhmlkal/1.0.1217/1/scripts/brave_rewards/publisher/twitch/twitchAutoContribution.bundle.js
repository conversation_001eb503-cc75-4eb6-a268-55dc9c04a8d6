(()=>{"use strict";const e="mnojpmjdmbbfmejpflffifhffcmidifd";let t=null;const n=()=>t,i="twitch",o="www.twitch.tv",r=()=>{const e=document.querySelector(".channel-info-content .tw-avatar [src]");return e&&e.getAttribute("src")||""},a=()=>document.querySelector("h1.tw-title"),s=()=>{const e=a();return e&&e.textContent||""},c=e=>e.replace(/^\/|\/[\s\S]*/g,""),d=new Set(["directory","downloads","jobs","p","search","turbo"]),l=()=>"videos"===c(location.pathname).toLowerCase(),u=e=>{if(!e)return"";const t=/^\/*videos\//i;if(t.test(location.pathname)){const n=location.pathname.replace(t,""),o=c(n);return o?`${i}_${e}_void_${o}`:""}return`${i}_${e}`},m=e=>e?`${i}#author:${e}`:"",h=()=>({mediaId:"",mediaKey:"",publisherUrl:`https://${o}`,publisherKey:o,publisherName:o,favIconUrl:""}),p=()=>{const e=(()=>{if(l()){const e=a();if(!e||!e.parentElement)return"";const t=e.parentElement.getAttribute("href");return t?c(t).toLowerCase():""}const e=c(location.pathname).toLowerCase();return d.has(e)?"":e})();return e?{mediaId:e,mediaKey:u(e),publisherUrl:`https://${o}/${e}`,publisherKey:m(e),publisherName:s(),favIconUrl:r()}:h()},f=(e,t)=>e&&t?`${e}_${t}`:"";let b=!1,v=0,w="";const y=new class{constructor(){this.previous=h()}async read(){let e=p();if(!e.mediaId&&!l())return this.previous=e,e;let{previous:t}=this;const n=()=>e.mediaId&&e.publisherName&&e.favIconUrl&&(e.mediaId&&t.mediaId===e.mediaId||e.publisherName!==t.publisherName&&e.favIconUrl!==t.favIconUrl);for(let t=0;t<5e3&&!n();t+=250)await new Promise(e=>setTimeout(e,250)),e=p();return this.previous=e,e}},I=new Set,$=()=>{v=Date.now()},g=e=>{e&&(e.url||"complete"===e.status)&&location.href!==w&&(w=location.href,$())};var U;chrome.extension.inIncognitoContext||(U=e=>{e?(document.addEventListener("visibilitychange",function(){"visible"===document.visibilityState?$():y.read().then(e=>{const t=!I.has(e.mediaKey);t&&I.add(e.mediaKey);const o=e.mediaKey.replace(`${i}_`,""),r=Math.round((Date.now()-v)/1e3);((e,t,i,o)=>{if(!t)return;const r=n();r&&r.postMessage({type:"MediaDurationMetadata",mediaType:e,data:{mediaKey:f(e,t),duration:i,firstVisit:o}})})(i,o,r,t)}).catch(e=>{throw new Error(`Failed to retrieve publisher data: ${e}`)})}),"visible"===document.visibilityState&&$(),((e,t)=>{if(b)return;b=!0;const i=n();i&&(i.postMessage({type:"RegisterOnUpdatedTab",mediaType:e}),i.onMessage.addListener(function(e){e.data&&"OnUpdatedTab"===e.type&&t(e.data.changeInfo)}))})(i,g)):console.error("Failed to initialize communications port")},t?U(!0):(chrome.runtime.sendMessage(e,{type:"SupportsGreaselion"},function(n){!chrome.runtime.lastError&&n&&n.supported&&(t=chrome.runtime.connect(e,{name:"Greaselion"}),U(!0))}),setTimeout(()=>{t||(t=chrome.runtime.connect("jidkidbbcafjabdphckchenhfomhnfma",{name:"Greaselion"}),U(!0))},100)),console.info("Greaselion script loaded: twitchAutoContribution.ts"))})();