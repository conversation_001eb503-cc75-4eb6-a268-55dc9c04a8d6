(()=>{"use strict";const e="mnojpmjdmbbfmejpflffifhffcmidifd";let t=null;const r=()=>t,n=(e,t)=>e&&t?`${e}_${t}`:"",o=(e,t)=>`${e}#channel:${t}`,i=(e,t,r)=>{if(e.length<t.length)return"";const n=e.indexOf(t);if(-1===n)return"";const o=n+t.length,i=e.indexOf(r,o);let a="";return i!==o?a=-1!==i&&i>o||-1!==i?e.substring(o,i):e.substring(o):""===r&&(a=e.substring(o)),a},a=(e,t)=>`${e}: ${t.statusText} (${t.status})`,s="youtube",c="youtube.com",u=e=>`https://www.youtube.com/channel/${e}/videos`,l=e=>{if(!e)return"";let t=i(e,'"avatar":{"thumbnails":[{"url":"','"');return t||(t=i(e,'"width":88,"height":88},{"url":"','"'),t||"")},h=e=>{if(!e)return"";let t=i(e,'"ucid":"','"');return t||(t=i(e,'HeaderRenderer":{"channelId":"','"'),t||(t=i(e,'<link rel="canonical" href="https://www.youtube.com/channel/','">'),t||(t=i(e,'browseEndpoint":{"browseId":"','"'),t||"")))},d=()=>document.querySelector("#contents .ytp-title-link"),m=e=>{if(!e||!e.href)return"";const t=new URL(e.href);return t?f(t):""},f=e=>{if(!e)return"";const t=new URLSearchParams(e.search);return t&&t.get("v")||""},p=e=>!(!e||!e.includes("/channel/")),w=e=>!(!e||!e.includes("/user/")),b=async()=>{const e=location.href,t=await fetch(e);if(!t.ok){const e=a("Publisher request failed",t);throw new Error(e)}const r=await t.text(),c=h(r);if(!c)throw new Error("Invalid channel id");const f=o(s,c),p=(e=>{if(!e)return"";const t=i(e,'<meta itemprop="name" content="','"');return t?(r=t,(new DOMParser).parseFromString(r,"text/html").documentElement.textContent||""):"";var r})(r);if(!p)throw new Error("Invalid publisher name");const w=d(),b=m(w),y=n(s,b||c),v=l(r);return{url:u(c),publisherKey:f,publisherName:p,mediaKey:y,favIconUrl:v}},y=(e,t,r,a)=>{const c=h(t);if(!c)throw new Error("Invalid channel id");const d=o(s,c),m=f(new URL(e));if(!m)throw new Error("Invalid media id");const p=n(s,m||c);if(!r&&!(r=(e=>{if(!e)return"";const t=i(e,'"author":"','"');if(!t)return"";let r=null;try{r=JSON.parse(`{"brave_publisher":"${t}"}`)}catch(e){throw new Error(`Error parsing publisher name from response: ${e}`)}return r.brave_publisher})(t)))throw new Error("Invalid publisher name");return a||(a=u(c)),{url:a,publisherKey:d,publisherName:r,mediaKey:p,favIconUrl:l(t)}},v=async()=>{if((e=location.pathname)&&e.endsWith("/watch"))return(async()=>{const e=location.href,t=f(new URL(e));if(!t)throw new Error("Invalid media id");const r=(e=>`https://www.youtube.com/watch?v=${e}`)(t),n=encodeURI(r),o=await fetch(`https://www.youtube.com/oembed?format=json&url=${n}`);if(!o.ok){if(401===o.status)return(async e=>{const t=await fetch(e);if(!t.ok){const e=a("Publisher request failed",t);throw new Error(e)}const r=await t.text();if(!r)throw new Error("Publisher request failed: empty response");return y(e,r,"","")})(e);{const e=a("oEmbed request failed",o);throw new Error(e)}}const i=await o.json(),s={};s.publisherUrl=i.author_url,s.publisherName=i.author_name;const c=await fetch(s.publisherUrl);if(!c.ok){const e=a("Publisher request failed",o);throw new Error(e)}const u=await c.text();if(!u)throw new Error("Publisher request failed: empty response");return y(e,u,s.publisherName,s.publisherUrl)})();var e;if(p(location.pathname)){const e=(e=>{if(!e)return"";const t=i(e+"/","/channel/","/");if(!t)return"";const r=t.split("?");return r&&0!==r.length?r[0]:""})(location.pathname);return(async e=>{if(!e)throw new Error("Invalid channel id");const t=document.querySelector("#channel-container #text-container");if(!t)throw new Error("Unable to extract channel name from page");const r=o(s,e),i=(a=t)?a.innerText.trim():"";var a;if(!i)throw new Error("Invalid publisher name");const c=d(),h=m(c),f=n(s,h||e);return{url:u(e),publisherKey:r,publisherName:i,mediaKey:f,favIconUrl:(e=>{for(const t of e){const e=l(t.text);if(e)return e}return""})(document.scripts)}})(e)}return w(location.pathname)?b():(e=>{const t=["/","/account","/account_advanced","/account_billing","/account_notifications","/account_playback","/account_privacy","/account_sharing","/channel","/feed","/gaming","/oops","/pair","/playlist","/premium","/reporthistory","/subscription_manager","/user","/watch"];if(!e)return!1;const r=(e=>{if(!e)return"";let t=e.substring(0,e.indexOf("/",1));return t&&t!==e||(t=e.substring(0,e.indexOf("?",1)),t&&t!==e||(t=e)),t})(e);for(const e of t)if(r===e)return!0;return!1})(location.pathname)?{url:`https://www.${c}`,publisherKey:c,publisherName:c,mediaKey:"",favIconUrl:""}:b()};let g=0;const E=new Set,x=()=>{g=Date.now()};var I;chrome.extension.inIncognitoContext||(I=e=>{e?(document.addEventListener("visibilitychange",function(){"visible"===document.visibilityState?x():(p(location.pathname)||w(location.pathname))&&v().then(e=>{const t=!E.has(e.mediaKey);t&&E.add(e.mediaKey);const o=e.mediaKey.replace(`${s}_`,""),i=Math.round((Date.now()-g)/1e3);((e,t,o,i)=>{if(!t)return;const a=r();a&&a.postMessage({type:"MediaDurationMetadata",mediaType:e,data:{mediaKey:n(e,t),duration:o,firstVisit:i}})})(s,o,i,t)}).catch(e=>{throw new Error(`Failed to retrieve publisher data: ${e}`)})}),"visible"===document.visibilityState&&x(),document.addEventListener("yt-page-data-updated",function(){x()})):console.error("Failed to initialize communications port")},t?I(!0):(chrome.runtime.sendMessage(e,{type:"SupportsGreaselion"},function(r){!chrome.runtime.lastError&&r&&r.supported&&(t=chrome.runtime.connect(e,{name:"Greaselion"}),I(!0))}),setTimeout(()=>{t||(t=chrome.runtime.connect("jidkidbbcafjabdphckchenhfomhnfma",{name:"Greaselion"}),I(!0))},100)),console.info("Greaselion script loaded: youtubeAutoContribution.ts"))})();