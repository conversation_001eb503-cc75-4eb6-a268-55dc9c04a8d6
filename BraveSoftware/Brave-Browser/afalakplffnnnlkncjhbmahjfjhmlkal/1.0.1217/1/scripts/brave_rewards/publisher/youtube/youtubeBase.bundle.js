(()=>{"use strict";const e="mnojpmjdmbbfmejpflffifhffcmidifd";let t=null;const r=()=>t;let n=!1;const i=(e,t,i)=>{if(!e||n)return;n=!0;const o=r();o&&(o.postMessage({type:"RegisterOnCompletedWebRequest",mediaType:e,data:{urlPatterns:[t]}}),o.onMessage.addListener(e=>{"OnCompletedWebRequest"===e.type&&i(e.mediaType,e.details)}))},o=(e,t)=>e&&t?`${e}_${t}`:"",a=(e,t)=>`${e}#channel:${t}`,s=(e,t,r)=>{if(e.length<t.length)return"";const n=e.indexOf(t);if(-1===n)return"";const i=n+t.length,o=e.indexOf(r,i);let a="";return o!==i?a=-1!==o&&o>i||-1!==o?e.substring(i,o):e.substring(i):""===r&&(a=e.substring(i)),a},c=(e,t)=>`${e}: ${t.statusText} (${t.status})`,u="youtube",l="youtube.com",h=e=>`https://www.youtube.com/channel/${e}/videos`,d=e=>{if(!e)return"";let t=s(e,'"avatar":{"thumbnails":[{"url":"','"');return t||(t=s(e,'"width":88,"height":88},{"url":"','"'),t||"")},m=e=>{if(!e)return"";let t=s(e,'"ucid":"','"');return t||(t=s(e,'HeaderRenderer":{"channelId":"','"'),t||(t=s(e,'<link rel="canonical" href="https://www.youtube.com/channel/','">'),t||(t=s(e,'browseEndpoint":{"browseId":"','"'),t||"")))},f=()=>document.querySelector("#contents .ytp-title-link"),p=e=>{if(!e||!e.href)return"";const t=new URL(e.href);return t?w(t):""},w=e=>{if(!e)return"";const t=new URLSearchParams(e.search);return t&&t.get("v")||""},b=e=>!(!e||!e.endsWith("/watch"));let y=!0;const v=e=>{y=e},g=async()=>{const e=location.href,t=await fetch(e);if(!t.ok){const e=c("Publisher request failed",t);throw new Error(e)}const r=await t.text(),n=m(r);if(!n)throw new Error("Invalid channel id");const i=a(u,n),l=(e=>{if(!e)return"";const t=s(e,'<meta itemprop="name" content="','"');return t?(r=t,(new DOMParser).parseFromString(r,"text/html").documentElement.textContent||""):"";var r})(r);if(!l)throw new Error("Invalid publisher name");const w=f(),b=p(w),y=o(u,b||n),v=d(r);return{url:h(n),publisherKey:i,publisherName:l,mediaKey:y,favIconUrl:v}},E=(e,t,r,n)=>{const i=m(t);if(!i)throw new Error("Invalid channel id");const c=a(u,i),l=w(new URL(e));if(!l)throw new Error("Invalid media id");const f=o(u,l||i);if(!r&&!(r=(e=>{if(!e)return"";const t=s(e,'"author":"','"');if(!t)return"";let r=null;try{r=JSON.parse(`{"brave_publisher":"${t}"}`)}catch(e){throw new Error(`Error parsing publisher name from response: ${e}`)}return r.brave_publisher})(t)))throw new Error("Invalid publisher name");return n||(n=h(i)),{url:n,publisherKey:c,publisherName:r,mediaKey:f,favIconUrl:d(t)}},I=()=>{U().then(e=>{const t=r();if(!t)throw new Error("Invalid port");t.postMessage({type:"SavePublisherVisit",mediaType:u,data:{url:e.url,publisherKey:e.publisherKey,publisherName:e.publisherName,mediaKey:e.mediaKey,favIconUrl:e.favIconUrl}})}).catch(e=>{throw new Error(`Failed to retrieve publisher data: ${e}`)})},U=async()=>{if(b(location.pathname))return(async()=>{const e=location.href,t=w(new URL(e));if(!t)throw new Error("Invalid media id");const r=(e=>`https://www.youtube.com/watch?v=${e}`)(t),n=encodeURI(r),i=await fetch(`https://www.youtube.com/oembed?format=json&url=${n}`);if(!i.ok){if(401===i.status)return(async e=>{const t=await fetch(e);if(!t.ok){const e=c("Publisher request failed",t);throw new Error(e)}const r=await t.text();if(!r)throw new Error("Publisher request failed: empty response");return E(e,r,"","")})(e);{const e=c("oEmbed request failed",i);throw new Error(e)}}const o=await i.json(),a={};a.publisherUrl=o.author_url,a.publisherName=o.author_name;const s=await fetch(a.publisherUrl);if(!s.ok){const e=c("Publisher request failed",i);throw new Error(e)}const u=await s.text();if(!u)throw new Error("Publisher request failed: empty response");return E(e,u,a.publisherName,a.publisherUrl)})();if((e=location.pathname)&&e.includes("/channel/")){const e=(e=>{if(!e)return"";const t=s(e+"/","/channel/","/");if(!t)return"";const r=t.split("?");return r&&0!==r.length?r[0]:""})(location.pathname);return(async e=>{if(!e)throw new Error("Invalid channel id");const t=document.querySelector("#channel-container #text-container");if(!t)throw new Error("Unable to extract channel name from page");const r=a(u,e),n=(i=t)?i.innerText.trim():"";var i;if(!n)throw new Error("Invalid publisher name");const s=f(),c=p(s),l=o(u,c||e);return{url:h(e),publisherKey:r,publisherName:n,mediaKey:l,favIconUrl:(e=>{for(const t of e){const e=d(t.text);if(e)return e}return""})(document.scripts)}})(e)}var e;return(e=>!(!e||!e.includes("/user/")))(location.pathname)?g():(e=>{const t=["/","/account","/account_advanced","/account_billing","/account_notifications","/account_playback","/account_privacy","/account_sharing","/channel","/feed","/gaming","/oops","/pair","/playlist","/premium","/reporthistory","/subscription_manager","/user","/watch"];if(!e)return!1;const r=(e=>{if(!e)return"";let t=e.substring(0,e.indexOf("/",1));return t&&t!==e||(t=e.substring(0,e.indexOf("?",1)),t&&t!==e||(t=e)),t})(e);for(const e of t)if(r===e)return!0;return!1})(location.pathname)?{url:`https://www.${l}`,publisherKey:l,publisherName:l,mediaKey:"",favIconUrl:""}:g()},x="https://www.youtube.com/api/stats/watchtime?*",$=(e,t)=>{if(e!==u)return;if(!t||!t.url)return;(e=>{const t=new URLSearchParams(e.search),n=(e=>e&&e.get("docid")||"")(t),i=o(u,n),a=(e=>{if(!e)return 0;const t=e.get("st"),r=e.get("et");if(!t||!r)return 0;const n=t.split(",");if(!n||0===n.length)return 0;const i=r.split(",");if(!i||0===i.length)return 0;if(n.length!==i.length)return 0;let o=0;for(let e=0;e<n.length;++e){const t=parseFloat(n[e]),r=parseFloat(i[e]);o+=Math.round(r-t)}return o})(t),s=r();s&&(s.postMessage({type:"MediaDurationMetadata",mediaType:u,data:{mediaKey:i,duration:a,firstVisit:y}}),v(!1))})(new URL(t.url))};var K;chrome.extension.inIncognitoContext||(K=e=>{e?(document.addEventListener("readystatechange",function(){"complete"!==document.readyState||"visible"!==document.visibilityState||b(location.pathname)||setTimeout(()=>{i(u,x,$),I()},200)}),document.addEventListener("visibilitychange",function(){"visible"===document.visibilityState&&(i(u,x,$),I())}),document.addEventListener("yt-page-data-updated",function(){"visible"===document.visibilityState&&(i(u,x,$),I()),v(!0)})):console.error("Failed to initialize communications port")},t?K(!0):(chrome.runtime.sendMessage(e,{type:"SupportsGreaselion"},function(r){!chrome.runtime.lastError&&r&&r.supported&&(t=chrome.runtime.connect(e,{name:"Greaselion"}),K(!0))}),setTimeout(()=>{t||(t=chrome.runtime.connect("jidkidbbcafjabdphckchenhfomhnfma",{name:"Greaselion"}),K(!0))},100)),console.info("Greaselion script loaded: youtubeBase.ts"))})();