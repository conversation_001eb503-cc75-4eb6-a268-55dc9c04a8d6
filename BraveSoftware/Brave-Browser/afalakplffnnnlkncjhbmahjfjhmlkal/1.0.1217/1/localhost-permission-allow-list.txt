# https://community.brave.com/t/bug-brave-shields-break-trezor-bridge-connect-when-accessing-wallet/219544
trezor.io

# https://github.com/brave/brave-browser/issues/27346#issuecomment-1435404209
razroo.com

# https://pcsupport.lenovo.com/ (Service Bridge)
lenovo.com

# https://community.brave.com/t/intel-driver-support-assistant-doesnt-work-with-shields-on/212468
intel.com
intel.de
intel.ie
intel.it
intel.pl
intel.es
intel.com.tr
intel.co.uk
intel.la
intel.com.br
intel.com.au
intel.sg
intel.in
intel.co.id
intel.co.kr
intel.co.jp
intel.vn
intel.com.tw
intel.cn
intel.co.il

# https://github.com/uBlockOrigin/uAssets/pull/24488
faceit.com

# https://github.com/uBlockOrigin/uAssets/issues/23388
bund.de
organspende-register.de
bayernportal.de
deutsche-rentenversicherung.de
mv-serviceportal.de

# https://github.com/uBlockOrigin/uAssets/issues/21960
mega.nz

# https://github.com/uBlockOrigin/uAssets/pull/22475
client.foldingathome.org

# https://github.com/uBlockOrigin/uAssets/pull/20768
musicbrainz.org

# https://github.com/uBlockOrigin/uAssets/issues/19005
battlelog.battlefield.com

# https://github.com/uBlockOrigin/uAssets/issues/23456
figma.com

# https://community.brave.com/t/filecrypt-co-functionality-is-broken-when-shields-are-up/
filecrypt.co

# https://studio.apollographql.com/sandbox/explorer
apollographql.com

# https://driverhub.asus.com/en
driverhub.asus.com

# https://github.com/brave/brave-browser/issues/43050
pcsupport.lenovo.com
