[{"include": ["*://maps.gsi.go.jp/*"], "exceptions": ["canvas"], "issue": "https://github.com/brave/brave-browser/issues/35755"}, {"include": ["*://www.britishairways.com/*", "*://www.ba.com/*"], "exceptions": ["language"], "issue": "https://github.com/brave/brave-browser/issues/36814"}, {"include": ["*://www.harkins.com/*"], "exceptions": ["canvas"], "issue": "https://github.com/brave/brave-browser/issues/35750"}, {"include": ["*://www.hanes.com/*"], "exceptions": ["language"], "issue": "https://github.com/brave/brave-browser/issues/46653"}, {"include": ["*://www.nperf.com/*"], "exceptions": ["canvas"], "issue": "https://github.com/brave/brave-browser/issues/35020"}, {"include": ["*://docs.google.com/*"], "exceptions": ["plugins"], "issue": "https://github.com/brave/brave-browser/issues/26079"}, {"include": ["*://workspace.google.com/*"], "exceptions": ["canvas"], "issue": "https://github.com/brave/brave-browser/issues/44574"}, {"include": ["*://vizzy.io/*"], "exceptions": ["audio"], "issue": "https://github.com/brave/brave-browser/issues/40440"}, {"include": ["*://tileman.io/*"], "exceptions": ["keyboard"], "issue": "https://github.com/brave/brave-browser/issues/43055"}, {"include": ["*://www.athenacrisis.com/*", "*://athenacrisis.com/*"], "exceptions": ["canvas"], "issue": "https://github.com/brave/brave-browser/issues/41686"}, {"include": ["*://www.tangerine.ca/*"], "exceptions": ["font", "screen"], "issue": "https://github.com/brave/brave-browser/issues/42600"}, {"include": ["*://www.fedex.com/*"], "exceptions": ["referrer"], "issue": "https://github.com/brave/brave-browser/issues/44976"}, {"include": ["*://ospfranco.com/*"], "exceptions": ["webgl2"], "issue": "https://github.com/brave/brave-browser/issues/46599"}]