[{"include": ["*://*.amazon.ae/*", "*://*.amazon.ca/*", "*://*.amazon.cn/*", "*://*.amazon.co.jp/*", "*://*.amazon.co.uk/*", "*://*.amazon.co.za/*", "*://*.amazon.com/*", "*://*.amazon.com.au/*", "*://*.amazon.com.be/*", "*://*.amazon.com.br/*", "*://*.amazon.com.mx/*", "*://*.amazon.com.tr/*", "*://*.amazon.de/*", "*://*.amazon.eg/*", "*://*.amazon.es/*", "*://*.amazon.fr/*", "*://*.amazon.ie/*", "*://*.amazon.in/*", "*://*.amazon.it/*", "*://*.amazon.nl/*", "*://*.amazon.pl/*", "*://*.amazon.sa/*", "*://*.amazon.se/*", "*://*.amazon.sg/*"], "exclude": [], "params": ["ascsubtag", "dib", "dib_tag", "geniuslink", "keywords", "th", "content-id", "linkCode", "linkId", "tag", "ufe", "crid", "pd_rd_i", "pd_rd_r", "pd_rd_w", "pd_rd_wg", "pf_rd_i", "pf_rd_m", "pf_rd_p", "pf_rd_r", "pf_rd_s", "pf_rd_t", "qid", "ref", "refinements", "rnid", "social_share", "sprefix"]}, {"include": ["*://www.google.ac/search?*", "*://www.google.ad/search?*", "*://www.google.ae/search?*", "*://www.google.al/search?*", "*://www.google.am/search?*", "*://www.google.as/search?*", "*://www.google.at/search?*", "*://www.google.az/search?*", "*://www.google.ba/search?*", "*://www.google.be/search?*", "*://www.google.bf/search?*", "*://www.google.bg/search?*", "*://www.google.bi/search?*", "*://www.google.bj/search?*", "*://www.google.bs/search?*", "*://www.google.bt/search?*", "*://www.google.by/search?*", "*://www.google.ca/search?*", "*://www.google.cat/search?*", "*://www.google.cd/search?*", "*://www.google.cf/search?*", "*://www.google.cg/search?*", "*://www.google.ch/search?*", "*://www.google.ci/search?*", "*://www.google.cl/search?*", "*://www.google.cm/search?*", "*://www.google.cn/search?*", "*://www.google.co.ao/search?*", "*://www.google.co.bw/search?*", "*://www.google.co.ck/search?*", "*://www.google.co.cr/search?*", "*://www.google.co.id/search?*", "*://www.google.co.il/search?*", "*://www.google.co.in/search?*", "*://www.google.co.jp/search?*", "*://www.google.co.ke/search?*", "*://www.google.co.kr/search?*", "*://www.google.co.ls/search?*", "*://www.google.co.ma/search?*", "*://www.google.co.mz/search?*", "*://www.google.co.nz/search?*", "*://www.google.co.th/search?*", "*://www.google.co.tz/search?*", "*://www.google.co.ug/search?*", "*://www.google.co.uk/search?*", "*://www.google.co.uz/search?*", "*://www.google.co.ve/search?*", "*://www.google.co.vi/search?*", "*://www.google.co.za/search?*", "*://www.google.co.zm/search?*", "*://www.google.co.zw/search?*", "*://www.google.com/search?*", "*://www.google.com.af/search?*", "*://www.google.com.ag/search?*", "*://www.google.com.ai/search?*", "*://www.google.com.ar/search?*", "*://www.google.com.au/search?*", "*://www.google.com.bd/search?*", "*://www.google.com.bh/search?*", "*://www.google.com.bn/search?*", "*://www.google.com.bo/search?*", "*://www.google.com.br/search?*", "*://www.google.com.bz/search?*", "*://www.google.com.co/search?*", "*://www.google.com.cu/search?*", "*://www.google.com.cy/search?*", "*://www.google.com.do/search?*", "*://www.google.com.ec/search?*", "*://www.google.com.eg/search?*", "*://www.google.com.et/search?*", "*://www.google.com.fj/search?*", "*://www.google.com.gh/search?*", "*://www.google.com.gi/search?*", "*://www.google.com.gt/search?*", "*://www.google.com.hk/search?*", "*://www.google.com.iq/search?*", "*://www.google.com.jm/search?*", "*://www.google.com.kh/search?*", "*://www.google.com.kw/search?*", "*://www.google.com.lb/search?*", "*://www.google.com.ly/search?*", "*://www.google.com.mm/search?*", "*://www.google.com.mt/search?*", "*://www.google.com.mx/search?*", "*://www.google.com.my/search?*", "*://www.google.com.na/search?*", "*://www.google.com.nf/search?*", "*://www.google.com.ng/search?*", "*://www.google.com.ni/search?*", "*://www.google.com.np/search?*", "*://www.google.com.om/search?*", "*://www.google.com.pa/search?*", "*://www.google.com.pe/search?*", "*://www.google.com.pg/search?*", "*://www.google.com.ph/search?*", "*://www.google.com.pk/search?*", "*://www.google.com.pr/search?*", "*://www.google.com.py/search?*", "*://www.google.com.qa/search?*", "*://www.google.com.sa/search?*", "*://www.google.com.sb/search?*", "*://www.google.com.sg/search?*", "*://www.google.com.sl/search?*", "*://www.google.com.sv/search?*", "*://www.google.com.tj/search?*", "*://www.google.com.tn/search?*", "*://www.google.com.tr/search?*", "*://www.google.com.tw/search?*", "*://www.google.com.ua/search?*", "*://www.google.com.uy/search?*", "*://www.google.com.vc/search?*", "*://www.google.com.vn/search?*", "*://www.google.cv/search?*", "*://www.google.cz/search?*", "*://www.google.de/search?*", "*://www.google.dj/search?*", "*://www.google.dk/search?*", "*://www.google.dm/search?*", "*://www.google.dz/search?*", "*://www.google.ee/search?*", "*://www.google.es/search?*", "*://www.google.eus/search?*", "*://www.google.fi/search?*", "*://www.google.fm/search?*", "*://www.google.fr/search?*", "*://www.google.ga/search?*", "*://www.google.ge/search?*", "*://www.google.gg/search?*", "*://www.google.gl/search?*", "*://www.google.gm/search?*", "*://www.google.gp/search?*", "*://www.google.gr/search?*", "*://www.google.gy/search?*", "*://www.google.hn/search?*", "*://www.google.hr/search?*", "*://www.google.ht/search?*", "*://www.google.hu/search?*", "*://www.google.ie/search?*", "*://www.google.im/search?*", "*://www.google.iq/search?*", "*://www.google.is/search?*", "*://www.google.it/search?*", "*://www.google.je/search?*", "*://www.google.jo/search?*", "*://www.google.kg/search?*", "*://www.google.ki/search?*", "*://www.google.kz/search?*", "*://www.google.la/search?*", "*://www.google.li/search?*", "*://www.google.lk/search?*", "*://www.google.lt/search?*", "*://www.google.lu/search?*", "*://www.google.lv/search?*", "*://www.google.md/search?*", "*://www.google.me/search?*", "*://www.google.mg/search?*", "*://www.google.mk/search?*", "*://www.google.ml/search?*", "*://www.google.mn/search?*", "*://www.google.ms/search?*", "*://www.google.mu/search?*", "*://www.google.mv/search?*", "*://www.google.mw/search?*", "*://www.google.ne/search?*", "*://www.google.ng/search?*", "*://www.google.nl/search?*", "*://www.google.no/search?*", "*://www.google.nr/search?*", "*://www.google.nu/search?*", "*://www.google.pl/search?*", "*://www.google.pn/search?*", "*://www.google.ps/search?*", "*://www.google.pt/search?*", "*://www.google.ro/search?*", "*://www.google.rs/search?*", "*://www.google.ru/search?*", "*://www.google.rw/search?*", "*://www.google.sc/search?*", "*://www.google.se/search?*", "*://www.google.sh/search?*", "*://www.google.si/search?*", "*://www.google.sk/search?*", "*://www.google.sm/search?*", "*://www.google.sn/search?*", "*://www.google.so/search?*", "*://www.google.sr/search?*", "*://www.google.st/search?*", "*://www.google.td/search?*", "*://www.google.tg/search?*", "*://www.google.tk/search?*", "*://www.google.tl/search?*", "*://www.google.tm/search?*", "*://www.google.tn/search?*", "*://www.google.to/search?*", "*://www.google.tt/search?*", "*://www.google.vg/search?*", "*://www.google.vu/search?*", "*://www.google.ws/search?*"], "exclude": [], "params": ["bih", "biw", "client", "dpr", "ei", "fbs", "gs_lcrp", "gs_lp", "ictx", "<PERSON>lsig", "oq", "sa", "sca_esv", "sclient", "sei", "source", "sourceid", "spell", "sstk", "uact", "ved"]}, {"include": ["*://new.reddit.com/*", "*://old.reddit.com/*", "*://www.reddit.com/*"], "exclude": [], "params": ["correlation_id", "post_fullname", "ref", "ref_campaign", "ref_source", "share_id"]}, {"include": ["https://*.app.link/*"], "exclude": [], "params": ["cjevent", "click_id"]}, {"include": ["https://www.goodreads.com/book/show/*"], "exclude": [], "params": ["from_search", "from_srp", "qid", "rank"]}, {"include": ["*://www.ft.com/*", "*://*.linkedin.com/*", "*://therecord.media/*"], "exclude": [], "params": ["trk"]}, {"include": ["*://*.cbsnews.com/*", "*://*.cbssports.com/*", "*://*.cnet.com/*", "*://*.gamespot.com/*", "*://*.insideedition.com/*", "*://*.paramountplus.com/*"], "exclude": [], "params": ["ftag", "intcid", "linkId"]}, {"include": ["*://www.zoopla.co.uk/*"], "exclude": [], "params": ["search_identifier"]}, {"include": ["*://*.bandcamp.com/*"], "exclude": [], "params": ["from"]}, {"include": ["*://www.gofundme.com/*"], "exclude": [], "params": ["attribution_id"]}, {"include": ["*://gamefound.com/*"], "exclude": [], "params": ["ref", "refcode"]}, {"include": ["*://*.washingtonpost.com/*"], "exclude": [], "params": ["itid"]}, {"include": ["*://*.ticketweb.ca/*", "*://*.ticketweb.com/*", "*://*.ticketweb.ie/*", "*://*.ticketweb.uk/*"], "exclude": [], "params": ["REFERRAL_ID"]}, {"include": ["*://*.imdb.com/*"], "exclude": [], "params": ["pf_rd_m", "pf_rd_p", "pf_rd_r", "pf_rd_s", "pf_rd_t", "pf_rd_i", "ref_"]}, {"include": ["*://*.ebay.com/itm/*", "*://www.ebay.at/itm/*", "*://www.ebay.ca/itm/*", "*://www.ebay.co.uk/itm/*", "*://www.ebay.com.au/itm/*", "*://www.ebay.com.sg/itm/*", "*://www.ebay.de/itm/*", "*://www.ebay.es/itm/*", "*://www.ebay.ie/itm/*", "*://www.ebay.fr/itm/*", "*://www.ebay.it/itm/*", "*://www.ebay.ch/itm/*", "*://www.ebay.nl/itm/*", "*://www.ebay.pl/itm/*"], "exclude": [], "params": ["media", "mkcid", "mkevt", "ssspo", "ssrc", "ssuid", "widget_ver"]}, {"include": ["*://www.instagram.com/*"], "exclude": [], "params": ["igsh", "igshid", "ig_rid"]}, {"include": ["*://*.twitter.com/*", "*://*.x.com/*"], "exclude": ["*://*.twitter.com/i/redirect?*", "*://*.x.com/i/redirect?*"], "params": ["cxt", "ref_src", "ref_url", "s", "t"]}, {"include": ["*://hbr.org/*", "*://techcrunch.com/*"], "exclude": [], "params": ["tpcc"]}, {"include": ["*://www.wsj.com/*"], "exclude": [], "params": ["reflink", "st", "mod"]}, {"include": ["*://ici.radio-canada.ca/*", "*://radio-canada.ca/*"], "exclude": [], "params": ["partageApp"]}, {"include": ["*://search.brave.com/*", "*://actionnetwork.org/*", "*://*.bamboohr.com/careers/*", "*://www.tumblr.com/*", "*://medium.com/*"], "exclude": [], "params": ["source"]}, {"include": ["*://www.zillow.com/*"], "exclude": [], "params": ["rtoken"]}, {"include": ["*://feverup.com/*"], "exclude": [], "params": ["CAMEFROM"]}, {"include": ["*://www.linkedin.com/in/*"], "exclude": [], "params": ["trackingCode", "trackingId", "original_referer"]}, {"include": ["*://www.linkedin.com/help/*"], "exclude": [], "params": ["mcid", "src"]}, {"include": ["*://www.linkedin.com/posts/*"], "exclude": [], "params": ["rcm"]}, {"include": ["*://open.spotify.com/*"], "exclude": [], "params": ["si"]}, {"include": ["*://youtu.be/*", "*://*.youtube.com/watch?*"], "exclude": [], "params": ["app", "embeds_euri", "embeds_loader_url_for_pings", "embeds_origin", "feature", "pp", "si", "source_ve_path"]}, {"include": ["*://www.limesonline.com/*", "*://www.lastampa.it/*", "*://laprovinciapavese.gelocal.it/*", "*://lasentinella.gelocal.it/*", "*://www.huffingtonpost.it/*", "*://www.ilsecoloxix.it/*", "*://eprint.iacr.org/*", "*://*.goodreads.com/*", "*://hackernoon.com/*", "*://www.backerkit.com/*", "*://*.kickstarter.com/*"], "exclude": [], "params": ["ref"]}, {"include": ["*://*.tiktok.com/*"], "exclude": [], "params": ["embed_source", "refer", "referer_url", "referer_video_id"]}, {"include": ["*://*.bloomberglaw.com/*", "*://*.bloomberg.com/*"], "exclude": [], "params": ["trk", "in_source", "leadSource", "srnd"]}, {"include": ["*://*.aliexpress.com/item/*"], "exclude": [], "params": ["algo_exp_id", "pdp_npi", "curPageLogUid"]}, {"include": ["*://*/*"], "exclude": ["*://app.hive.co/*"], "params": ["h_sid", "h_slt"]}, {"include": ["*://*.eventbrite.at/*", "*://*.eventbrite.be/*", "*://*.eventbrite.ca/*", "*://*.eventbrite.ch/*", "*://*.eventbrite.cl/*", "*://*.eventbrite.co/*", "*://*.eventbrite.com.ar/*", "*://*.eventbrite.com.au/*", "*://*.eventbrite.com.br/*", "*://*.eventbrite.com.mx/*", "*://*.eventbrite.com.pe/*", "*://*.eventbrite.co.nz/*", "*://*.eventbrite.co.uk/*", "*://*.eventbrite.de/*", "*://*.eventbrite.dk/*", "*://*.eventbrite.es/*", "*://*.eventbrite.fi/*", "*://*.eventbrite.fr/*", "*://*.eventbrite.hk/*", "*://*.eventbrite.ie/*", "*://*.eventbrite.it/*", "*://*.eventbrite.nl/*", "*://*.eventbrite.pt/*", "*://*.eventbrite.se/*", "*://*.eventbrite.sg/*", "*://*.eventbrite.com/*"], "exclude": [], "params": ["utm-campaign", "utm-content", "utm-experiment", "utm-medium", "utm-share-source", "utm-source", "utm-term", "aff", "can_id", "email_referrer", "email_subject", "link_id", "ref", "source"]}, {"include": ["*://*.seek.com.au/*", "*://*.seek.co.nz/*"], "exclude": [], "params": ["tracking"]}, {"include": ["*://mailchi.mp/*"], "exclude": [], "params": ["e"]}, {"include": ["*://*.songkick.com/*"], "exclude": [], "params": ["deep_link_campaign", "deep_link_medium", "deep_link_source"]}, {"include": ["*://*.humblebundle.com/*"], "exclude": [], "params": ["hmb_campaign", "hmb_medium", "hmb_source"]}, {"include": ["*://ctr.narvar.com/*/tracking/*"], "exclude": [], "params": ["destination_country", "dzip", "order_number", "origin_country", "ozip", "service"]}, {"include": ["*://dice.fm/event/*"], "exclude": [], "params": ["cid1", "cid2", "pid"]}, {"include": ["*://*.nytimes.com/*"], "exclude": [], "params": ["referringSource", "sgrp", "smid", "ugrp"]}, {"include": ["*://*.github.com/*"], "exclude": [], "params": ["notification_referrer_id"]}, {"include": ["*://arstechnica.com/*", "*://pitchfork.com/*", "*://www.allure.com/*", "*://www.architecturaldigest.com/*", "*://www.bonappetit.com/*", "*://www.cntraveler.com/*", "*://www.epicurious.com/*", "*://www.glamour.com/*", "*://www.gq.com/*", "*://www.gq-magazine.co.uk/*", "*://www.gqitalia.it/*", "*://www.gqmagazine.fr/*", "*://www.newyorker.com/*", "*://www.self.com/*", "*://www.them.us/*", "*://www.teenvogue.com/*", "*://www.vanityfair.com/*", "*://www.vogue.com/*", "*://www.wired.com/*", "*://www.wired.co.uk/*"], "exclude": [], "params": ["bxid", "cndid", "esrc", "hasha", "hashb", "hashc", "source", "mbid"]}, {"include": ["*://*/*"], "exclude": ["https://urldefense.com/v3/*"], "params": ["_branch_referrer", "_branch_match_id", "__hssc", "__hstc", "_hsenc", "_hsmi", "hsCtaTracking", "_bhlid", "cm_cr", "cm_me", "cm_re", "cm_sp", "cm_mmc", "cm_mmca1", "cm_mmca2", "cm_mmca3", "cm_mmca4", "cm_mmca5", "cm_mmca6", "cm_mmca7", "cm_mmca8", "cm_mmca9", "cm_mmca10", "cm_mmca11", "cm_mmca12", "cm_mmca13", "cm_mmca14", "cm_mmca15", "ck_subscriber_id", "hsa_acc", "hsa_ad", "hsa_cam", "hsa_grp", "hsa_kw", "hsa_la", "hsa_mt", "hsa_net", "hsa_ol", "hsa_src", "hsa_tgt", "hsa_ver", "action_object_map", "action_ref_map", "action_type_map", "fb_action_ids", "fb_action_types", "fb_comment_id", "fb_ref", "fb_source", "<PERSON><PERSON><PERSON><PERSON>", "irgwc", "ir_adid", "ir_campaignid", "ir_partnerid", "yad<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "yclid", "ymclid", "ysclid", "cx_click", "cx_recsOrder", "cx_recsWidget", "at_campaign", "at_campaign_type", "at_channel", "at_creation", "at_custom1", "at_custom2", "at_custom3", "at_custom4", "at_detail_placement", "at_emailtype", "at_format", "at_format", "at_general_placement", "at_identifier", "at_link", "at_link_id", "at_link_origin", "at_link_type", "at_medium", "at_network", "at_platform", "at_ptr_name", "at_recipient_id", "at_recipient_list", "at_send_date", "at_term", "at_type", "at_variant", "brave-campaign-id", "brave-creative-id", "brave-creative-set-id", "et_cid", "et_lid", "et_rid", "ml_sub", "ml_sub_hash", "ml_subscriber", "ml_subscriber_hash", "mc_cid", "nb_adid", "nb_aname", "nb_asize", "nb_atype", "nb_cid", "nb_mtype", "nb_partner_name", "nb_partner_shared_id", "nb_pid", "nb_platform", "nb_product_sku", "s_campaign", "sc_customer", "sc_eh", "sc_lid", "sc_llid", "sc_src", "sc_uid", "ss_campaign_id", "ss_campaign_name", "ss_campaign_sent_date", "ss_source", "mtm_campaign", "mtm_cid", "mtm_content", "mtm_group", "mtm_keyword", "mtm_kwd", "mtm_medium", "mtm_placement", "mtm_source", "piwik_campaign", "piwik_kwd", "pk_campaign", "pk_cid", "pk_content", "pk_cpn", "pk_keyword", "pk_kwd", "pk_medium", "pk_source", "utm_ad", "utm_affiliate", "utm_brand", "utm_campaign", "utm_campaigncode", "utm_campaignid", "utm_channel", "utm_cid", "utm_content", "utm_creative", "utm_creative_format", "utm_device", "utm_emcid", "utm_emmid", "utm_id", "utm_internal", "utm_keyword", "utm_mailing", "utm_marketing_tactic", "utm_medium", "utm_name", "utm_place", "utm_product", "utm_pubreferrer", "utm_reader", "utm_referrer", "utm_serial", "utm_session", "utm_siteid", "utm_social", "utm_social-type", "utm_source", "utm_source_platform", "utm_supplier", "utm_swu", "utm_term", "utm_umguk", "utm_userid", "utm_viz_id", "srsltid", "g<PERSON><PERSON>", "wbraid", "gad_source", "gclsrc", "gclid", "usqp", "guc<PERSON>unter", "guce_referrer", "guce_referrer_sig", "ex_cid"]}, {"include": ["*://*.threads.net/*"], "exclude": [], "params": ["xmt"]}, {"include": ["https://dev-pages.bravesoftware.com/clean-urls/*"], "exclude": ["https://dev-pages.bravesoftware.com/clean-urls/exempted/*"], "params": ["brave_testing1", "brave_testing2"]}]