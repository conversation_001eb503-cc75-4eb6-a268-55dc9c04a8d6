    OpenRGB v0.9
    Commit: b5f46e3f1de03595656c682fc2f068b66e735e63 from 2023-07-09 22:51:47 -0500
    Launched: 20250714_085418
====================================================================================================

0     |Info:    Found file: setup.orp attempting to validate header
0     |Info:    Valid v4 profile found for setup
9     |Info:    ------------------------------------------------------
9     |Info:    |               Start device detection               |
9     |Info:    ------------------------------------------------------
9     |Info:    Initializing HID interfaces: Success
15    |Info:    ------------------------------------------------------
15    |Info:    |             Detecting I2C interfaces               |
15    |Info:    ------------------------------------------------------
15    |Info:    Registering I2C interface: /dev/i2c-3 Device 10DE:2684 Subsystem: 10DE:167C
15    |Info:    Registering I2C interface: /dev/i2c-1 Device 0000:0000 Subsystem: 0000:0000
15    |Info:    Registering I2C interface: /dev/i2c-CYP0002:00 Device 0000:0000 Subsystem: 0000:0000
15    |Info:    Registering I2C interface: /dev/i2c-6 Device 10DE:2684 Subsystem: 10DE:167C
15    |Info:    Registering I2C interface: /dev/i2c-2 Device 0000:0000 Subsystem: 0000:0000
15    |Info:    Registering I2C interface: /dev/i2c-0 Device 0000:0000 Subsystem: 0000:0000
15    |Info:    Registering I2C interface: /dev/i2c-9 Device 8086:7AA3 Subsystem: 1043:8694
15    |Info:    Registering I2C interface: /dev/i2c-5 Device 10DE:2684 Subsystem: 10DE:167C
15    |Info:    ------------------------------------------------------
15    |Info:    |               Detecting I2C devices                |
15    |Info:    ------------------------------------------------------
307   |Info:    ------------------------------------------------------
307   |Info:    |               Detecting I2C PCI devices            |
307   |Info:    ------------------------------------------------------
307   |Info:    ------------------------------------------------------
307   |Info:    |               Detecting HID devices                |
307   |Info:    ------------------------------------------------------
704   |Info:    [Candy companion chip] Registering RGB controller
1016  |Info:    [Mountain Everest] Registering RGB controller
1099  |Info:    [ASUS ROG MAXIMUS Z690 HERO] Registering RGB controller
1099  |Info:    ------------------------------------------------------
1099  |Info:    |            Detecting libusb HID devices            |
1099  |Info:    ------------------------------------------------------
1119  |Info:    ------------------------------------------------------
1119  |Info:    |              Detecting other devices               |
1119  |Info:    ------------------------------------------------------
1124  |Info:    ------------------------------------------------------
1124  |Info:    |                Detection completed                 |
1124  |Info:    ------------------------------------------------------
1124  |Dialog:  <h2>WARNING:</h2><p>One or more I2C/SMBus interfaces failed to initialize.</p><p>RGB DRAM modules and some motherboards' onboard RGB lighting will not be available without I2C/SMBus.</p><p>On Linux, this is usually because the i2c-dev module is not loaded.  You must load the i2c-dev module along with the correct i2c driver for your motherboard.  This is usually i2c-piix4 for AMD systems and i2c-i801 for Intel systems.</p><p>See <a href='https://help.openrgb.org/'>help.openrgb.org</a> for additional troubleshooting steps if you keep seeing this message.<br></p>
1125  |Info:    Profile loading: Succeeded for Candy companion chip @ HID: /dev/hidraw12 (Receiver) 
Wireless Index: 255
1125  |Info:    Profile loading: Succeeded for Mountain Everest @ HID: /dev/hidraw9
1125  |Info:    Profile loading: Succeeded for ASUS ROG MAXIMUS Z690 HERO @ HID: /dev/hidraw0
