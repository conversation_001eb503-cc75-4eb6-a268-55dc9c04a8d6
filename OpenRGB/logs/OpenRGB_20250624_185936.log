    OpenRGB v0.9
    Commit: b5f46e3f1de03595656c682fc2f068b66e735e63 from 2023-07-09 22:51:47 -0500
    Launched: 20250624_185936
====================================================================================================

0     |Info:    Found file: setup.orp attempting to validate header
0     |Info:    Valid v4 profile found for setup
1001  |Info:    ------------------------------------------------------
1001  |Info:    |               Start device detection               |
1001  |Info:    ------------------------------------------------------
1001  |Info:    Initializing HID interfaces: Success
1008  |Info:    ------------------------------------------------------
1008  |Info:    |             Detecting I2C interfaces               |
1008  |Info:    ------------------------------------------------------
1008  |Info:    Registering I2C interface: /dev/i2c-3 Device 10DE:2684 Subsystem: 10DE:167C
1008  |Info:    Registering I2C interface: /dev/i2c-1 Device 0000:0000 Subsystem: 0000:0000
1008  |Info:    Registering I2C interface: /dev/i2c-CYP0002:00 Device 0000:0000 Subsystem: 0000:0000
1008  |Info:    Registering I2C interface: /dev/i2c-6 Device 10DE:2684 Subsystem: 10DE:167C
1008  |Info:    Registering I2C interface: /dev/i2c-2 Device 0000:0000 Subsystem: 0000:0000
1008  |Info:    Registering I2C interface: /dev/i2c-0 Device 0000:0000 Subsystem: 0000:0000
1008  |Info:    Registering I2C interface: /dev/i2c-9 Device 8086:7AA3 Subsystem: 1043:8694
1009  |Info:    Registering I2C interface: /dev/i2c-5 Device 10DE:2684 Subsystem: 10DE:167C
1009  |Info:    ------------------------------------------------------
1009  |Info:    |               Detecting I2C devices                |
1009  |Info:    ------------------------------------------------------
1300  |Info:    ------------------------------------------------------
1300  |Info:    |               Detecting I2C PCI devices            |
1300  |Info:    ------------------------------------------------------
1301  |Info:    ------------------------------------------------------
1301  |Info:    |               Detecting HID devices                |
1301  |Info:    ------------------------------------------------------
1719  |Info:    [Candy companion chip] Registering RGB controller
2032  |Info:    [Mountain Everest] Registering RGB controller
2106  |Info:    [ASUS ROG MAXIMUS Z690 HERO] Registering RGB controller
2106  |Info:    ------------------------------------------------------
2106  |Info:    |            Detecting libusb HID devices            |
2106  |Info:    ------------------------------------------------------
2138  |Info:    ------------------------------------------------------
2138  |Info:    |              Detecting other devices               |
2138  |Info:    ------------------------------------------------------
2142  |Info:    ------------------------------------------------------
2142  |Info:    |                Detection completed                 |
2142  |Info:    ------------------------------------------------------
2142  |Dialog:  <h2>WARNING:</h2><p>One or more I2C/SMBus interfaces failed to initialize.</p><p>RGB DRAM modules and some motherboards' onboard RGB lighting will not be available without I2C/SMBus.</p><p>On Linux, this is usually because the i2c-dev module is not loaded.  You must load the i2c-dev module along with the correct i2c driver for your motherboard.  This is usually i2c-piix4 for AMD systems and i2c-i801 for Intel systems.</p><p>See <a href='https://help.openrgb.org/'>help.openrgb.org</a> for additional troubleshooting steps if you keep seeing this message.<br></p>
4274  |Info:    Profile loading: Succeeded for Candy companion chip @ HID: /dev/hidraw12 (Receiver) 
Wireless Index: 255
4274  |Info:    Profile loading: Succeeded for Mountain Everest @ HID: /dev/hidraw9
4274  |Info:    Profile loading: Succeeded for ASUS ROG MAXIMUS Z690 HERO @ HID: /dev/hidraw0
