/* Import custom tray menu styles */
@import url("tray-menu-style.css");

/* Import app-specific styles */
@import url("apps/nemo.css");

/* SwayNC notification background fix - prevent full screen background */
window.swaync-notification-window .background,
.floating-notifications.background,
.floating-notifications .background {
  background-color: transparent !important;
}

@keyframes ripple {
  to {
    background-size: 1000% 1000%;
  }
}

@keyframes ripple-on-slider {
  to {
    background-size: auto, 1000% 1000%;
  }
}

@keyframes ripple-on-headerbar {
  from {
    background-image: radial-gradient(circle, #8caaee 0%, transparent 0%);
  }

  to {
    background-image: radial-gradient(circle, #8caaee 100%, transparent 100%);
  }
}

/***************
 * Base States *
 ***************/
/* Exclude SwayNC from global background styling to prevent notification background issues */
.background:not(window.swaync-notification-window):not(.floating-notifications) {
  background-color: #303446;
  color: #FFFFFF;
}

dnd {
  color: #FFFFFF;
}

.normal-icons {
  -gtk-icon-size: 16px;
}

.large-icons {
  -gtk-icon-size: 32px;
}

.aboutdialog .large-icons {
  -gtk-icon-size: 128px;
}

spinner:disabled,
arrow:disabled,
scrollbar:disabled,
check:disabled,
radio:disabled,
treeview.expander:disabled {
  -gtk-icon-filter: opacity(0.5);
}

iconview,
.view {
  background-color: #303446;
  color: #FFFFFF;
}

iconview:disabled,
.view:disabled {
  color: rgba(255, 255, 255, 0.5);
}

iconview:selected,
.view:selected {
  color: #FFFFFF;
}

textview text {
  background-color: #303446;
}

textview border {
  background-color: #232634;
  color: rgba(255, 255, 255, 0.7);
}

iconview:hover,
iconview:selected {
  border-radius: 3px;
}

rubberband,
.content-view rubberband,
.content-view columnview.view>rubberband,
.content-view treeview.view>rubberband,
.content-view .rubberband,
columnview.view>rubberband,
.content-view columnview.view>.rubberband,
treeview.view>rubberband,
.content-view treeview.view>.rubberband,
gridview>rubberband,
flowbox>rubberband {
  border: 1px solid #8caaee;
  background-color: rgba(140, 170, 238, 0.3);
}

flowbox>flowboxchild {
  padding: 4px;
  border-radius: 6px;
}

.content-view .tile:selected {
  background-color: transparent;
}

gridview>child {
  padding: 3px;
}

gridview>child:selected {
  outline-color: alpha(currentColor, 0.06);
}

gridview>child box {
  border-spacing: 8px;
  margin: 12px;
}

coverflow cover {
  color: #FFFFFF;
  background-color: #303446;
  border: 1px solid black;
}

label.separator {
  color: rgba(255, 255, 255, 0.7);
}

label:disabled {
  opacity: 1;
  color: rgba(255, 255, 255, 0.5);
}

headerbar label:disabled,
tab label:disabled,
button label:disabled {
  color: inherit;
  opacity: 1;
}

label.osd {
  border-radius: 6px;
  background-color: rgba(29, 31, 43, 0.9);
  color: #FFFFFF;
}

.dim-label,
row.expander image.expander-row-arrow,
row label.subtitle {
  color: rgba(255, 255, 255, 0.7);
  opacity: 1;
}

.accent {
  color: #8caaee;
}

.success {
  color: #66BB6A;
}

.warning {
  color: #FBC02D;
}

.error {
  color: #F44336;
}

.large-title {
  font-weight: 300;
  font-size: 24pt;
}

.title-1 {
  font-weight: 800;
  font-size: 20pt;
}

.title-2 {
  font-weight: 800;
  font-size: 15pt;
}

.title-3 {
  font-weight: 700;
  font-size: 15pt;
}

.title-4 {
  font-weight: 700;
  font-size: 13pt;
}

.heading {
  font-weight: 700;
  font-size: 11pt;
}

.body {
  font-weight: 400;
  font-size: 11pt;
}

.caption {
  font-weight: 400;
  font-size: 9pt;
}

.caption-heading {
  font-weight: 700;
  font-size: 9pt;
}

window.assistant .sidebar {
  padding: 4px 0;
}

window.assistant .sidebar label {
  min-height: 36px;
  padding: 0 12px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 500;
}

window.assistant .sidebar label.highlight {
  color: #FFFFFF;
}

.osd .scale-popup>arrow,
.osd .scale-popup>contents,
.osd popover>arrow,
.osd popover>contents,
popover.touch-selection>arrow,
popover.touch-selection>contents,
popover.magnifier>arrow,
popover.magnifier>contents,
.osd {
  color: #FFFFFF;
  background-color: #303446;
  background-clip: padding-box;
  border-radius: 6px;
  border: none;
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.15), 0 4px 3px 0 rgba(0, 0, 0, 0.18), 0 1px 6px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.1);
}

.osd {
  padding: 6px;
  margin: 6px;
}

.osd.circular {
  border-radius: 9999px;
}

/*********************
 * Spinner Animation *
 *********************/
@keyframes spin {
  to {
    transform: rotate(1turn);
  }
}

spinner {
  background: none;
  opacity: 0;
  -gtk-icon-source: -gtk-icontheme("process-working-symbolic");
}

spinner:checked {
  opacity: 1;
  animation: spin 1s linear infinite;
}

spinner:checked:disabled {
  opacity: 0.5;
}

/****************
 * Text Entries *
 ****************/

entry headerbar popover.background entry,
headerbar popover.background entry entry,
entry {
  min-height: 36px;
  padding: 0 8px;
  border-spacing: 6px;
  border-radius: 6px;
  caret-color: currentColor;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.7);
  outline: 0 solid transparent;
  outline-offset: 2px;
}


entry headerbar popover.background entry:focus-within,
headerbar popover.background entry entry:focus-within,
entry:focus-within {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: rgba(255, 255, 255, 0.08);
  box-shadow: inset 0 0 0 2px transparent;
  color: #FFFFFF;
  outline: 2px solid #8caaee;
  outline-offset: -2px;
}


entry headerbar popover.background entry:drop(active),
headerbar popover.background entry entry:drop(active),
entry headerbar popover.background entry:hover:not(:focus-within),
headerbar popover.background entry entry:hover:not(:focus-within),
entry:drop(active),
entry:hover:not(:focus-within) {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: alpha(currentColor, 0.08);
  box-shadow: inset 0 0 0 2px alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
  outline-offset: 2px;
}


entry headerbar popover.background entry:disabled,
headerbar popover.background entry entry:disabled,
entry:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.5);
  outline: none;
}


entry headerbar popover.background entry.flat:focus-within,
headerbar popover.background entry entry.flat:focus-within,
entry headerbar popover.background entry.flat:disabled,
headerbar popover.background entry entry.flat:disabled,
entry headerbar popover.background entry.flat:hover,
headerbar popover.background entry entry.flat:hover,
entry headerbar popover.background entry.flat,
headerbar popover.background entry entry.flat,
entry.flat:focus-within,
entry.flat:disabled,
entry.flat:hover,
entry.flat {
  min-height: 0;
  padding: 2px;
  background-color: transparent;
  box-shadow: none;
  border-radius: 0;
  outline: none;
}


entry headerbar popover.background entry image,
headerbar popover.background entry entry image,
entry image {
  color: rgba(255, 255, 255, 0.7);
}


entry headerbar popover.background entry image:hover,
headerbar popover.background entry entry image:hover,
entry headerbar popover.background entry image:active,
headerbar popover.background entry entry image:active,
entry image:hover,
entry image:active {
  color: #FFFFFF;
}


entry headerbar popover.background entry image:disabled,
headerbar popover.background entry entry image:disabled,
entry image:disabled {
  color: rgba(255, 255, 255, 0.5);
}


entry headerbar popover.background entry image.left,
headerbar popover.background entry entry image.left,
entry image.left {
  margin: 0 6px 0 2px;
}


entry headerbar popover.background entry image.right,
headerbar popover.background entry entry image.right,
entry image.right {
  margin: 0 2px 0 6px;
}


entry headerbar popover.background entry undershoot.left,
headerbar popover.background entry entry undershoot.left,
entry undershoot.left {
  background-color: transparent;
  background-image: linear-gradient(to top, transparent 50%, rgba(255, 255, 255, 0.3) 50%);
  padding-left: 1px;
  background-size: 1px 12px;
  background-repeat: repeat-y;
  background-origin: content-box;
  background-position: left top;
  margin: 0 4px;
  margin: 4px 0;
}


entry headerbar popover.background entry undershoot.right,
headerbar popover.background entry entry undershoot.right,
entry undershoot.right {
  background-color: transparent;
  background-image: linear-gradient(to top, transparent 50%, rgba(255, 255, 255, 0.3) 50%);
  padding-right: 1px;
  background-size: 1px 12px;
  background-repeat: repeat-y;
  background-origin: content-box;
  background-position: right top;
  margin: 0 4px;
  margin: 4px 0;
}


entry headerbar popover.background entry.error,
headerbar popover.background entry entry.error,
entry.error {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(244, 67, 54, 0.1);
  color: rgba(244, 67, 54, 0.75);
  outline: 0 solid transparent;
  outline-offset: 2px;
  outline: none;
}


entry headerbar popover.background entry.error:focus-within,
headerbar popover.background entry entry.error:focus-within,
entry.error:focus-within {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: rgba(244, 67, 54, 0.1);
  box-shadow: inset 0 0 0 2px transparent;
  color: #F44336;
  outline: 2px solid #F44336;
  outline-offset: -2px;
  outline: none;
}


entry headerbar popover.background entry.error:drop(active),
headerbar popover.background entry entry.error:drop(active),
entry headerbar popover.background entry.error:hover:not(:focus-within),
headerbar popover.background entry entry.error:hover:not(:focus-within),
entry.error:drop(active),
entry.error:hover:not(:focus-within) {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: alpha(currentColor, 0.08);
  box-shadow: inset 0 0 0 2px alpha(currentColor, 0.08);
  color: #F44336;
  outline: 0 solid transparent;
  outline-offset: 2px;
  outline: none;
}


entry headerbar popover.background entry.error:disabled,
headerbar popover.background entry entry.error:disabled,
entry.error:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(244, 67, 54, 0.1);
  color: rgba(244, 67, 54, 0.35);
  outline: none;
  outline: none;
}


entry headerbar popover.background entry.error>text>selection,
headerbar popover.background entry entry.error>text>selection,
entry.error>text>selection {
  background-color: rgba(244, 67, 54, 0.25);
  color: #F44336;
}


entry headerbar popover.background entry.error image,
headerbar popover.background entry entry.error image,
entry.error image {
  color: rgba(244, 67, 54, 0.75);
}


entry headerbar popover.background entry.error image:hover,
headerbar popover.background entry entry.error image:hover,
entry headerbar popover.background entry.error image:active,
headerbar popover.background entry entry.error image:active,
entry.error image:hover,
entry.error image:active {
  color: #F44336;
}


entry headerbar popover.background entry.error image:disabled,
headerbar popover.background entry entry.error image:disabled,
entry.error image:disabled {
  color: rgba(244, 67, 54, 0.35);
}


entry headerbar popover.background entry.warning,
headerbar popover.background entry entry.warning,
entry.warning {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(251, 192, 45, 0.1);
  color: rgba(251, 192, 45, 0.75);
  outline: 0 solid transparent;
  outline-offset: 2px;
  outline: none;
}


entry headerbar popover.background entry.warning:focus-within,
headerbar popover.background entry entry.warning:focus-within,
entry.warning:focus-within {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: rgba(251, 192, 45, 0.1);
  box-shadow: inset 0 0 0 2px transparent;
  color: #FBC02D;
  outline: 2px solid #FBC02D;
  outline-offset: -2px;
  outline: none;
}


entry headerbar popover.background entry.warning:drop(active),
headerbar popover.background entry entry.warning:drop(active),
entry headerbar popover.background entry.warning:hover:not(:focus-within),
headerbar popover.background entry entry.warning:hover:not(:focus-within),
entry.warning:drop(active),
entry.warning:hover:not(:focus-within) {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: alpha(currentColor, 0.08);
  box-shadow: inset 0 0 0 2px alpha(currentColor, 0.08);
  color: #FBC02D;
  outline: 0 solid transparent;
  outline-offset: 2px;
  outline: none;
}


entry headerbar popover.background entry.warning:disabled,
headerbar popover.background entry entry.warning:disabled,
entry.warning:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(251, 192, 45, 0.1);
  color: rgba(251, 192, 45, 0.35);
  outline: none;
  outline: none;
}


entry headerbar popover.background entry.warning>text>selection,
headerbar popover.background entry entry.warning>text>selection,
entry.warning>text>selection {
  background-color: rgba(251, 192, 45, 0.25);
  color: #FBC02D;
}


entry headerbar popover.background entry.warning image,
headerbar popover.background entry entry.warning image,
entry.warning image {
  color: rgba(251, 192, 45, 0.75);
}


entry headerbar popover.background entry.warning image:hover,
headerbar popover.background entry entry.warning image:hover,
entry headerbar popover.background entry.warning image:active,
headerbar popover.background entry entry.warning image:active,
entry.warning image:hover,
entry.warning image:active {
  color: #FBC02D;
}


entry headerbar popover.background entry.warning image:disabled,
headerbar popover.background entry entry.warning image:disabled,
entry.warning image:disabled {
  color: rgba(251, 192, 45, 0.35);
}


entry headerbar popover.background entry.success,
headerbar popover.background entry entry.success,
entry.success {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(102, 187, 106, 0.1);
  color: rgba(102, 187, 106, 0.75);
  outline: 0 solid transparent;
  outline-offset: 2px;
  outline: none;
}


entry headerbar popover.background entry.success:focus-within,
headerbar popover.background entry entry.success:focus-within,
entry.success:focus-within {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: rgba(102, 187, 106, 0.1);
  box-shadow: inset 0 0 0 2px transparent;
  color: #66BB6A;
  outline: 2px solid #66BB6A;
  outline-offset: -2px;
  outline: none;
}


entry headerbar popover.background entry.success:drop(active),
headerbar popover.background entry entry.success:drop(active),
entry headerbar popover.background entry.success:hover:not(:focus-within),
headerbar popover.background entry entry.success:hover:not(:focus-within),
entry.success:drop(active),
entry.success:hover:not(:focus-within) {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: alpha(currentColor, 0.08);
  box-shadow: inset 0 0 0 2px alpha(currentColor, 0.08);
  color: #66BB6A;
  outline: 0 solid transparent;
  outline-offset: 2px;
  outline: none;
}


entry headerbar popover.background entry.success:disabled,
headerbar popover.background entry entry.success:disabled,
entry.success:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(102, 187, 106, 0.1);
  color: rgba(102, 187, 106, 0.35);
  outline: none;
  outline: none;
}


entry headerbar popover.background entry.success>text>selection,
headerbar popover.background entry entry.success>text>selection,
entry.success>text>selection {
  background-color: rgba(102, 187, 106, 0.25);
  color: #66BB6A;
}


entry headerbar popover.background entry.success image,
headerbar popover.background entry entry.success image,
entry.success image {
  color: rgba(102, 187, 106, 0.75);
}


entry headerbar popover.background entry.success image:hover,
headerbar popover.background entry entry.success image:hover,
entry headerbar popover.background entry.success image:active,
headerbar popover.background entry entry.success image:active,
entry.success image:hover,
entry.success image:active {
  color: #66BB6A;
}


entry headerbar popover.background entry.success image:disabled,
headerbar popover.background entry entry.success image:disabled,
entry.success image:disabled {
  color: rgba(102, 187, 106, 0.35);
}


entry>progress,
entry progress>trough>progress {
  margin: 0 -4px;
  border-bottom: 2px solid #8caaee;
  background-color: transparent;
}


entry button.image-button {
  min-height: 24px;
  min-width: 24px;
  padding: 0;
}

treeview entry.flat,
treeview entry {
  background-color: #303446;
}

treeview entry.flat,
treeview entry.flat:focus-within,
treeview entry,
treeview entry:focus-within {
  border-image: none;
  box-shadow: none;
}

.entry-tag {
  margin: 2px;
  border-radius: 9999px;
  box-shadow: none;
  background-color: rgba(255, 255, 255, 0.12);
  color: #FFFFFF;
}

.entry-tag:hover {
  background-image: image(alpha(currentColor, 0.08));
}

:dir(ltr) .entry-tag {
  margin-left: 4px;
  margin-right: 0;
  padding-left: 12px;
  padding-right: 8px;
}

:dir(rtl) .entry-tag {
  margin-left: 0;
  margin-right: 4px;
  padding-left: 8px;
  padding-right: 12px;
}

.entry-tag.button {
  box-shadow: none;
  background-color: transparent;
}

.entry-tag.button:not(:hover):not(:active) {
  color: rgba(255, 255, 255, 0.7);
}

editablelabel>stack>text {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.7);
  outline: 0 solid transparent;
  outline-offset: 2px;
}

/***********
 * Buttons *
 ***********/
@keyframes needs-attention {
  from {
    background-image: radial-gradient(farthest-side, #8caaee 0%, rgba(140, 170, 238, 0) 0%);
  }

  to {
    background-image: radial-gradient(farthest-side, #8caaee 95%, rgba(140, 170, 238, 0));
  }
}

infobar.warning>revealer>box button,
infobar.warning:backdrop>revealer>box button,
popover.touch-selection button,
popover.magnifier button,
headerbar.selection-mode button:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
}

infobar.warning>revealer>box button:focus,
popover.touch-selection button:focus,
popover.magnifier button:focus,
headerbar.selection-mode button:focus:not(.suggested-action):not(.destructive-action),
infobar.warning>revealer>box button:hover,
popover.touch-selection button:hover,
popover.magnifier button:hover,
headerbar.selection-mode button:hover:not(.suggested-action):not(.destructive-action),
infobar.warning>revealer>box button:active,
popover.touch-selection button:active,
popover.magnifier button:active,
headerbar.selection-mode button:active:not(.suggested-action):not(.destructive-action),
infobar.warning>revealer>box button:checked,
popover.touch-selection button:checked,
popover.magnifier button:checked,
headerbar.selection-mode button:checked:not(.suggested-action):not(.destructive-action) {
  color: #FFFFFF;
}

infobar.warning>revealer>box button:disabled,
popover.touch-selection button:disabled,
popover.magnifier button:disabled,
headerbar.selection-mode button:disabled:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.32);
}

infobar.warning>revealer>box button:checked:disabled,
popover.touch-selection button:checked:disabled,
popover.magnifier button:checked:disabled,
headerbar.selection-mode button:checked:disabled:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.5);
}

headerbar popover.background button:not(.suggested-action):not(.destructive-action):not(.flat),
button {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: rgba(255, 255, 255, 0.08);
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
  outline: 0 solid transparent;
  outline-offset: 2px;
  color: #FFFFFF;
}

headerbar popover.background button:focus:not(.suggested-action):not(.destructive-action):not(.flat),
button:focus {
  outline: 2px solid rgba(140, 170, 238, 0.35);
  outline-offset: 0;
}

headerbar popover.background button:hover:not(.suggested-action):not(.destructive-action):not(.flat),
button:hover {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
  -gtk-icon-filter: brightness(1.2);
}

headerbar popover.background button.keyboard-activating:not(.suggested-action):not(.destructive-action):not(.flat),
button.keyboard-activating,
headerbar popover.background button:active:not(.suggested-action):not(.destructive-action):not(.flat),
button:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, border 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}

headerbar popover.background button:disabled:not(.suggested-action):not(.destructive-action):not(.flat),
button:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
  outline-color: transparent;
}

headerbar popover.background button:checked:not(.suggested-action):not(.destructive-action):not(.flat),
button:checked {
  background-color: #8caaee;
  color: rgba(0, 0, 0, 0.87);
}

headerbar popover.background button:checked:hover:not(.suggested-action):not(.destructive-action):not(.flat),
button:checked:hover {
  outline-color: transparent;
  background-color: #a2baf1;
  color: rgba(0, 0, 0, 0.87);
}

headerbar popover.background button:checked:disabled:not(.suggested-action):not(.destructive-action):not(.flat),
button:checked:disabled {
  outline-color: transparent;
  background-color: rgba(140, 170, 238, 0.35);
  color: rgba(0, 0, 0, 0.38);
}

placessidebar row button.sidebar-button,
calendar>header>button,
scrollbar button,
notebook>header>tabs>arrow,
popover modelbutton,
spinbutton>button,
splitbutton.flat>button,
splitbutton.flat>menubutton>button {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1);
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
  background-color: transparent;
  outline: 0 solid transparent;
  outline-offset: 2px;
  color: rgba(255, 255, 255, 0.7);
}

placessidebar row button.sidebar-button:focus:not(:hover):not(:active),
calendar>header>button:focus:not(:hover):not(:active),
scrollbar button:focus:not(:hover):not(:active),
notebook>header>tabs>arrow:focus:not(:hover):not(:active),
popover modelbutton:focus:not(:hover):not(:active),
spinbutton>button:focus:not(:hover):not(:active),
splitbutton.flat>button:focus:not(:hover):not(:active),
splitbutton.flat>menubutton>button:focus:not(:hover):not(:active) {
  color: #FFFFFF;
  outline: 2px solid rgba(255, 255, 255, 0.04);
  outline-offset: -2px;
}

placessidebar row button.sidebar-button:hover,
calendar>header>button:hover,
scrollbar button:hover,
notebook>header>tabs>arrow:hover,
popover modelbutton:hover,
spinbutton>button:hover,
splitbutton.flat>button:hover,
splitbutton.flat>menubutton>button:hover {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
}

placessidebar row button.sidebar-button:active,
calendar>header>button:active,
scrollbar button:active,
notebook>header>tabs>arrow:active,
popover modelbutton:active,
spinbutton>button:active,
splitbutton.flat>button:active,
splitbutton.flat>menubutton>button:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
}

placessidebar row button.sidebar-button:disabled,
calendar>header>button:disabled,
scrollbar button:disabled,
notebook>header>tabs>arrow:disabled,
popover modelbutton:disabled,
spinbutton>button:disabled,
splitbutton.flat>button:disabled,
splitbutton.flat>menubutton>button:disabled {
  color: rgba(255, 255, 255, 0.32);
  background-color: transparent;
}

filechooser #pathbarbox>stack>box>button,
window.messagedialog .response-area>box>button,
window.dialog.message .dialog-action-area>button,
.app-notification button,
headerbar button:not(.suggested-action):not(.destructive-action),
.toolbar button,
dropdown>.linked:not(.vertical)>button:not(:only-child),
combobox>.linked:not(.vertical)>button:not(:only-child),
splitbutton.suggested-action>button,
splitbutton.suggested-action>menubutton>button,
splitbutton.destructive-action>button,
splitbutton.destructive-action>menubutton>button,
splitbutton.opaque>button,
splitbutton.opaque>menubutton>button,
menubutton.suggested-action>button,
menubutton.destructive-action>button,
menubutton.opaque>button,
menubutton.flat>button,
button.flat {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1);
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
  background-color: transparent;
  outline: 0 solid transparent;
  outline-offset: 2px;
  color: rgba(255, 255, 255, 0.7);
}

filechooser #pathbarbox>stack>box>button:focus:not(:hover):not(:active),
window.messagedialog .response-area>box>button:focus:not(:hover):not(:active),
window.dialog.message .dialog-action-area>button:focus:not(:hover):not(:active),
.app-notification button:focus:not(:hover):not(:active),
headerbar button:focus:not(:hover):not(:active):not(.suggested-action):not(.destructive-action),
.toolbar button:focus:not(:hover):not(:active),
dropdown>.linked:not(.vertical)>button:focus:not(:hover):not(:active):not(:only-child),
combobox>.linked:not(.vertical)>button:focus:not(:hover):not(:active):not(:only-child),
splitbutton.suggested-action>button:focus:not(:hover):not(:active),
splitbutton.suggested-action>menubutton>button:focus:not(:hover):not(:active),
splitbutton.destructive-action>button:focus:not(:hover):not(:active),
splitbutton.destructive-action>menubutton>button:focus:not(:hover):not(:active),
splitbutton.opaque>button:focus:not(:hover):not(:active),
splitbutton.opaque>menubutton>button:focus:not(:hover):not(:active),
menubutton.suggested-action>button:focus:not(:hover):not(:active),
menubutton.destructive-action>button:focus:not(:hover):not(:active),
menubutton.opaque>button:focus:not(:hover):not(:active),
menubutton.flat>button:focus:not(:hover):not(:active),
button.flat:focus:not(:hover):not(:active) {
  color: #FFFFFF;
  outline: 2px solid rgba(255, 255, 255, 0.04);
  outline-offset: -2px;
}

filechooser #pathbarbox>stack>box>button:hover,
window.messagedialog .response-area>box>button:hover,
window.dialog.message .dialog-action-area>button:hover,
.app-notification button:hover,
headerbar button:hover:not(.suggested-action):not(.destructive-action),
.toolbar button:hover,
dropdown>.linked:not(.vertical)>button:hover:not(:only-child),
combobox>.linked:not(.vertical)>button:hover:not(:only-child),
splitbutton.suggested-action>button:hover,
splitbutton.suggested-action>menubutton>button:hover,
splitbutton.destructive-action>button:hover,
splitbutton.destructive-action>menubutton>button:hover,
splitbutton.opaque>button:hover,
splitbutton.opaque>menubutton>button:hover,
menubutton.suggested-action>button:hover,
menubutton.destructive-action>button:hover,
menubutton.opaque>button:hover,
menubutton.flat>button:hover,
button.flat:hover {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
}

filechooser #pathbarbox>stack>box>button:active,
window.messagedialog .response-area>box>button:active,
window.dialog.message .dialog-action-area>button:active,
.app-notification button:active,
headerbar button:active:not(.suggested-action):not(.destructive-action),
.toolbar button:active,
dropdown>.linked:not(.vertical)>button:active:not(:only-child),
combobox>.linked:not(.vertical)>button:active:not(:only-child),
splitbutton.suggested-action>button:active,
splitbutton.suggested-action>menubutton>button:active,
splitbutton.destructive-action>button:active,
splitbutton.destructive-action>menubutton>button:active,
splitbutton.opaque>button:active,
splitbutton.opaque>menubutton>button:active,
menubutton.suggested-action>button:active,
menubutton.destructive-action>button:active,
menubutton.opaque>button:active,
menubutton.flat>button:active,
button.flat:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
}

filechooser #pathbarbox>stack>box>button:disabled,
window.messagedialog .response-area>box>button:disabled,
window.dialog.message .dialog-action-area>button:disabled,
.app-notification button:disabled,
headerbar button:disabled:not(.suggested-action):not(.destructive-action),
.toolbar button:disabled,
dropdown>.linked:not(.vertical)>button:disabled:not(:only-child),
combobox>.linked:not(.vertical)>button:disabled:not(:only-child),
splitbutton.suggested-action>button:disabled,
splitbutton.suggested-action>menubutton>button:disabled,
splitbutton.destructive-action>button:disabled,
splitbutton.destructive-action>menubutton>button:disabled,
splitbutton.opaque>button:disabled,
splitbutton.opaque>menubutton>button:disabled,
menubutton.suggested-action>button:disabled,
menubutton.destructive-action>button:disabled,
menubutton.opaque>button:disabled,
menubutton.flat>button:disabled,
button.flat:disabled {
  color: rgba(255, 255, 255, 0.32);
  background-color: transparent;
}

filechooser #pathbarbox>stack>box>button:checked,
window.messagedialog .response-area>box>button:checked,
window.dialog.message .dialog-action-area>button:checked,
.app-notification button:checked,
headerbar button:checked:not(.suggested-action):not(.destructive-action),
.toolbar button:checked,
dropdown>.linked:not(.vertical)>button:checked:not(:only-child),
combobox>.linked:not(.vertical)>button:checked:not(:only-child),
splitbutton.suggested-action>button:checked,
splitbutton.suggested-action>menubutton>button:checked,
splitbutton.destructive-action>button:checked,
splitbutton.destructive-action>menubutton>button:checked,
splitbutton.opaque>button:checked,
splitbutton.opaque>menubutton>button:checked,
menubutton.suggested-action>button:checked,
menubutton.destructive-action>button:checked,
menubutton.opaque>button:checked,
menubutton.flat>button:checked,
button.flat:checked {
  background-color: alpha(currentColor, 0.1);
  color: #FFFFFF;
}

filechooser #pathbarbox>stack>box>button:checked:disabled,
window.messagedialog .response-area>box>button:checked:disabled,
window.dialog.message .dialog-action-area>button:checked:disabled,
.app-notification button:checked:disabled,
headerbar button:checked:disabled:not(.suggested-action):not(.destructive-action),
.toolbar button:checked:disabled,
dropdown>.linked:not(.vertical)>button:checked:disabled:not(:only-child),
combobox>.linked:not(.vertical)>button:checked:disabled:not(:only-child),
splitbutton.suggested-action>button:checked:disabled,
splitbutton.suggested-action>menubutton>button:checked:disabled,
splitbutton.destructive-action>button:checked:disabled,
splitbutton.destructive-action>menubutton>button:checked:disabled,
splitbutton.opaque>button:checked:disabled,
splitbutton.opaque>menubutton>button:checked:disabled,
menubutton.suggested-action>button:checked:disabled,
menubutton.destructive-action>button:checked:disabled,
menubutton.opaque>button:checked:disabled,
menubutton.flat>button:checked:disabled,
button.flat:checked:disabled {
  background-color: alpha(currentColor, 0.1);
  color: rgba(255, 255, 255, 0.5);
}

button.opaque {
  box-shadow: none;
}

.osd button.opaque:focus:focus-visible {
  outline-color: rgba(255, 255, 255, 0.15);
}

button.opaque:hover {
  background-image: image(alpha(currentColor, 0.1));
}

button.keyboard-activating.opaque,
button.opaque:active {
  background-image: image(rgba(0, 0, 0, 0.2));
}

button.opaque:checked {
  background-image: image(rgba(0, 0, 0, 0.15));
}

button.opaque:checked:hover {
  background-image: image(rgba(0, 0, 0, 0.05));
}

button.opaque:checked.keyboard-activating,
button.opaque:checked:active {
  background-image: image(rgba(0, 0, 0, 0.3));
}

.nautilus-window .floating-bar button,
placessidebar row button.sidebar-button,
notebook>header>tabs>tab button.flat,
popover.menu box.circular-buttons button.circular.image-button.model,
spinbutton>button {
  min-height: 24px;
  min-width: 24px;
  padding: 0;
  border-radius: 9999px;
}

button {
  min-height: 24px;
  min-width: 16px;
  padding: 6px 10px;
  border-radius: 6px;
  font-weight: 500;
}

button:drop(active) {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}

button separator {
  margin: 4px 1px;
}

button.opaque {
  background-color: #595d6b;
  color: #FFFFFF;
}

button.text-button {
  min-width: 32px;
  padding-left: 16px;
  padding-right: 16px;
}

button.image-button {
  min-width: 24px;
  padding: 6px;
}

button.text-button.image-button,
button.image-text-button {
  min-width: 24px;
  padding: 6px;
  border-radius: 6px;
}

button.text-button.image-button>box,
button.text-button.image-button>box>box,
button.image-text-button>box,
button.image-text-button>box>box {
  border-spacing: 4px;
}

button.text-button.image-button>box>label,
button.text-button.image-button>box>box>label,
button.image-text-button>box>label,
button.image-text-button>box>box>label {
  padding-left: 2px;
  padding-right: 2px;
}

button.text-button.image-button label:first-child,
button.image-text-button label:first-child {
  margin-left: 10px;
}

button.text-button.image-button label:last-child,
button.image-text-button label:last-child {
  margin-right: 10px;
}

button.text-button.image-button.flat label:first-child,
button.image-text-button.flat label:first-child {
  margin-left: 6px;
}

button.text-button.image-button.flat label:last-child,
button.image-text-button.flat label:last-child {
  margin-right: 6px;
}

button.text-button.image-button image:not(:only-child),
button.image-text-button image:not(:only-child) {
  margin: 0 4px;
}

button.arrow-button {
  padding-left: 9px;
  padding-right: 9px;
}

button.arrow-button>box {
  border-spacing: 4px;
}

button.arrow-button.text-button {
  padding-left: 16px;
  padding-right: 16px;
}

button.arrow-button.text-button>box {
  border-spacing: 6px;
}

menubutton.pill>button,
button.pill {
  padding: 9px 30px;
  border-radius: 9999px;
}

button.card {
  background-color: rgba(255, 255, 255, 0.04);
  background-clip: padding-box;
  font-weight: inherit;
  border: 1px solid rgba(255, 255, 255, 0.12);
  background-clip: border-box;
}

button.card:hover {
  background-image: none;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}

button.card.keyboard-activating,
button.card:active {
  background-image: none;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, border 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}

button.card:checked {
  background-image: none;
  background-color: #8caaee;
  color: rgba(0, 0, 0, 0.87);
  border-color: #8caaee;
}

button.card:checked:hover {
  background-image: none;
  outline-color: transparent;
  background-color: #a2baf1;
  color: rgba(0, 0, 0, 0.87);
}

button.card:checked:disabled {
  outline-color: transparent;
  background-color: rgba(140, 170, 238, 0.35);
  color: rgba(0, 0, 0, 0.38);
}

button.card:checked.keyboard-activating,
button.card:checked:active {
  background-image: none;
}

button.card:checked.has-open-popup {
  background-image: none;
}

button.card:drop(active) {
  color: #FF7043;
  box-shadow: inset 0 0 0 1px #FF7043;
}

.linked:not(.vertical)>button:focus,
.linked.vertical>button:focus {
  box-shadow: none;
  outline: none;
}

.linked:not(.vertical)>button.flat:not(:only-child),
.linked.vertical>button.flat:not(:only-child) {
  background-color: alpha(currentColor, 0.05);
}

.linked:not(.vertical)>button.flat:focus,
.linked.vertical>button.flat:focus {
  box-shadow: none;
  outline: none;
}

.linked:not(.vertical)>menubutton>button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

button.osd {
  min-width: 24px;
  min-width: 24px;
  padding: 6px;
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.35);
  color: white;
}

button.osd>image {
  padding: 0;
}

button.osd.remove-button {
  padding: 0;
}

button.osd:focus {
  outline-color: transparent;
}

button.osd:hover {
  background-color: rgba(0, 0, 0, 0.45);
  color: white;
}

button.osd:active {
  background-color: rgba(0, 0, 0, 0.65);
  color: white;
}

button.osd:disabled {
  background-color: rgba(0, 0, 0, 0.15);
  color: rgba(255, 255, 255, 0.35);
}

button.suggested-action {
  background-color: #8caaee;
  color: rgba(0, 0, 0, 0.87);
  box-shadow: none;
}

button.suggested-action:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
  outline-color: transparent;
}

button.suggested-action:hover {
  box-shadow: inset 0 0 0 9999px transparent, 0 2px 2.4px -1px rgba(140, 170, 238, 0.2), 0 4px 3px 0 rgba(140, 170, 238, 0.14), 0 1px 6px 0 rgba(140, 170, 238, 0.12);
}

button.suggested-action:checked {
  background-color: rgba(105, 128, 179, 0.961);
}

button.suggested-action:checked:hover {
  box-shadow: inset 0 0 0 9999px transparent, 0 3px 3px -3px rgba(140, 170, 238, 0.3), 0 2px 3px -1px rgba(140, 170, 238, 0.24), 0 2px 5px 0 rgba(140, 170, 238, 0.12);
}

button.suggested-action:focus {
  box-shadow: 0 0 0 2px rgba(140, 170, 238, 0.35);
}

button.suggested-action.flat {
  background-color: transparent;
  color: #8caaee;
}

button.suggested-action.flat:disabled {
  color: rgba(255, 255, 255, 0.32);
  background-color: transparent;
}

button.suggested-action.flat:checked {
  background-color: rgba(140, 170, 238, 0.3);
}

button.destructive-action {
  background-color: #F44336;
  color: #FFFFFF;
  box-shadow: none;
}

button.destructive-action:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
  outline-color: transparent;
}

button.destructive-action:hover {
  box-shadow: inset 0 0 0 9999px transparent, 0 2px 2.4px -1px rgba(244, 67, 54, 0.2), 0 4px 3px 0 rgba(244, 67, 54, 0.14), 0 1px 6px 0 rgba(244, 67, 54, 0.12);
}

button.destructive-action:checked {
  background-color: #f77b72;
}

button.destructive-action:checked:hover {
  box-shadow: inset 0 0 0 9999px transparent, 0 3px 3px -3px rgba(244, 67, 54, 0.3), 0 2px 3px -1px rgba(244, 67, 54, 0.24), 0 2px 5px 0 rgba(244, 67, 54, 0.12);
}

button.destructive-action:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.35);
}

button.destructive-action.flat {
  background-color: transparent;
  color: #F44336;
}

button.destructive-action.flat:disabled {
  color: rgba(255, 255, 255, 0.32);
  background-color: transparent;
}

button.destructive-action.flat:checked {
  background-color: rgba(244, 67, 54, 0.3);
}

stackswitcher>button>label {
  margin: 0 -6px;
  padding: 0 6px;
}

stackswitcher>button>image {
  margin: -3px -6px;
  padding: 3px 6px;
}

stackswitcher>button.needs-attention:checked>label,
stackswitcher>button.needs-attention:checked>image {
  animation: none;
  background-image: none;
}

button.font>box,
button.file>box {
  border-spacing: 6px;
}

button.font>box>box>label,
button.file>box>box>label {
  font-weight: bold;
}

windowcontrols button:not(.suggested-action):not(.destructive-action),
filechooser #pathbarbox>stack>box>button,
menubutton.circular>button,
button.close,
button.circular {
  border-radius: 9999px;
}

windowcontrols button:not(.suggested-action):not(.destructive-action) label,
filechooser #pathbarbox>stack>box>button label,
menubutton.circular>button label,
button.close label,
button.circular label {
  padding: 0;
}

menubutton.osd {
  background: none;
  color: inherit;
}

menubutton.suggested-action {
  background-color: #8caaee;
  color: white;
}

menubutton.destructive-action {
  background-color: #F44336;
  color: white;
}

menubutton.opaque {
  background-color: #595d6b;
  color: #FFFFFF;
}

menubutton.suggested-action,
menubutton.destructive-action,
menubutton.opaque {
  border-radius: 6px;
}

menubutton.suggested-action.circular,
menubutton.suggested-action.pill,
menubutton.destructive-action.circular,
menubutton.destructive-action.pill,
menubutton.opaque.circular,
menubutton.opaque.pill {
  border-radius: 9999px;
}

menubutton.suggested-action>button,
menubutton.suggested-action>button:checked,
menubutton.destructive-action>button,
menubutton.destructive-action>button:checked,
menubutton.opaque>button,
menubutton.opaque>button:checked {
  background-color: transparent;
  color: inherit;
}

menubutton.image-button>button {
  min-width: 24px;
  padding-left: 6px;
  padding-right: 6px;
}

menubutton arrow {
  min-height: 16px;
  min-width: 16px;
}

menubutton arrow.none {
  -gtk-icon-source: -gtk-icontheme("open-menu-symbolic");
}

menubutton arrow.down {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

menubutton arrow.up {
  -gtk-icon-source: -gtk-icontheme("pan-up-symbolic");
}

menubutton arrow.left {
  -gtk-icon-source: -gtk-icontheme("pan-start-symbolic");
}

menubutton arrow.right {
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
}

splitbutton {
  border-radius: 6px;
}

splitbutton,
splitbutton>separator {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  transition-property: background;
}

splitbutton>separator {
  margin-top: 6px;
  margin-bottom: 6px;
  background: none;
}

splitbutton>menubutton>button {
  padding-left: 4px;
  padding-right: 4px;
}

splitbutton.image-button>button {
  min-width: 24px;
  padding-left: 6px;
  padding-right: 6px;
}

splitbutton.text-button.image-button>button,
splitbutton.image-text-button>button {
  padding-left: 9px;
  padding-right: 9px;
}

splitbutton.text-button.image-button>button>box,
splitbutton.image-text-button>button>box {
  border-spacing: 6px;
}

splitbutton>button:dir(ltr),
splitbutton>menubutton>button:dir(rtl) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  margin-right: -1px;
}

splitbutton>button:dir(rtl),
splitbutton>menubutton>button:dir(ltr) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: -1px;
}

splitbutton.flat>separator {
  background: rgba(255, 255, 255, 0.12);
}

splitbutton.flat:hover,
splitbutton.flat:active,
splitbutton.flat:checked {
  background: alpha(currentColor, 0.07);
}

splitbutton.flat:hover>separator,
splitbutton.flat:active>separator,
splitbutton.flat:checked>separator {
  background: none;
}

splitbutton.flat:focus-within:focus-visible>separator {
  background: none;
}

splitbutton.flat>button,
splitbutton.flat>menubutton>button {
  border-radius: 6px;
}

splitbutton.suggested-action {
  background-color: #8caaee;
  color: white;
}

splitbutton.destructive-action {
  background-color: #F44336;
  color: white;
}

splitbutton.opaque {
  background-color: #595d6b;
  color: #FFFFFF;
}

splitbutton.suggested-action>button,
splitbutton.suggested-action>button:checked,
splitbutton.suggested-action>menubutton>button,
splitbutton.suggested-action>menubutton>button:checked,
splitbutton.destructive-action>button,
splitbutton.destructive-action>button:checked,
splitbutton.destructive-action>menubutton>button,
splitbutton.destructive-action>menubutton>button:checked,
splitbutton.opaque>button,
splitbutton.opaque>button:checked,
splitbutton.opaque>menubutton>button,
splitbutton.opaque>menubutton>button:checked {
  color: inherit;
  background-color: transparent;
}

splitbutton.suggested-action>menubutton>button:dir(ltr),
splitbutton.destructive-action>menubutton>button:dir(ltr),
splitbutton.opaque>menubutton>button:dir(ltr) {
  box-shadow: inset 1px 0 rgba(255, 255, 255, 0.12);
}

splitbutton.suggested-action>menubutton>button:dir(rtl),
splitbutton.destructive-action>menubutton>button:dir(rtl),
splitbutton.opaque>menubutton>button:dir(rtl) {
  box-shadow: inset -1px 0 rgba(255, 255, 255, 0.12);
}

splitbutton>menubutton>button>arrow.none {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

buttoncontent {
  border-spacing: 6px;
}

buttoncontent>label {
  font-weight: bold;
}

buttoncontent>label:dir(ltr) {
  padding-right: 2px;
}

buttoncontent>label:dir(rtl) {
  padding-left: 2px;
}

.arrow-button>box>buttoncontent>label:dir(ltr),
splitbutton>button>buttoncontent>label:dir(ltr) {
  padding-right: 0;
}

.arrow-button>box>buttoncontent>label:dir(rtl),
splitbutton>button>buttoncontent>label:dir(rtl) {
  padding-left: 0;
}

stacksidebar row.needs-attention>label,
stackswitcher>button.needs-attention>label,
stackswitcher>button.needs-attention>image {
  animation: needs-attention 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-repeat: no-repeat;
  background-position: right 3px;
  background-size: 6px 6px;
}

stacksidebar row.needs-attention>label:dir(rtl),
stackswitcher>button.needs-attention>label:dir(rtl),
stackswitcher>button.needs-attention>image:dir(rtl) {
  background-position: left 3px;
}

.linked:not(.vertical)>entry,
.linked:not(.vertical)>button,
.linked:not(.vertical)>button.image-button {
  border-radius: 0;
}

.linked:not(.vertical)>entry:first-child,
.linked:not(.vertical)>button:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.linked:not(.vertical)>entry:last-child,
.linked:not(.vertical)>button:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.linked:not(.vertical)>entry:only-child,
.linked:not(.vertical)>button:only-child {
  border-radius: 6px;
}

.linked.vertical>entry,
.linked.vertical>button,
.linked.vertical>button.image-button {
  border-radius: 0;
}

.linked.vertical>entry:first-child,
.linked.vertical>button:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.linked.vertical>entry:last-child,
.linked.vertical>button:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.linked.vertical>entry:only-child,
.linked.vertical>button:only-child {
  border-radius: 6px;
}

button.color {
  min-height: 24px;
  min-width: 24px;
  padding: 6px;
}


list>row button.image-button:not(.flat) {
  background-color: transparent;
  box-shadow: none;
  border: none;
}


list>row button.image-button:not(.flat):hover {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}


list>row button.image-button:not(.flat):active,
list>row button.image-button:not(.flat):checked {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, border 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}


list>row button.image-button:not(.flat).suggested-action {
  background-color: #8caaee;
  color: rgba(0, 0, 0, 0.87);
}


list>row button.image-button:not(.flat).destructive-action {
  background-color: #F44336;
  color: #FFFFFF;
}

/*********
 * Links *
 *********/
link {
  color: #1e66f5;
}

link:visited {
  color: #BA68C8;
}

button.link:link,
button.link:link:focus,
button.link:link:hover,
button.link:link:active {
  color: #1e66f5;
}

button.link:visited,
button.link:visited:focus,
button.link:visited:hover,
button.link:visited:active {
  color: #BA68C8;
}

button.link>label {
  text-decoration-line: underline;
}

/*****************
 * GtkSpinButton *
 *****************/
spinbutton {
  border-radius: 6px;
  padding: 0;
  border-spacing: 0;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.7);
  outline: 0 solid transparent;
  outline-offset: 2px;
}

spinbutton:focus-within {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  background-color: rgba(255, 255, 255, 0.08);
  box-shadow: inset 0 0 0 2px transparent;
  color: #FFFFFF;
  outline: 2px solid #8caaee;
  outline-offset: -2px;
}

spinbutton:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.5);
  outline: none;
}

spinbutton>text {
  border-image: none;
  border-radius: 0;
  box-shadow: none;
  background-color: transparent;
  margin: 0;
}

spinbutton>button {
  border: none;
}

spinbutton>button:focus:not(:hover):not(:active):not(:disabled) {
  box-shadow: none;
}

spinbutton:not(.vertical)>text {
  min-width: 32px;
  padding-left: 12px;
}

spinbutton:not(.vertical)>button {
  padding: 0;
  margin: 6px;
}

spinbutton:not(.vertical)>button.up:dir(ltr),
spinbutton:not(.vertical)>button.down:dir(rtl) {
  margin-left: 3px;
}

spinbutton:not(.vertical)>button.up:dir(rtl),
spinbutton:not(.vertical)>button.down:dir(ltr) {
  margin-right: 3px;
}

cell.activatable spinbutton:not(.vertical) {
  margin: 3px 0;
}

cell.activatable spinbutton:not(.vertical)>button {
  margin: 0;
  padding: 0;
  min-height: 24px;
  border-radius: 0;
}

cell.activatable spinbutton:not(.vertical)>button:last-child {
  border-radius: 0 6px 6px 0;
}

cell.activatable spinbutton:not(.vertical)>button.up:dir(ltr),
cell.activatable spinbutton:not(.vertical)>button.down:dir(rtl) {
  margin-left: 0;
}

cell.activatable spinbutton:not(.vertical)>button.up:dir(rtl),
cell.activatable spinbutton:not(.vertical)>button.down:dir(ltr) {
  margin-right: 0;
}

spinbutton.vertical>text {
  min-height: 36px;
  min-width: 42px;
  padding: 0;
}

spinbutton.vertical>button {
  padding: 0;
  margin: 6px 9px;
}

/**************
 * ComboBoxes *
 **************/
dropdown>button>box {
  border-spacing: 6px;
}

dropdown arrow,
combobox arrow {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
  min-height: 16px;
  min-width: 16px;
}

dropdown>popover.menu>contents modelbutton,
combobox>popover.menu>contents modelbutton {
  padding-left: 9px;
  padding-right: 9px;
}

dropdown button.combo cellview:dir(ltr),
combobox button.combo cellview:dir(ltr) {
  margin-left: -2px;
}

dropdown button.combo cellview:dir(rtl),
combobox button.combo cellview:dir(rtl) {
  margin-right: -2px;
}

dropdown popover,
combobox popover {
  margin-top: 4px;
  padding: 0;
}

dropdown popover listview,
combobox popover listview {
  margin: 0;
}

dropdown popover listview>row,
combobox popover listview>row {
  padding: 6px;
}

dropdown popover listview>row:selected,
combobox popover listview>row:selected {
  color: #FFFFFF;
  background-color: alpha(currentColor, 0.06);
}

dropdown popover .dropdown-searchbar,
combobox popover .dropdown-searchbar {
  padding: 6px;
}

dropdown.linked button:nth-child(2):dir(ltr),
combobox.linked button:nth-child(2):dir(ltr) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

dropdown.linked button:nth-child(2):dir(rtl),
combobox.linked button:nth-child(2):dir(rtl) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

dropdown>.linked:not(.vertical)>entry:not(:only-child),
combobox>.linked:not(.vertical)>entry:not(:only-child) {
  border-radius: 6px;
}

dropdown>.linked:not(.vertical)>entry:not(:only-child):first-child,
combobox>.linked:not(.vertical)>entry:not(:only-child):first-child {
  margin-right: -36px;
  padding-right: 36px;
}

dropdown>.linked:not(.vertical)>entry:not(:only-child):last-child,
combobox>.linked:not(.vertical)>entry:not(:only-child):last-child {
  margin-left: -36px;
  padding-left: 36px;
}

dropdown>.linked:not(.vertical)>button:not(:only-child),
combobox>.linked:not(.vertical)>button:not(:only-child) {
  min-height: 16px;
  min-width: 16px;
  margin: 6px;
  padding: 4px;
  border-radius: 6px;
}

.linked:not(.vertical)>combobox:not(:first-child)>box>button.combo {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.linked:not(.vertical)>combobox:not(:last-child)>box>button.combo {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.linked.vertical>combobox:not(:first-child)>box>button.combo {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.linked.vertical>combobox:not(:last-child)>box>button.combo {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

button.combo:only-child {
  border-radius: 6px;
  font-weight: normal;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.7);
  outline: 0 solid transparent;
  outline-offset: 2px;
}

button.combo:only-child:focus {
  color: #FFFFFF;
  outline: 2px solid rgba(255, 255, 255, 0.04);
  outline-offset: -2px;
}

button.combo:only-child:hover {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
}

button.combo:only-child:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
}

button.combo:only-child:checked {
  background-color: alpha(currentColor, 0.1);
  color: #FFFFFF;
}

button.combo:only-child:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
  outline-color: transparent;
}

/************
 * Toolbars *
 ************/
.toolbar {
  padding: 6px;
  background-color: #303446;
  border-spacing: 6px;
}

.osd .toolbar {
  background-color: transparent;
}

.app-notification,
.toolbar.osd {
  transition: box-shadow 75ms cubic-bezier(0, 0, 0.2, 1);
  padding: 6px;
  border-radius: 12px;
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.15), 0 4px 3px 0 rgba(0, 0, 0, 0.18), 0 1px 6px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.1);
  background-color: #414559;
  color: #FFFFFF;
}

.app-notification:backdrop,
.toolbar.osd:backdrop {
  box-shadow: 0 3px 2px -3px rgba(0, 0, 0, 0.2), 0 2px 2px -1px rgba(0, 0, 0, 0.24), 0 1px 3px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.1);
}

.left.app-notification,
.right.app-notification,
.top.app-notification,
.bottom.app-notification,
.toolbar.osd.left,
.toolbar.osd.right,
.toolbar.osd.top,
.toolbar.osd.bottom {
  border-radius: 0;
}

.bottom.app-notification,
.toolbar.osd.bottom {
  box-shadow: none;
  background-color: transparent;
  background-image: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1) 30%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0.4));
}

.toolbar.horizontal>separator {
  margin: 2px;
}

.toolbar.vertical>separator {
  margin: 2px;
}

.toolbar entry,
.toolbar spinbutton,
.toolbar splitbutton,
.toolbar separator:not(.sidebar),
.toolbar button,
.toolbar menubutton,
.toolbar scalebutton {
  margin-top: 0;
  margin-bottom: 0;
}

.toolbar menubutton>button,
.toolbar splitbutton>button,
.toolbar splitbutton>menubutton,
.toolbar scalebutton>button {
  margin-top: 0;
  margin-bottom: 0;
}

.toolbar switch {
  margin-top: 4px;
  margin-bottom: 4px;
}

.toolbar spinbutton entry,
.toolbar spinbutton button {
  margin: 0;
}

.toolbar popover.menu separator:not(.sidebar) {
  margin-top: 6px;
  margin-bottom: 6px;
}

searchbar>revealer>box {
  padding: 6px;
  border-spacing: 6px;
  border-style: solid;
  border-width: 0 0 1px;
  border-color: rgba(255, 255, 255, 0.12);
  background-color: #303446;
  background-clip: border-box;
  box-shadow: none;
}

searchbar>revealer>box entry,
searchbar>revealer>box button,
searchbar>revealer>box menubutton {
  margin: 0;
}

/***************
 * Header bars *
 ***************/
headerbar button:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
  border: none;
}

headerbar button:hover:not(.suggested-action):not(.destructive-action),
headerbar button:active:not(.suggested-action):not(.destructive-action),
headerbar button:checked:not(.suggested-action):not(.destructive-action) {
  color: #FFFFFF;
}

headerbar button:disabled:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.32);
}

headerbar button:checked:disabled:not(.suggested-action):not(.destructive-action) {
  background-color: transparent;
  color: rgba(255, 255, 255, 0.5);
}

headerbar button:backdrop:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.5);
}

headerbar button:backdrop:focus:not(.suggested-action):not(.destructive-action),
headerbar button:backdrop:hover:not(.suggested-action):not(.destructive-action),
headerbar button:backdrop:active:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
}

headerbar button:backdrop:disabled:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.32);
}

headerbar button:backdrop:checked:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
}

headerbar button:backdrop:checked:disabled:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.32);
}

headerbar entry {
  background-color: rgba(255, 255, 255, 0.04);
  color: #FFFFFF;
}

headerbar entry:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
}

headerbar entry image {
  color: rgba(255, 255, 255, 0.7);
}

headerbar entry image:hover,
headerbar entry image:active {
  color: #FFFFFF;
}

headerbar entry image:disabled {
  color: rgba(255, 255, 255, 0.5);
}

headerbar {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), color 75ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 -1px rgba(255, 255, 255, 0.12);
  background-color: #232634;
  color: #FFFFFF;
  min-height: 48px;
  padding: 0;
  margin: 0;
}

headerbar:disabled {
  color: rgba(255, 255, 255, 0.5);
}

headerbar:backdrop {
  background-color: #303446;
  color: rgba(255, 255, 255, 0.7);
}

headerbar:backdrop:disabled {
  color: rgba(255, 255, 255, 0.32);
}

headerbar.flat,
headerbar.flat:backdrop {
  background: none;
  box-shadow: none;
  transition: none;
}

headerbar .title {
  padding: 0 12px;
  font-weight: bold;
}

headerbar .subtitle {
  padding: 0 12px;
  font-size: smaller;
}

headerbar .subtitle,
headerbar .dim-label,
headerbar row.expander image.expander-row-arrow,
row.expander headerbar image.expander-row-arrow,
headerbar row label.subtitle,
row headerbar label.subtitle {
  transition: color 75ms cubic-bezier(0, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.7);
}

headerbar .subtitle:backdrop,
headerbar .dim-label:backdrop,
headerbar row.expander image.expander-row-arrow:backdrop,
row.expander headerbar image.expander-row-arrow:backdrop,
headerbar row label.subtitle:backdrop,
row headerbar label.subtitle:backdrop {
  color: rgba(255, 255, 255, 0.5);
}

headerbar .titlebar {
  background-color: transparent;
  box-shadow: none;
}

headerbar headerbar+separator {
  background-color: rgba(255, 255, 255, 0.12);
}

headerbar>windowhandle>box {
  padding: 0 6px;
}

headerbar>windowhandle>box,
headerbar>windowhandle>box>box.start,
headerbar>windowhandle>box>box.end {
  border-spacing: 6px;
}

headerbar entry,
headerbar spinbutton,
headerbar button,
headerbar menubutton,
headerbar stackswitcher,
headerbar separator:not(.sidebar) {
  margin-top: 6px;
  margin-bottom: 6px;
}

headerbar menubutton>button,
headerbar spinbutton>button,
headerbar splitbutton>button,
headerbar splitbutton>menubutton,
headerbar .linked>menubutton,
headerbar entry>menubutton {
  margin-top: 0;
  margin-bottom: 0;
}

headerbar button.suggested-action:disabled,
headerbar button.destructive-action:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
  opacity: 1;
}

headerbar .linked:not(.vertical)>entry:not(:only-child) {
  border-radius: 6px;
}

headerbar .entry-tag {
  margin-top: 5px;
  margin-bottom: 5px;
}

headerbar popover.background button.suggested-action:disabled,
headerbar popover.background button.destructive-action:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
}

headerbar popover.background entry,
headerbar popover.background spinbutton,
headerbar popover.background button,
headerbar popover.background menubutton,
headerbar popover.background stackswitcher {
  margin-top: 0;
  margin-bottom: 0;
}

headerbar separator:not(.sidebar) {
  background-color: rgba(255, 255, 255, 0.12);
}

headerbar switch {
  margin-top: 12px;
  margin-bottom: 12px;
}

headerbar.selection-mode {
  transition: background-color 0.1ms 225ms, color 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: #8caaee;
  color: rgba(0, 0, 0, 0.87);
}

headerbar.selection-mode:backdrop {
  color: rgba(0, 0, 0, 0.6);
}

headerbar.selection-mode .subtitle:link {
  color: rgba(0, 0, 0, 0.87);
}

headerbar.selection-mode .selection-menu {
  padding-left: 16px;
  padding-right: 16px;
}

headerbar.selection-mode .selection-menu .arrow {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

headerbar.default-decoration {
  min-height: 24px;
  padding: 6px;
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.1);
}

headerbar.default-decoration windowcontrols button,
headerbar.default-decoration windowcontrols menubutton {
  min-width: 16px;
  min-height: 16px;
  margin: 0;
  padding: 0;
}

headerbar.default-decoration windowcontrols menubutton button {
  min-height: 20px;
  min-width: 20px;
  margin: 0;
  padding: 4px;
}

.solid-csd headerbar:dir(rtl),
.solid-csd headerbar:dir(ltr) {
  border-radius: 0;
  box-shadow: none;
}

window.devel headerbar {
  background: #232634 cross-fade(10% -gtk-icontheme("system-run-symbolic"), image(transparent)) 90% 0/256px 256px no-repeat, linear-gradient(to right, transparent 65%, rgba(140, 170, 238, 0.1)), linear-gradient(to top, #292d3d 3px, #2d3143);
}

window.devel headerbar:backdrop {
  background: #232634 cross-fade(10% -gtk-icontheme("system-run-symbolic"), image(transparent)) 90% 0/256px 256px no-repeat, image(#232634);
  /* background-color would flash */
}

/************
 * Pathbars *
 ************/

pathbar>button {
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 6px;
  background-color: alpha(currentColor, 0.08);
  border: none;
  box-shadow: none;
}


pathbar>button:disabled {
  background-color: alpha(currentColor, 0.05);
}


pathbar>button:checked {
  background-color: alpha(currentColor, 0.1);
  color: #FFFFFF;
}


pathbar>button:checked:hover {
  background-color: alpha(currentColor, 0.16);
  color: #FFFFFF;
}


pathbar>button label,
pathbar>button image {
  margin-left: 3px;
  margin-right: 3px;
}


pathbar>button.slider-button {
  padding-left: 4px;
  padding-right: 4px;
}

.pathbar {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.7);
  border: none;
  border-radius: 6px;
  padding: 2px;
}

headerbar .pathbar {
  margin-top: 6px;
  margin-bottom: 6px;
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.7);
}

.pathbar>button {
  margin-top: 0;
  margin-bottom: 0;
  min-height: 20px;
  border-radius: 4px;
  border: none;
  box-shadow: none;
}

.pathbar>button:last-child {
  background-color: alpha(currentColor, 0.1);
  color: #FFFFFF;
}

/**************
 * Tree Views *
 **************/
columnview.view,
treeview.view {
  border-left-color: #494c5c;
  border-top-color: #494c5c;
}

columnview.view:hover,
columnview.view:selected,
treeview.view:hover,
treeview.view:selected {
  border-radius: 0;
}

columnview.view:focus,
treeview.view:focus {
  box-shadow: none;
  outline: none;
}

columnview.view.separator,
treeview.view.separator {
  min-height: 5px;
  color: rgba(255, 255, 255, 0.12);
}

columnview.view:drop(active),
treeview.view:drop(active) {
  box-shadow: none;
}

columnview.view:drop(active).after,
treeview.view:drop(active).after {
  border-top-style: none;
}

columnview.view:drop(active).before,
treeview.view:drop(active).before {
  border-bottom-style: none;
}

columnview.view>dndtarget:drop(active),
treeview.view>dndtarget:drop(active) {
  border-style: solid none;
  border-width: 1px;
  border-color: alpha(currentColor, 0.06);
}

columnview.view>dndtarget:drop(active).after,
treeview.view>dndtarget:drop(active).after {
  border-top-style: none;
}

columnview.view>dndtarget:drop(active).before,
treeview.view>dndtarget:drop(active).before {
  border-bottom-style: none;
}

columnview.view.expander,
treeview.view.expander {
  min-width: 16px;
  min-height: 16px;
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
  color: rgba(255, 255, 255, 0.7);
}

columnview.view.expander:dir(rtl),
treeview.view.expander:dir(rtl) {
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic-rtl");
}

columnview.view.expander:hover,
treeview.view.expander:hover {
  color: #FFFFFF;
}

columnview.view.expander:selected,
treeview.view.expander:selected {
  color: #FFFFFF;
}

columnview.view.expander:selected:hover,
treeview.view.expander:selected:hover {
  color: #FFFFFF;
}

columnview.view.expander:checked,
treeview.view.expander:checked {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

columnview.view.expander:disabled,
treeview.view.expander:disabled {
  color: rgba(255, 255, 255, 0.32);
}

columnview.view.progressbar,
treeview.view.progressbar {
  border-bottom: 6px solid #8caaee;
  box-shadow: none;
  background-color: transparent;
  background-image: none;
}

columnview.view.progressbar:selected:hover,
treeview.view.progressbar:selected:hover {
  box-shadow: none;
}

columnview.view.trough,
treeview.view.trough {
  border-bottom: 6px solid rgba(255, 255, 255, 0.12);
  box-shadow: none;
  background-color: transparent;
  background-image: none;
}

columnview.view.trough:selected:hover,
treeview.view.trough:selected:hover {
  box-shadow: none;
}

columnview.view>header>button,
treeview.view>header>button {
  padding: 2px 6px;
  border-style: none solid solid none;
  border-width: 1px;
  border-color: rgba(255, 255, 255, 0.12);
  border-radius: 0;
  background-clip: border-box;
}

columnview.view>header>button:not(:focus):not(:hover):not(:active),
treeview.view>header>button:not(:focus):not(:hover):not(:active) {
  color: rgba(255, 255, 255, 0.7);
}

columnview.view>header>button,
columnview.view>header>button:disabled,
treeview.view>header>button,
treeview.view>header>button:disabled {
  background-color: #303446;
}

columnview.view>header>button:last-child,
treeview.view>header>button:last-child {
  border-right-style: none;
}

columnview.view button.dnd,
columnview.view header.button.dnd,
treeview.view button.dnd,
treeview.view header.button.dnd {
  padding: 2px 6px;
  border-style: none solid solid;
  border-width: 1px;
  border-color: rgba(255, 255, 255, 0.12);
  border-radius: 0;
  box-shadow: none;
  background-color: #303446;
  background-clip: border-box;
  color: #8caaee;
}

columnview.view acceleditor>label,
treeview.view acceleditor>label {
  background-color: #8caaee;
}

stack.view treeview.view {
  min-height: 36px;
}

/*********
 * Menus *
 *********/
menubar {
  padding: 0;
  background-color: #232634;
  color: #FFFFFF;
}

menubar:backdrop {
  background-color: #303446;
  color: rgba(255, 255, 255, 0.7);
}

.csd menubar {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
}

menubar>item {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 20px;
  padding: 4px 8px;
  color: rgba(255, 255, 255, 0.7);
}

menubar>item:selected {
  transition: none;
  background-color: alpha(currentColor, 0.1);
  color: #FFFFFF;
}

menubar>item:disabled {
  color: rgba(255, 255, 255, 0.32);
}

menubar>item label:disabled {
  color: inherit;
}

menubar>item popover.menu popover.menu {
  margin-left: 9px;
}

menubar>item popover.menu.background popover.menu.background>contents {
  margin: 0;
  border-radius: 12px;
}

/**********************
 * Popover Base Menus *
 **********************/
popover.menu box.inline-buttons {
  color: #FFFFFF;
  padding: 0 6px;
}

popover.menu box.inline-buttons button.image-button.model {
  min-height: 28px;
  min-width: 28px;
  padding: 0;
  border: none;
  outline: none;
  transition: none;
}

popover.menu box.inline-buttons button.image-button.model:selected {
  background: image(alpha(currentColor, 0.06));
}

popover.menu box.circular-buttons {
  padding: 6px;
}

popover.menu box.circular-buttons button.circular.image-button.model {
  padding: 6px;
}

popover.menu box.circular-buttons button.circular.image-button.model:focus {
  background-color: alpha(currentColor, 0.06);
}

popover.menu arrow.left,
popover.menu radio.left,
popover.menu check.left {
  margin-left: 0;
  margin-right: 0;
}

popover.menu arrow.right,
popover.menu radio.right,
popover.menu check.right {
  margin-left: 0;
  margin-right: 0;
}

popover.menu label.title {
  font-weight: bold;
  padding: 4px 26px;
}

/************
 * Popovers *
 ************/
popover.background {
  font: initial;
}

popover.background,
popover.background:backdrop {
  background-color: transparent;
}

popover>arrow,
popover>contents {
  transition: box-shadow 75ms cubic-bezier(0, 0, 0.2, 1);
  padding: 6px;
  background-color: #414559;
  border-radius: 12px;
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-clip: border-box;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.75), 0 2px 3px -1px rgba(0, 0, 0, 0.05), 0 4px 6px 0 rgba(0, 0, 0, 0.06), 0 1px 10px 0 rgba(0, 0, 0, 0.05);
}

popover>contents>list,
popover>contents>.view,
popover>contents>toolbar {
  border-style: none;
  box-shadow: none;
  background-color: transparent;
}

popover>contents separator {
  background-color: rgba(255, 255, 255, 0.12);
  margin: 3px 0;
}

popover>contents list separator {
  margin: 0;
}

popover>contents list>row {
  border-radius: 6px;
}

popover>contents stack>box {
  padding: 0;
}

popover>contents>box>button {
  margin: 0;
}

popover .view:not(:selected),
popover toolbar {
  background-color: #414559;
}

popover.menu button,
popover button.model {
  min-height: 32px;
  padding: 0 8px;
}

popover modelbutton {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 22px;
  min-width: 56px;
  padding: 3px 9px;
  color: #FFFFFF;
  font: initial;
  border-radius: 6px;
}

popover modelbutton:focus:not(:hover) {
  transition: none;
  box-shadow: none;
  outline: none;
}

popover modelbutton:disabled {
  color: rgba(255, 255, 255, 0.5);
}

popover modelbutton accelerator {
  color: rgba(255, 255, 255, 0.5);
  margin-left: 30px;
}

popover modelbutton accelerator:disabled {
  color: rgba(255, 255, 255, 0.12);
}

popover modelbutton arrow.left {
  -gtk-icon-source: -gtk-icontheme("go-previous-symbolic");
}

popover modelbutton arrow.right {
  -gtk-icon-source: -gtk-icontheme("go-next-symbolic");
}

.osd popover,
popover.touch-selection,
popover.magnifier {
  background-color: transparent;
}

magnifier {
  background-color: #303446;
}

/*************
 * Notebooks *
 *************/
tabbar tab,
tabbar tabbox>tabboxchild>tab,
notebook>header>tabs>tab {
  min-height: 24px;
  min-width: 24px;
  padding: 3px 6px;
  border: none;
  background-clip: padding-box;
  font-weight: 500;
  border-radius: 6px;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1), outline 300ms cubic-bezier(0, 0, 0.2, 1);
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
  background-color: transparent;
  outline: 0 solid transparent;
  outline-offset: 2px;
  color: rgba(255, 255, 255, 0.7);
}

tabbar tab:hover:not(:checked):not(:selected),
notebook>header>tabs>tab:hover:not(:checked):not(:selected) {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  box-shadow: none;
}

tabbar tab:disabled,
notebook>header>tabs>tab:disabled {
  color: rgba(255, 255, 255, 0.32);
  background-color: transparent;
}

tabbar tab:active,
notebook>header>tabs>tab:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  box-shadow: none;
}

tabbar tab:checked:not(:active),
notebook>header>tabs>tab:checked:not(:active),
tabbar tab:selected:not(:active),
notebook>header>tabs>tab:selected:not(:active) {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, background-color 0ms;
  background-color: rgba(255, 255, 255, 0.15);
  color: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

tabbar tab:checked:not(:active):disabled,
notebook>header>tabs>tab:checked:not(:active):disabled,
tabbar tab:selected:not(:active):disabled,
notebook>header>tabs>tab:selected:not(:active):disabled {
  color: rgba(255, 255, 255, 0.5);
}

frame>paned>notebook>header,
notebook.frame>header {
  background-color: rgba(255, 255, 255, 0.04);
}

notebook,
notebook.frame {
  background-color: #303446;
  border-radius: 12px;
}

notebook.frame frame>border {
  border: none;
  border-radius: 6px;
}

notebook.frame frame>list row.activatable {
  border-radius: 6px;
}

notebook>header {
  border: none;
  background-color: rgba(255, 255, 255, 0.04);
  margin: 3px;
  border-radius: 9px;
}

notebook>header.top>tabs>arrow {
  border-top-style: none;
}

notebook>header.bottom>tabs>arrow {
  border-bottom-style: none;
}

notebook>header.top>tabs>arrow,
notebook>header.bottom>tabs>arrow {
  padding-left: 4px;
  padding-right: 4px;
}

notebook>header.top>tabs>arrow.down,
notebook>header.bottom>tabs>arrow.down {
  margin-left: 0;
  -gtk-icon-source: -gtk-icontheme("pan-start-symbolic");
}

notebook>header.top>tabs>arrow.up,
notebook>header.bottom>tabs>arrow.up {
  margin-right: 0;
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
}

notebook>header.left>tabs>arrow {
  border-left-style: none;
}

notebook>header.right>tabs>arrow {
  border-right-style: none;
}

notebook>header.left>tabs>arrow,
notebook>header.right>tabs>arrow {
  padding-top: 4px;
  padding-bottom: 4px;
}

notebook>header.left>tabs>arrow.down,
notebook>header.right>tabs>arrow.down {
  margin-top: 0;
  -gtk-icon-source: -gtk-icontheme("pan-up-symbolic");
}

notebook>header.left>tabs>arrow.up,
notebook>header.right>tabs>arrow.up {
  margin-bottom: 0;
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

notebook>header>tabs>arrow {
  min-height: 16px;
  min-width: 16px;
  border-radius: 6px;
}

notebook>header>tabs>tab {
  margin: 3px;
}

notebook>header>tabs>tab>box {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  margin: -6px -12px;
  padding: 6px 12px;
}

notebook>header>tabs>tab>box:drop(active) {
  background-color: rgba(255, 255, 255, 0.12);
  color: #FFFFFF;
}

notebook>header>tabs>tab button.flat:last-child {
  margin-left: 6px;
  margin-right: -3px;
}

notebook>header>tabs>tab button.flat:first-child {
  margin-left: -3px;
  margin-right: 6px;
}

notebook>header>tabs>tab button.close-button {
  min-width: 24px;
  min-height: 24px;
}

notebook>header.top>tabs,
notebook>header.bottom>tabs {
  padding-left: 0;
  padding-right: 0;
}

notebook>header.top>tabs:not(:only-child):first-child,
notebook>header.bottom>tabs:not(:only-child):first-child {
  margin-left: 0;
}

notebook>header.top>tabs:not(:only-child):last-child,
notebook>header.bottom>tabs:not(:only-child):last-child {
  margin-right: 0;
}

notebook>header.top>tabs>tab:not(:last-child),
notebook>header.bottom>tabs>tab:not(:last-child) {
  margin-right: 0;
}

notebook>header.top>tabs tab.reorderable-page,
notebook>header.bottom>tabs tab.reorderable-page {
  border-style: solid;
}

notebook>header.left>tabs,
notebook>header.right>tabs {
  padding-top: 0;
  padding-bottom: 0;
}

notebook>header.left>tabs:not(:only-child):first-child,
notebook>header.right>tabs:not(:only-child):first-child {
  margin-top: 0;
}

notebook>header.left>tabs:not(:only-child):last-child,
notebook>header.right>tabs:not(:only-child):last-child {
  margin-bottom: 0;
}

notebook>header.left>tabs>tab:not(:last-child),
notebook>header.right>tabs>tab:not(:last-child) {
  margin-bottom: 0;
}

notebook>header.left>tabs tab.reorderable-page,
notebook>header.right>tabs tab.reorderable-page {
  border-style: solid;
}

notebook>header>menubutton>button.image-button {
  padding: 3px;
  min-width: 24px;
  min-height: 24px;
  margin: 0 3px;
}

notebook>stack:not(:only-child) {
  background-color: transparent;
  border-radius: 6px;
}

tabbar>revealer>box {
  box-shadow: none;
}

tabbar .box {
  min-height: 36px;
  border-bottom: none;
  background: none;
}

tabbar scrolledwindow.pinned undershoot {
  border: 0 solid rgba(255, 255, 255, 0.12);
}

tabbar scrolledwindow.pinned:dir(rtl) undershoot.left {
  border-left-width: 1px;
}

tabbar scrolledwindow.pinned:dir(ltr) undershoot.right {
  border-right-width: 1px;
}

tabbar scrolledwindow.pinned tabbox>background:dir(ltr) {
  box-shadow: inset -1px 0 rgba(255, 255, 255, 0.12);
}

tabbar scrolledwindow.pinned tabbox>background:dir(rtl) {
  box-shadow: inset 1px 0 rgba(255, 255, 255, 0.12);
}

tabbar undershoot {
  transition: background 150ms ease-in-out;
}

tabbar undershoot.left {
  background: linear-gradient(to right, #303446, rgba(0, 0, 0, 0) 20px);
}

tabbar undershoot.right {
  background: linear-gradient(to left, #303446, rgba(0, 0, 0, 0) 20px);
}

tabbar .needs-attention-left undershoot.left {
  background: linear-gradient(to right, alpha(#8caaee, 0.5), alpha(#8caaee, 0.3) 1px, alpha(#8caaee, 0) 20px);
}

tabbar .needs-attention-right undershoot.right {
  background: linear-gradient(to left, alpha(#8caaee, 0.5), alpha(#8caaee, 0.3) 1px, alpha(#8caaee, 0) 20px);
}

tabbar tabbox {
  background-color: rgba(255, 255, 255, 0.04);
  background-image: none;
  padding: 0;
  margin: 0;
  border-radius: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

tabbar tabbox>background {
  background: none;
}

tabbar tabbox>separator {
  margin: 9px 0;
  min-width: 1px;
  transition: opacity 150ms ease-in-out;
}

tabbar tabbox>separator.hidden {
  opacity: 0;
}

tabbar tabbox>tabboxchild {
  margin: 0 -3px;
  padding: 0;
}

tabbar tabbox>tabboxchild>tab {
  margin: 3px;
}

tabbar tab.needs-attention {
  background-image: radial-gradient(ellipse at bottom, rgba(255, 255, 255, 0.8), alpha(#8caaee, 0.4) 10%, alpha(#8caaee, 0) 30%);
}

tabbar tab.needs-attention:hover {
  background-image: image(alpha(currentColor, 0.03)), radial-gradient(ellipse at bottom, rgba(255, 255, 255, 0.8), alpha(#8caaee, 0.4) 10%, alpha(#8caaee, 0) 30%);
}

tabbar .start-action,
tabbar .end-action {
  background-color: rgba(255, 255, 255, 0.04);
  background-clip: padding-box;
  border-color: rgba(255, 255, 255, 0.12);
  border-style: solid;
  transition: background 150ms ease-in-out;
}

tabbar .start-action button,
tabbar .end-action button {
  border: none;
  border-radius: 0;
}

tabbar .start-action:dir(ltr),
tabbar .end-action:dir(rtl) {
  border-right-width: 1px;
}

tabbar .start-action:dir(rtl),
tabbar .end-action:dir(ltr) {
  border-left-width: 1px;
}

tabbar:not(.inline) scrolledwindow.pinned undershoot {
  border-color: rgba(255, 255, 255, 0.12);
}

tabbar:not(.inline) undershoot.left {
  background: linear-gradient(to right, #232634, rgba(0, 0, 0, 0) 20px);
}

tabbar:not(.inline) undershoot.right {
  background: linear-gradient(to left, #232634, rgba(0, 0, 0, 0) 20px);
}

tabbar:not(.inline) .needs-attention-left undershoot.left {
  background: linear-gradient(to right, alpha(#8caaee, 0.5), alpha(#8caaee, 0.3) 1px, alpha(#8caaee, 0) 20px);
}

tabbar:not(.inline) .needs-attention-right undershoot.right {
  background: linear-gradient(to left, alpha(#8caaee, 0.5), alpha(#8caaee, 0.3) 1px, alpha(#8caaee, 0) 20px);
}

tabbar:not(.inline) tabbox>background {
  background-color: #232634;
}

tabbar:not(.inline) .start-action,
tabbar:not(.inline) .end-action {
  background-color: alpha(#232634, 0.6);
  border-color: rgba(255, 255, 255, 0.12);
}

tabbar:not(.inline):backdrop .box {
  background-color: #303446;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
}

dnd tab {
  min-height: 24px;
  background-color: #232634;
  color: #FFFFFF;
  box-shadow: 0 1px 5px 1px rgba(0, 0, 0, 0.09), 0 2px 14px 3px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.05);
  outline: 1px solid rgba(0, 0, 0, 0.75);
  outline-offset: -1px;
  margin: 24px;
}

dnd tab.needs-attention {
  background-image: radial-gradient(ellipse at bottom, rgba(255, 255, 255, 0.8), alpha(#8caaee, 0.4) 10%, alpha(#8caaee, 0) 30%);
}

tabbar tab,
dnd tab {
  padding: 6px;
}

tabbar tab button.image-button,
dnd tab button.image-button {
  padding: 0;
  margin: 0;
  min-width: 24px;
  min-height: 24px;
  border-radius: 9999px;
}

tabbar tab button.image-button.tab-close-button,
dnd tab button.image-button.tab-close-button {
  margin-right: -3px;
}

tabview:drop(active),
tabbox:drop(active) {
  box-shadow: none;
}

/**************
 * Scrollbars *
 **************/
scrollbar {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: #303446;
  box-shadow: none;
  outline: none;
}

scrollbar.top {
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

scrollbar.bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.12);
}

scrollbar.left {
  border-right: 1px solid rgba(255, 255, 255, 0.12);
}

scrollbar.right {
  border-left: 1px solid rgba(255, 255, 255, 0.12);
}

scrollbar>range>trough {
  border: none;
  background: none;
  padding: 0;
  outline: none;
}

scrollbar>range>trough>slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  min-width: 8px;
  min-height: 8px;
  border: 4px solid transparent;
  border-radius: 9999px;
  background-clip: padding-box;
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: none;
  outline: none;
}

scrollbar>range>trough>slider:hover {
  background-color: rgba(255, 255, 255, 0.7);
}

scrollbar>range>trough>slider:active {
  background-color: #FFFFFF;
}

scrollbar>range>trough>slider:disabled {
  background-color: rgba(255, 255, 255, 0.32);
}

scrollbar>range.fine-tune>trough>slider {
  min-width: 4px;
  min-height: 4px;
}

scrollbar>range.fine-tune.horizontal>trough>slider {
  margin: 2px 0;
}

scrollbar>range.fine-tune.vertical>trough>slider {
  margin: 0 2px;
}

scrollbar.overlay-indicator:not(.fine-tune)>range>trough>slider {
  transition-property: background-color, min-height, min-width;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering) {
  border-color: transparent;
  background-color: transparent;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering)>range>trough>slider {
  min-width: 4px;
  min-height: 4px;
  margin: 3px;
  border: 1px solid rgba(48, 52, 70, 0.3);
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering) button {
  min-width: 4px;
  min-height: 4px;
  margin: 3px;
  border: 1px solid rgba(48, 52, 70, 0.3);
  border-radius: 9999px;
  background-color: rgba(255, 255, 255, 0.5);
  background-clip: padding-box;
  -gtk-icon-source: none;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering) button:disabled {
  background-color: rgba(255, 255, 255, 0.32);
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering).horizontal>range>trough>slider {
  min-width: 24px;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering).horizontal button {
  min-width: 8px;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering).vertical>range>trough>slider {
  min-height: 24px;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering).vertical button {
  min-height: 8px;
}

scrollbar.overlay-indicator.dragging,
scrollbar.overlay-indicator.hovering {
  background-color: rgba(65, 69, 89, 0.9);
}

scrollbar.horizontal>range>trough>slider {
  min-width: 24px;
}

scrollbar.vertical>range>trough>slider {
  min-height: 24px;
}

scrollbar button {
  min-width: 16px;
  min-height: 16px;
  padding: 0;
  border-radius: 0;
}

scrollbar.vertical button.down {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

scrollbar.vertical button.up {
  -gtk-icon-source: -gtk-icontheme("pan-up-symbolic");
}

scrollbar.horizontal button.down {
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
}

scrollbar.horizontal button.up {
  -gtk-icon-source: -gtk-icontheme("pan-start-symbolic");
}

/**********
 * Switch *
 **********/
switch {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  margin: 6px 0;
  padding: 0;
  border: none;
  border-radius: 9999px;
  background-color: rgba(255, 255, 255, 0.5);
  background-clip: border-box;
  font-size: 0;
  color: transparent;
}

switch:checked {
  background-color: #8caaee;
}

switch:disabled {
  opacity: 0.5;
}

switch image {
  margin: -8px;
}

switch>slider {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  min-width: 18px;
  min-height: 18px;
  margin: 3px;
  border-radius: 9999px;
  outline: none;
  box-shadow: 0 3px 2px -2px rgba(0, 0, 0, 0.05), 0 2px 3px -1px rgba(0, 0, 0, 0.06), 0 1px 4px 0 rgba(0, 0, 0, 0.05);
  background-color: white;
  border: none;
}

switch:focus slider,
switch:hover slider,
switch:focus:hover slider {
  box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.12);
}

/*************************
 * Check and Radio items *
 *************************/
.view.content-view.check:not(list),
.content-view .tile check:not(list) {
  min-height: 40px;
  min-width: 40px;
  margin: 0;
  padding: 0;
  box-shadow: none;
  background-color: transparent;
  background-image: none;
}

.view.content-view.check:not(list):hover,
.view.content-view.check:not(list):active,
.content-view .tile check:not(list):hover,
.content-view .tile check:not(list):active {
  box-shadow: 0 0 0 10px rgba(255, 255, 255, 0.12);
}

.view.content-view.check:not(list),
.content-view .tile check:not(list) {
  -gtk-icon-source: -gtk-scaled(url("assets/selectionmode-checkbox-unchecked-dark.png"), url("assets/<EMAIL>"));
}

.view.content-view.check:not(list):checked,
.content-view .tile check:not(list):checked {
  -gtk-icon-source: -gtk-scaled(url("assets/selectionmode-checkbox-checked-dark.png"), url("assets/<EMAIL>"));
}

checkbutton,
radiobutton {
  outline: none;
  border-spacing: 3px;
}

check,
radio {
  min-height: 20px;
  min-width: 20px;
  margin: 3px;
  padding: 0;
  border-radius: 9999px;
  border: none;
  color: transparent;
  background-color: rgba(255, 255, 255, 0.12);
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 150ms cubic-bezier(0, 0, 0.2, 1);
}

check:hover,
radio:hover {
  box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.04);
  background-color: rgba(255, 255, 255, 0.15);
}

check:active,
radio:active {
  box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.12);
  background-color: rgba(255, 255, 255, 0.2);
}

check:disabled,
radio:disabled {
  background-color: rgba(255, 255, 255, 0.04);
}

check:checked,
check:indeterminate,
radio:checked,
radio:indeterminate {
  color: rgba(0, 0, 0, 0.87);
  background-color: #8caaee;
}

check:checked:hover,
check:indeterminate:hover,
radio:checked:hover,
radio:indeterminate:hover {
  box-shadow: 0 0 0 6px rgba(140, 170, 238, 0.15);
  background-color: #b8cbf5;
}

check:checked:active,
check:indeterminate:active,
radio:checked:active,
radio:indeterminate:active {
  box-shadow: 0 0 0 6px rgba(140, 170, 238, 0.2);
  background-color: #8caaee;
}

check:checked:disabled,
check:indeterminate:disabled,
radio:checked:disabled,
radio:indeterminate:disabled {
  color: rgba(0, 0, 0, 0.6);
  background-color: rgba(140, 170, 238, 0.35);
}

popover modelbutton.flat check,
popover modelbutton.flat check:focus,
popover modelbutton.flat check:hover,
popover modelbutton.flat check:focus:hover,
popover modelbutton.flat check:active,
popover modelbutton.flat check:disabled,
popover modelbutton.flat radio,
popover modelbutton.flat radio:focus,
popover modelbutton.flat radio:hover,
popover modelbutton.flat radio:focus:hover,
popover modelbutton.flat radio:active,
popover modelbutton.flat radio:disabled {
  transition: none;
  box-shadow: none;
  background-image: none;
}

popover modelbutton.flat check.left:dir(rtl),
popover modelbutton.flat radio.left:dir(rtl) {
  margin-left: -3px;
  margin-right: 6px;
}

popover modelbutton.flat check.right:dir(ltr),
popover modelbutton.flat radio.right:dir(ltr) {
  margin-left: 6px;
  margin-right: -3px;
}

popover.menu check,
popover.menu radio {
  transition: none;
  margin: 0;
  padding: 0;
}

popover.menu check:dir(ltr),
popover.menu radio:dir(ltr) {
  margin-right: 6px;
  margin-left: -3px;
}

popover.menu check:dir(rtl),
popover.menu radio:dir(rtl) {
  margin-left: 6px;
  margin-right: -3px;
}

popover.menu check,
popover.menu check:hover,
popover.menu check:disabled,
popover.menu check:checked:hover,
popover.menu check:indeterminate:hover,
popover.menu radio,
popover.menu radio:hover,
popover.menu radio:disabled,
popover.menu radio:checked:hover,
popover.menu radio:indeterminate:hover {
  box-shadow: none;
}


check {
  -gtk-icon-size: 20px;
}


check:checked {
  -gtk-icon-source: -gtk-scaled(-gtk-recolor(url("assets/scalable/checkbox-checked-symbolic.svg")), -gtk-recolor(url("assets/scalable/<EMAIL>")));
}


check:indeterminate {
  -gtk-icon-source: -gtk-scaled(-gtk-recolor(url("assets/scalable/checkbox-mixed-symbolic.svg")), -gtk-recolor(url("assets/scalable/<EMAIL>")));
}


radio {
  -gtk-icon-size: 20px;
}


radio:checked {
  -gtk-icon-source: -gtk-scaled(-gtk-recolor(url("assets/scalable/radio-checked-symbolic.svg")), -gtk-recolor(url("assets/scalable/<EMAIL>")));
}


radio:indeterminate {
  -gtk-icon-source: -gtk-scaled(-gtk-recolor(url("assets/scalable/radio-mixed-symbolic.svg")), -gtk-recolor(url("assets/scalable/<EMAIL>")));
}


popover.menu check {
  min-height: 16px;
  min-width: 16px;
  -gtk-icon-size: 16px;
}


popover.menu check:checked {
  -gtk-icon-source: -gtk-scaled(-gtk-recolor(url("assets/scalable/small-checkbox-checked-symbolic.svg")), -gtk-recolor(url("assets/scalable/<EMAIL>")));
}


popover.menu check:indeterminate {
  -gtk-icon-source: -gtk-scaled(-gtk-recolor(url("assets/scalable/small-checkbox-mixed-symbolic.svg")), -gtk-recolor(url("assets/scalable/<EMAIL>")));
}


popover.menu radio {
  min-height: 16px;
  min-width: 16px;
  -gtk-icon-size: 16px;
}


popover.menu radio:checked {
  -gtk-icon-source: -gtk-scaled(-gtk-recolor(url("assets/scalable/small-radio-checked-symbolic.svg")), -gtk-recolor(url("assets/scalable/<EMAIL>")));
}


popover.menu radio:indeterminate {
  -gtk-icon-source: -gtk-scaled(-gtk-recolor(url("assets/scalable/small-radio-mixed-symbolic.svg")), -gtk-recolor(url("assets/scalable/<EMAIL>")));
}

check:not(:checked):active {
  -gtk-icon-transform: rotate(90deg);
}

treeview.view radio,
treeview.view check,
columnview.view radio,
columnview.view check {
  padding: 0;
  margin: 0;
  transition: none;
}

treeview.view radio,
treeview.view radio:hover,
treeview.view radio:disabled,
treeview.view radio:checked:hover,
treeview.view radio:indeterminate:hover,
treeview.view check,
treeview.view check:hover,
treeview.view check:disabled,
treeview.view check:checked:hover,
treeview.view check:indeterminate:hover,
columnview.view radio,
columnview.view radio:hover,
columnview.view radio:disabled,
columnview.view radio:checked:hover,
columnview.view radio:indeterminate:hover,
columnview.view check,
columnview.view check:hover,
columnview.view check:disabled,
columnview.view check:checked:hover,
columnview.view check:indeterminate:hover {
  box-shadow: none;
}

treeview.view:hover check,
treeview.view:hover radio,
treeview.view:selected check,
treeview.view:selected radio,
treeview.view:focus check,
treeview.view:focus radio,
columnview.view:hover check,
columnview.view:hover radio,
columnview.view:selected check,
columnview.view:selected radio,
columnview.view:focus check,
columnview.view:focus radio {
  box-shadow: none;
}

treeview.view:hover check:checked,
treeview.view:hover radio:checked,
treeview.view:selected check:checked,
treeview.view:selected radio:checked,
treeview.view:focus check:checked,
treeview.view:focus radio:checked,
columnview.view:hover check:checked,
columnview.view:hover radio:checked,
columnview.view:selected check:checked,
columnview.view:selected radio:checked,
columnview.view:focus check:checked,
columnview.view:focus radio:checked {
  color: rgba(0, 0, 0, 0.87);
  background-color: #8caaee;
}

/************
 * GtkScale *
 ************/
scale {
  min-height: 2px;
  min-width: 2px;
}

scale.horizontal {
  padding: 17px 12px;
}

scale.vertical {
  padding: 12px 17px;
}

scale>trough {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  outline: none;
  background-color: rgba(255, 255, 255, 0.3);
}

scale>trough:disabled {
  background-color: rgba(255, 255, 255, 0.12);
}

scale>trough>highlight {
  transition: background-image 75ms cubic-bezier(0, 0, 0.2, 1);
  background-image: image(#8caaee);
}

scale>trough>highlight:disabled {
  background-color: #303446;
  background-image: image(rgba(255, 255, 255, 0.32));
}

scale>trough>fill {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: rgba(255, 255, 255, 0.3);
}

scale>trough>fill:disabled {
  background-color: transparent;
}

scale>trough>slider {
  min-height: 18px;
  min-width: 18px;
  margin: -8px;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  border-radius: 9999px;
  color: #8caaee;
  background-color: #303446;
  box-shadow: inset 0 0 0 2px #8caaee;
}

scale>trough>slider:hover {
  box-shadow: inset 0 0 0 2px #8caaee, 0 0 0 8px rgba(255, 255, 255, 0.12);
}

scale>trough>slider:active {
  box-shadow: inset 0 0 0 4px #8caaee, 0 0 0 8px rgba(255, 255, 255, 0.12);
}

scale>trough>slider:disabled {
  box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.32);
}

scale.fine-tune.horizontal {
  min-height: 4px;
  padding-top: 16px;
  padding-bottom: 16px;
}

scale.fine-tune.vertical {
  min-width: 4px;
  padding-left: 16px;
  padding-right: 16px;
}

scale.fine-tune>trough>slider {
  margin: -7px;
}

scale>marks,
scale>value {
  color: rgba(255, 255, 255, 0.7);
}

scale indicator {
  background-color: rgba(255, 255, 255, 0.3);
  color: transparent;
}

scale.marks-before:not(.marks-after)>trough>slider,
scale.marks-after:not(.marks-before)>trough>slider {
  transform: rotate(0);
}

scale.horizontal>marks.top {
  margin-bottom: 7px;
  margin-top: -15px;
}

scale.horizontal.fine-tune>marks.top {
  margin-bottom: 6px;
  margin-top: -14px;
}

scale.horizontal>marks.bottom {
  margin-top: 7px;
  margin-bottom: -15px;
}

scale.horizontal.fine-tune>marks.bottom {
  margin-top: 6px;
  margin-bottom: -14px;
}

scale.vertical>marks.top {
  margin-right: 7px;
  margin-left: -15px;
}

scale.vertical.fine-tune>marks.top {
  margin-right: 6px;
  margin-left: -14px;
}

scale.vertical>marks.bottom {
  margin-left: 7px;
  margin-right: -15px;
}

scale.vertical.fine-tune>marks.bottom {
  margin-left: 6px;
  margin-right: -14px;
}

scale.horizontal indicator {
  min-height: 8px;
  min-width: 1px;
}

scale.vertical indicator {
  min-height: 1px;
  min-width: 8px;
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 32px;
  min-width: 32px;
  margin: -15px;
  border-radius: 50%;
  background-size: auto, 1000% 1000%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider,
scale.horizontal.marks-before:not(.marks-after)>trough>slider:hover,
scale.horizontal.marks-before:not(.marks-after)>trough>slider:active,
scale.horizontal.marks-before:not(.marks-after)>trough>slider:disabled {
  box-shadow: none;
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider:focus {
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider:hover {
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider:focus:hover {
  background-color: alpha(currentColor, 0.16);
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider:active {
  background-size: auto, 0% 0%;
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-before-slider-dark.png"), url("assets/<EMAIL>"));
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider:disabled {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-before-slider-disabled-dark.png"), url("assets/<EMAIL>"));
}

scale.horizontal.marks-before:not(.marks-after)>trough>slider:active {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-before-slider-dark.png"), url("assets/<EMAIL>"));
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 32px;
  min-width: 32px;
  margin: -15px;
  border-radius: 50%;
  background-size: auto, 1000% 1000%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider,
scale.horizontal.marks-after:not(.marks-before)>trough>slider:hover,
scale.horizontal.marks-after:not(.marks-before)>trough>slider:active,
scale.horizontal.marks-after:not(.marks-before)>trough>slider:disabled {
  box-shadow: none;
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider:focus {
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider:hover {
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider:focus:hover {
  background-color: alpha(currentColor, 0.16);
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider:active {
  background-size: auto, 0% 0%;
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-after-slider-dark.png"), url("assets/<EMAIL>"));
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider:disabled {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-after-slider-disabled-dark.png"), url("assets/<EMAIL>"));
}

scale.horizontal.marks-after:not(.marks-before)>trough>slider:active {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-after-slider-dark.png"), url("assets/<EMAIL>"));
}

scale.vertical.marks-before:not(.marks-after)>trough>slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 32px;
  min-width: 32px;
  margin: -15px;
  border-radius: 50%;
  background-size: auto, 1000% 1000%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}

scale.vertical.marks-before:not(.marks-after)>trough>slider,
scale.vertical.marks-before:not(.marks-after)>trough>slider:hover,
scale.vertical.marks-before:not(.marks-after)>trough>slider:active,
scale.vertical.marks-before:not(.marks-after)>trough>slider:disabled {
  box-shadow: none;
}

scale.vertical.marks-before:not(.marks-after)>trough>slider:focus {
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-before:not(.marks-after)>trough>slider:hover {
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-before:not(.marks-after)>trough>slider:focus:hover {
  background-color: alpha(currentColor, 0.16);
}

scale.vertical.marks-before:not(.marks-after)>trough>slider:active {
  background-size: auto, 0% 0%;
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-before:not(.marks-after)>trough>slider {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-before-slider-dark.png"), url("assets/<EMAIL>"));
}

scale.vertical.marks-before:not(.marks-after)>trough>slider:disabled {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-before-slider-disabled-dark.png"), url("assets/<EMAIL>"));
}

scale.vertical.marks-before:not(.marks-after)>trough>slider:active {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-before-slider-dark.png"), url("assets/<EMAIL>"));
}

scale.vertical.marks-after:not(.marks-before)>trough>slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 32px;
  min-width: 32px;
  margin: -15px;
  border-radius: 50%;
  background-size: auto, 1000% 1000%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}

scale.vertical.marks-after:not(.marks-before)>trough>slider,
scale.vertical.marks-after:not(.marks-before)>trough>slider:hover,
scale.vertical.marks-after:not(.marks-before)>trough>slider:active,
scale.vertical.marks-after:not(.marks-before)>trough>slider:disabled {
  box-shadow: none;
}

scale.vertical.marks-after:not(.marks-before)>trough>slider:focus {
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-after:not(.marks-before)>trough>slider:hover {
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-after:not(.marks-before)>trough>slider:focus:hover {
  background-color: alpha(currentColor, 0.16);
}

scale.vertical.marks-after:not(.marks-before)>trough>slider:active {
  background-size: auto, 0% 0%;
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-after:not(.marks-before)>trough>slider {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-after-slider-dark.png"), url("assets/<EMAIL>"));
}

scale.vertical.marks-after:not(.marks-before)>trough>slider:disabled {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-after-slider-disabled-dark.png"), url("assets/<EMAIL>"));
}

scale.vertical.marks-after:not(.marks-before)>trough>slider:active {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-after-slider-dark.png"), url("assets/<EMAIL>"));
}

scale.color {
  min-height: 0;
  min-width: 0;
}

scale.color.horizontal {
  padding: 0 0 12px 0;
}

scale.color.horizontal>trough>slider:dir(ltr),
scale.color.horizontal>trough>slider:dir(rtl) {
  margin-bottom: -13.5px;
  margin-top: 11.5px;
}

scale.color.vertical:dir(ltr) {
  padding: 0 0 0 12px;
}

scale.color.vertical:dir(ltr) slider {
  margin-left: -13.5px;
  margin-right: 11.5px;
}

scale.color.vertical:dir(rtl) {
  padding: 0 12px 0 0;
}

scale.color.vertical:dir(rtl)>trough>slider {
  margin-right: -13.5px;
  margin-left: 11.5px;
}

/*****************
 * Progress bars *
 *****************/
progressbar {
  color: rgba(255, 255, 255, 0.7);
  font-size: smaller;
}

progressbar.horizontal trough,
progressbar.horizontal progress {
  min-height: 6px;
}

progressbar.vertical trough,
progressbar.vertical progress {
  min-width: 6px;
}

progressbar trough {
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.12);
}

progressbar progress {
  border-radius: 6px;
  background-color: #8caaee;
}

progressbar.osd {
  min-width: 6px;
  min-height: 6px;
  background-color: transparent;
  box-shadow: none;
  margin: 0;
  padding: 0;
}

progressbar.osd trough {
  background-color: transparent;
}

progressbar.osd progress {
  background-color: #8caaee;
}

progressbar trough.empty progress {
  all: unset;
}

/*************
 * Level Bar *
 *************/
levelbar.horizontal block {
  min-height: 6px;
}

levelbar.horizontal.discrete block {
  min-width: 36px;
}

levelbar.horizontal.discrete block:not(:last-child) {
  margin-right: 2px;
}

levelbar.vertical block {
  min-width: 6px;
}

levelbar.vertical.discrete block {
  min-height: 36px;
}

levelbar.vertical.discrete block:not(:last-child) {
  margin-bottom: 2px;
}

levelbar trough {
  border-radius: 6px;
}

levelbar block.low {
  background-color: #FBC02D;
}

levelbar block.high,
levelbar block:not(.empty) {
  background-color: #8caaee;
}

levelbar block.full {
  background-color: #66BB6A;
}

levelbar block.empty {
  background-color: rgba(255, 255, 255, 0.12);
}

/****************
 * Print dialog *
*****************/
window.dialog.print drawing {
  color: #FFFFFF;
  background: none;
  border: none;
  padding: 0;
}

window.dialog.print drawing paper {
  padding: 0;
  border: 1px solid rgba(255, 255, 255, 0.12);
  background-color: #303446;
  color: #FFFFFF;
}

window.dialog.print .dialog-action-box {
  margin: 12px;
}

/**********
 * Frames *
 **********/
frame,
.frame {
  border: 1px solid rgba(255, 255, 255, 0.12);
}

frame>list,
.frame>list {
  border: none;
}

frame.view,
.frame.view {
  border-radius: 6px;
}

frame.flat,
.frame.flat {
  border-style: none;
}

frame {
  border-radius: 6px;
}

frame>label {
  margin: 4px;
}

frame.flat>border,
statusbar frame>border {
  border: none;
}

actionbar>revealer>box {
  padding: 6px;
  border-spacing: 6px;
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.12);
  background-color: #303446;
  background-clip: border-box;
  border: none;
}

actionbar>revealer>box button,
actionbar>revealer>box entry,
actionbar>revealer>box menubutton,
actionbar>revealer>box menubutton>button,
actionbar>revealer>box splitbutton,
actionbar>revealer>box splitbutton>button,
actionbar>revealer>box spinbutton {
  margin: 0;
}

statusbar {
  padding: 6px 18px;
}

scrolledwindow viewport.frame {
  border: none;
}

stack scrolledwindow.frame viewport.frame list {
  border: none;
}

overshoot.top {
  background-image: radial-gradient(farthest-side at top, alpha(currentColor, 0.12) 85%, alpha(currentColor, 0)), radial-gradient(farthest-side at top, alpha(currentColor, 0.05), alpha(currentColor, 0));
  background-size: 100% 3%, 100% 50%;
  background-repeat: no-repeat;
  background-position: top;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

overshoot.bottom {
  background-image: radial-gradient(farthest-side at bottom, alpha(currentColor, 0.12) 85%, alpha(currentColor, 0)), radial-gradient(farthest-side at bottom, alpha(currentColor, 0.05), alpha(currentColor, 0));
  background-size: 100% 3%, 100% 50%;
  background-repeat: no-repeat;
  background-position: bottom;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

overshoot.left {
  background-image: radial-gradient(farthest-side at left, alpha(currentColor, 0.12) 85%, alpha(currentColor, 0)), radial-gradient(farthest-side at left, alpha(currentColor, 0.05), alpha(currentColor, 0));
  background-size: 3% 100%, 50% 100%;
  background-repeat: no-repeat;
  background-position: left;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

overshoot.right {
  background-image: radial-gradient(farthest-side at right, alpha(currentColor, 0.12) 85%, alpha(currentColor, 0)), radial-gradient(farthest-side at right, alpha(currentColor, 0.05), alpha(currentColor, 0));
  background-size: 3% 100%, 50% 100%;
  background-repeat: no-repeat;
  background-position: right;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

junction {
  border: none;
  background-image: none;
}

separator {
  min-width: 1px;
  min-height: 1px;
  background-color: rgba(255, 255, 255, 0.12);
}

stacksidebar+separator.vertical,
stacksidebar separator.horizontal,
button.font separator,
button.file separator,
separator.spacer {
  min-width: 0;
  min-height: 0;
  background-color: transparent;
  background-image: none;
}

/*********
 * Lists *
 *********/
list.content,
list.boxed-list {
  border-radius: 7px;
  box-shadow: none;
  border: 1px solid rgba(255, 255, 255, 0.12);
}

listview,
list {
  border-color: rgba(255, 255, 255, 0.12);
  background-color: #303446;
  background-clip: padding-box;
}

listview>row,
list>row {
  padding: 6px;
  background-clip: padding-box;
}

listview>row.expander,
list>row.expander {
  padding: 0px;
}

listview>row.expander .row-header,
list>row.expander .row-header {
  padding: 2px;
}

listview.horizontal row.separator:not(:last-child),
listview.separators.horizontal>row:not(.separator):not(:last-child),
list.horizontal row.separator:not(:last-child),
list.separators.horizontal>row:not(.separator):not(:last-child) {
  border-left: 1px solid rgba(255, 255, 255, 0.12);
}

listview:not(.horizontal) row.separator:not(:last-child),
listview.separators:not(.horizontal)>row:not(.separator):not(:last-child),
list:not(.horizontal) row.separator:not(:last-child),
list.separators:not(.horizontal)>row:not(.separator):not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

list.frame {
  border-radius: 6px;
}

listview.view {
  color: #FFFFFF;
  background-color: transparent;
}

popover.menu listview.view {
  padding: 0;
  border-radius: 6px;
}

popover.menu listview.view>row {
  margin-left: 0;
  margin-right: 0;
  border-radius: 6px;
}

row {
  color: rgba(255, 255, 255, 0.7);
  background-clip: padding-box;
}

row label.subtitle {
  font-size: smaller;
}

row>box.header {
  margin-left: 12px;
  margin-right: 12px;
  min-height: 48px;
}

row>box.header>.icon:disabled {
  filter: opacity(0.35);
}

row>box.header>box.title {
  margin-top: 6px;
  margin-bottom: 6px;
  border-spacing: 3px;
}

.nautilus-window .nautilus-grid-view child.activatable,
columnview.view>header>button,
treeview.view>header>button,
row.activatable {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1), font-weight 0;
  outline: none;
  box-shadow: none;
  background-color: transparent;
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
  outline: none;
}

.nautilus-window .nautilus-grid-view child.activatable:focus,
columnview.view>header>button:focus,
treeview.view>header>button:focus,
row.activatable:focus {
  color: #FFFFFF;
  background-color: transparent;
  box-shadow: none;
  outline: none;
}

.nautilus-window .nautilus-grid-view child.activatable:hover,
columnview.view>header>button:hover,
treeview.view>header>button:hover,
.nautilus-window .nautilus-grid-view child.has-open-popup.activatable,
columnview.view>header>button.has-open-popup,
treeview.view>header>button.has-open-popup,
row.activatable:hover,
row.activatable.has-open-popup {
  color: #FFFFFF;
  background-color: alpha(currentColor, 0.05);
  box-shadow: none;
}

.nautilus-window .nautilus-grid-view child.activatable:active,
columnview.view>header>button:active,
treeview.view>header>button:active,
row.activatable:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, font-weight 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.05) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.05);
  color: #FFFFFF;
  box-shadow: none;
}

.nautilus-window .nautilus-grid-view child.activatable:selected,
columnview.view>header>button:selected,
treeview.view>header>button:selected,
row.activatable:selected {
  background-color: alpha(currentColor, 0.06);
}

.nautilus-window .nautilus-grid-view child.activatable:selected:hover,
columnview.view>header>button:selected:hover,
treeview.view>header>button:selected:hover,
row.activatable:selected:hover {
  background-color: alpha(currentColor, 0.08);
}

button row.activatable:focus,
button row.activatable:hover,
button row.activatable:active {
  box-shadow: none;
  background: none;
}

button:checked row.activatable {
  color: rgba(0, 0, 0, 0.87);
}

row:selected {
  background-color: alpha(currentColor, 0.06);
  color: inherit;
  box-shadow: none;
}

row:selected:hover {
  background-color: alpha(currentColor, 0.08);
}

row:selected:focus,
row:selected:focus-visible:focus-within {
  outline: none;
  background-color: alpha(currentColor, 0.08);
}

row:selected:focus:hover,
row:selected:focus-visible:focus-within:hover {
  background-color: alpha(currentColor, 0.16);
}

row:selected image,
row:selected label {
  color: #FFFFFF;
}

row:selected button image,
row:selected button label {
  color: inherit;
}

row:selected:disabled image,
row:selected:disabled label {
  color: rgba(255, 255, 255, 0.5);
}

.rich-list {
  /* rich lists usually containing other widgets than just labels/text */
}

.rich-list>row {
  padding: 9px 12px;
  min-height: 32px;
  /* should be tall even when only containing a label */
}

.rich-list>row:last-child {
  border-bottom: none;
}

.rich-list>row>box {
  border-spacing: 12px;
}

row label.subtitle {
  font-size: smaller;
}

row>box.header {
  margin-left: 12px;
  margin-right: 12px;
  border-spacing: 6px;
  min-height: 50px;
}

row>box.header>.icon:disabled {
  filter: opacity(0.45);
}

row>box.header>box.title {
  margin-top: 6px;
  margin-bottom: 6px;
  border-spacing: 3px;
  padding: 0;
}

row>box.header>box.title,
row>box.header>box.title>.title,
row>box.header>box.title>.subtitle {
  padding: 0;
  font-weight: inherit;
}

row>box.header>.prefixes,
row>box.header>.suffixes {
  border-spacing: 6px;
}

row>box.header>.icon:dir(ltr),
row>box.header>.prefixes:dir(ltr) {
  margin-right: 6px;
}

row>box.header>.icon:dir(rtl),
row>box.header>.prefixes:dir(rtl) {
  margin-left: 6px;
}

row.entry:not(:selected).activatable.focused:hover,
row.entry:not(:selected).activatable.focused:active {
  background-color: transparent;
}

row.entry .edit-icon,
row.entry .indicator {
  min-width: 24px;
  min-height: 24px;
  padding: 5px;
}

row.entry .edit-icon:disabled {
  opacity: 0.5;
}

row.entry .indicator {
  opacity: 0.65;
}

row.entry.monospace {
  font-family: inherit;
}

row.entry.monospace text {
  font-family: monospace;
}

row.entry.error text>selection:focus-within {
  background-color: alpha(#F44336, 0.2);
}

row.entry.error text>cursor-handle>contents {
  background-color: currentColor;
}

row.entry.warning text>selection:focus-within {
  background-color: alpha(#FBC02D, 0.2);
}

row.entry.warning text>cursor-handle>contents {
  background-color: currentColor;
}

row.entry.success text>selection:focus-within {
  background-color: alpha(#66BB6A, 0.2);
}

row.entry.success text>cursor-handle>contents {
  background-color: currentColor;
}

row.combo image.dropdown-arrow:disabled {
  filter: opacity(0.45);
}

row.combo listview.inline {
  background: none;
  border: none;
  box-shadow: none;
  color: inherit;
}

row.combo listview.inline,
row.combo listview.inline:disabled {
  background: none;
  color: inherit;
}

row.combo popover>contents {
  min-width: 120px;
}

list.content>row,
list.content>row.expander row.header,
list.boxed-list>row,
list.boxed-list>row.expander row.header,
row.expander list.nested>row {
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

list.content>row:not(:selected).activatable:hover,
list.content>row.expander row.header:not(:selected).activatable:hover,
list.boxed-list>row:not(:selected).activatable:hover,
list.boxed-list>row.expander row.header:not(:selected).activatable:hover,
row.expander list.nested>row:not(:selected).activatable:hover {
  background-color: alpha(currentColor, 0.08);
}

list.content>row:not(:selected).activatable:active,
list.content>row.expander row.header:not(:selected).activatable:active,
list.boxed-list>row:not(:selected).activatable:active,
list.boxed-list>row.expander row.header:not(:selected).activatable:active,
row.expander list.nested>row:not(:selected).activatable:active {
  background-color: alpha(currentColor, 0.12);
}

list.content>row:not(:selected).activatable.has-open-popup,
list.content>row.expander row.header:not(:selected).activatable.has-open-popup,
list.boxed-list>row:not(:selected).activatable.has-open-popup,
list.boxed-list>row.expander row.header:not(:selected).activatable.has-open-popup,
row.expander list.nested>row:not(:selected).activatable.has-open-popup {
  background-color: alpha(currentColor, 0.03);
}

row.expander {
  background: none;
  padding: 0px;
}

row.expander>box>list {
  background: none;
  color: inherit;
}

row.expander list.nested {
  color: inherit;
}

row.expander image.expander-row-arrow {
  transition: -gtk-icon-transform 200ms cubic-bezier(0, 0, 0.2, 1);
}

row.expander image.expander-row-arrow:dir(ltr) {
  margin-left: 6px;
}

row.expander image.expander-row-arrow:dir(rtl) {
  margin-right: 6px;
}

row.expander image.expander-row-arrow:dir(ltr) {
  -gtk-icon-transform: rotate(0.5turn);
}

row.expander image.expander-row-arrow:dir(rtl) {
  -gtk-icon-transform: rotate(-0.5turn);
}

row.expander image.expander-row-arrow:disabled {
  filter: opacity(0.45);
}

row.expander:checked image.expander-row-arrow {
  -gtk-icon-transform: rotate(0turn);
  opacity: 1;
}

row.expander:checked image.expander-row-arrow:not(:disabled) {
  color: #8caaee;
}

.osd row.expander:checked image.expander-row-arrow:not(:disabled) {
  color: inherit;
}

list.content>row.expander,
list.boxed-list>row.expander {
  border: none;
}

list.content>row:first-child,
list.content>row:first-child.expander row.header,
list.boxed-list>row:first-child,
list.boxed-list>row:first-child.expander row.header {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

list.content>row:last-child,
list.content>row:last-child.expander:not(:checked),
list.content>row:last-child.expander:not(:checked) row.header,
list.content>row:last-child.expander:checked list.nested,
list.content>row:last-child.expander:checked list.nested>row:last-child,
list.boxed-list>row:last-child,
list.boxed-list>row:last-child.expander:not(:checked),
list.boxed-list>row:last-child.expander:not(:checked) row.header,
list.boxed-list>row:last-child.expander:checked list.nested,
list.boxed-list>row:last-child.expander:checked list.nested>row:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  border-bottom-width: 0;
}

columnview>listview>row {
  padding: 0;
}

columnview>listview>row>cell {
  padding: 8px 6px;
}

columnview>listview>row>cell:not(:first-child) {
  border-left: 1px solid transparent;
}

columnview.column-separators>listview>row>cell {
  border-left-color: rgba(255, 255, 255, 0.12);
}

columnview.data-table>listview>row>cell {
  padding-top: 2px;
  padding-bottom: 2px;
}

treeexpander {
  border-spacing: 6px;
}

columnview row:not(:selected) cell editablelabel:not(.editing):focus-within {
  outline: 2px solid alpha(currentColor, 0.06);
}

columnview row:not(:selected) cell editablelabel.editing:focus-within {
  outline: 2px solid #8caaee;
}

columnview row:not(:selected) cell editablelabel.editing text selection {
  color: rgba(0, 0, 0, 0.87);
  background-color: #8caaee;
}

/*********************
 * App Notifications *
 *********************/
.app-notification {
  margin: 6px;
  border-spacing: 0;
  padding: 0;
  border: none;
  background-image: none;
}

.app-notification button.text-button:not(:disabled) {
  color: #8caaee;
}

.app-notification>box>label {
  margin-left: 9px;
}

.app-notification.frame,
.app-notification border {
  border: none;
}

/*************
 * Expanders *
 *************/
expander {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  min-width: 16px;
  min-height: 16px;
  color: rgba(255, 255, 255, 0.7);
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
}

expander:dir(rtl) {
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic-rtl");
}

expander:hover,
expander:active {
  color: #FFFFFF;
}

expander:checked {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

expander:disabled {
  color: rgba(255, 255, 255, 0.32);
}

expander-widget>box>title {
  border-radius: 6px;
}

expander-widget>box>title:hover>expander {
  color: rgba(255, 255, 255, 0.7);
}

.navigation-sidebar:not(decoration):not(window):drop(active):focus,
.navigation-sidebar:not(decoration):not(window):drop(active),
placessidebar:not(decoration):not(window):drop(active):focus,
placessidebar:not(decoration):not(window):drop(active),
stackswitcher:not(decoration):not(window):drop(active):focus,
stackswitcher:not(decoration):not(window):drop(active),
expander-widget:not(decoration):not(window):drop(active):focus,
expander-widget:not(decoration):not(window):drop(active) {
  box-shadow: none;
}

/************
 * Calendar *
 ************/
calendar {
  padding: 0;
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 6px;
  color: #FFFFFF;
}

calendar:disabled {
  color: rgba(255, 255, 255, 0.5);
}

calendar:selected {
  border-radius: 6px;
}

calendar>header {
  padding: 3px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

calendar>header>button {
  min-height: 24px;
}

calendar>grid {
  margin: 3px;
}

calendar>grid>label {
  border-radius: 6px;
  margin: 0;
}

calendar>grid>label.today:selected {
  box-shadow: none;
}

calendar>grid>label:focus {
  outline-style: none;
}

calendar>grid>label.day-number {
  padding: 6px;
}

calendar>grid>label.day-number.other-month {
  color: alpha(currentColor, 0.3);
}

/***********
 * Dialogs *
 ***********/
window.messagedialog .response-area>box>button,
window.dialog.message .dialog-action-area>button {
  border-radius: 0;
  min-height: 28px;
  padding: 6px 12px;
  margin: 0;
  border: none;
}

window.messagedialog .response-area>box>button:first-child,
window.dialog.message .dialog-action-area>button:first-child {
  border-radius: 0 0 0 12px;
}

window.messagedialog .response-area>box>button:last-child,
window.dialog.message .dialog-action-area>button:last-child {
  border-radius: 0 0 12px 0;
}

window.messagedialog .response-area>box>button:only-child,
window.dialog.message .dialog-action-area>button:only-child {
  border-radius: 0 0 12px 12px;
}

window.dialog.message.background {
  background-color: #414559;
}

window.dialog.message box.dialog-vbox.vertical {
  margin-top: 6px;
  border-spacing: 24px;
}

window.dialog.message box.dialog-vbox.vertical>box.vertical {
  margin-bottom: 6px;
}

window.dialog.message box.dialog-vbox.vertical>box>box>box>label.title {
  font-weight: 800;
  font-size: 15pt;
}

window.dialog.message .titlebar {
  min-height: 24px;
  border-style: none;
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.1);
  background-color: #414559;
  color: #FFFFFF;
}

window.dialog.message .titlebar:backdrop {
  background-color: #414559;
  color: rgba(255, 255, 255, 0.7);
}

window.dialog.message .dialog-action-area {
  border-top: 1px solid rgba(255, 255, 255, 0.12);
  margin: 0;
  border-spacing: 0;
}

window.dialog.message .dialog-action-area>button {
  border: none;
}

window.dialog.message .dialog-action-area>button:not(:last-child) {
  border-right: 1px solid rgba(255, 255, 255, 0.12);
}

window.dialog.message .dialog-action-area>button.suggested-action:not(:disabled) {
  color: #8caaee;
}

window.dialog.message .dialog-action-area>button.destructive-action:not(:disabled) {
  color: #F44336;
}

window.aboutdialog.background.csd scrolledwindow.frame,
window.aboutdialog.background.csd scrolledwindow.frame>viewport.view,
window.aboutdialog.background.csd scrolledwindow.frame>textview.view,
window.aboutdialog.background.csd scrolledwindow.frame>textview.view>text {
  border-radius: 6px;
}

/********************
 * AdwMessageDialog *
 ********************/
window.messagedialog {
  background-color: #414559;
  color: #FFFFFF;
}

window.messagedialog .message-area {
  padding: 24px 30px;
  border-spacing: 10px;
}

window.messagedialog .response-area>box>button.suggested {
  color: #8caaee;
}

window.messagedialog .response-area>box>button.destructive {
  color: #F44336;
}

window.messagedialog.csd:not(.solid-csd) {
  border-radius: 12px;
}

window.messagedialog.csd:not(.solid-csd) .response-area>box.horizontal>button {
  margin: 0;
}

window.messagedialog.csd:not(.solid-csd) .response-area>box.horizontal>button:first-child {
  margin-left: 0;
}

window.messagedialog.csd:not(.solid-csd) .response-area>box.horizontal>button:last-child {
  margin-right: 0;
}

window.messagedialog.csd:not(.solid-csd) .response-area>box.vertical>button {
  margin-top: 0;
  margin-bottom: 0;
}

window.messagedialog.csd:not(.solid-csd) .response-area>box.vertical>button:last-child {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  margin-bottom: 0;
}

filechooser .dialog-action-box {
  border-top: 1px solid rgba(255, 255, 255, 0.12);
}

filechooser #pathbarbox {
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  background-color: #303446;
}

filechooser stack.view frame>border {
  border: none;
}

filechooserbutton>button>box {
  border-spacing: 6px;
}

filechooserbutton:drop(active) {
  box-shadow: none;
  border-color: transparent;
}

/***********
 * Sidebar *
 ***********/
.sidebar {
  border-style: none;
  background-color: #232634;
}

.sidebar:not(separator):dir(ltr),
.sidebar:not(separator).left,
.sidebar:not(separator).left:dir(rtl) {
  border-right: 1px solid rgba(255, 255, 255, 0.12);
  border-left-style: none;
}

.sidebar:not(separator):dir(rtl),
.sidebar:not(separator).right {
  border-left: 1px solid rgba(255, 255, 255, 0.12);
  border-right-style: none;
}

.sidebar listview.view,
.sidebar list {
  background-color: transparent;
  color: inherit;
}

paned .sidebar.left,
paned .sidebar.right,
paned .sidebar.left:dir(rtl),
paned .sidebar:dir(rtl),
paned .sidebar:dir(ltr),
paned .sidebar {
  border-style: none;
}

leaflet.unfolded>box>stacksidebar.sidebar {
  border: none;
}

stacksidebar list {
  padding: 6px;
  background-color: #232634;
}

stacksidebar row {
  min-height: 24px;
  padding: 6px;
  border-radius: 6px;
}

stacksidebar row:selected {
  font-weight: 500;
}

stacksidebar row+row {
  margin-top: 4px;
}

stacksidebar row>label {
  padding-left: 6px;
  padding-right: 6px;
  color: inherit;
}

separator.sidebar {
  background-color: rgba(255, 255, 255, 0.12);
  border-right: none;
}

separator.sidebar.selection-mode,
.selection-mode separator.sidebar {
  background-color: rgba(255, 255, 255, 0.12);
}

/**********************
 * Navigation Sidebar *
 **********************/
.navigation-sidebar {
  padding: 4.5px 0;
  border-right: none;
}

.navigation-sidebar,
.navigation-sidebar.view {
  background-color: transparent;
  color: inherit;
}

.navigation-sidebar.background {
  background-color: #232634;
  color: rgba(255, 255, 255, 0.7);
}

.navigation-sidebar>separator {
  margin: 4.5px 0;
}

.navigation-sidebar>row {
  min-height: 24px;
  padding: 6px;
  border-radius: 6px;
  margin: 1.5px 6px;
}

/****************
 * File chooser *
 ****************/
row image.sidebar-icon {
  transition: color 75ms cubic-bezier(0, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.7);
}

row image.sidebar-icon:disabled {
  color: rgba(255, 255, 255, 0.32);
}

placessidebar>viewport.frame {
  border-style: none;
}

placessidebar list>separator {
  margin: 3px 0;
}

placessidebar row:selected {
  font-weight: 500;
}

placessidebar row image.sidebar-icon:dir(ltr) {
  padding-right: 8px;
}

placessidebar row image.sidebar-icon:dir(rtl) {
  padding-left: 8px;
}

placessidebar row label.sidebar-label {
  color: inherit;
}

placessidebar row label.sidebar-label:dir(ltr) {
  padding-right: 2px;
}

placessidebar row label.sidebar-label:dir(rtl) {
  padding-left: 2px;
}

placessidebar row.sidebar-placeholder-row {
  background-color: alpha(currentColor, 0.08);
}

placessidebar row.sidebar-new-bookmark-row {
  color: #8caaee;
}

placessidebar row.sidebar-new-bookmark-row image.sidebar-icon {
  color: #8caaee;
}

placessidebar row:drop(active) {
  background-color: alpha(currentColor, 0.08);
}

placesview .server-list-button>image {
  transition: 200ms cubic-bezier(0, 0, 0.2, 1);
  -gtk-icon-transform: rotate(0turn);
}

placesview .server-list-button:checked>image {
  transition: 200ms cubic-bezier(0, 0, 0.2, 1);
  -gtk-icon-transform: rotate(-0.5turn);
}

placesview>actionbar>revealer>box>label {
  border-spacing: 6px;
}

/*********
 * Paned *
 *********/
paned>separator {
  min-width: 1px;
  min-height: 1px;
  -gtk-icon-source: none;
  border-style: none;
  background-color: transparent;
  background-image: image(#494c5c);
  background-size: 1px 1px;
  background-clip: content-box;
  box-shadow: none;
}

paned>separator.wide {
  min-width: 6px;
  min-height: 6px;
  background-color: #303446;
  background-image: image(#494c5c), image(#494c5c);
  background-size: 1px 1px, 1px 1px;
}

paned.horizontal>separator {
  background-repeat: repeat-y;
}

paned.horizontal>separator:dir(ltr) {
  margin: 0 -8px 0 0;
  padding: 0 8px 0 0;
  background-position: left;
}

paned.horizontal>separator:dir(rtl) {
  margin: 0 0 0 -8px;
  padding: 0 0 0 8px;
  background-position: right;
}

paned.horizontal>separator.wide {
  margin: 0;
  padding: 0;
  background-repeat: repeat-y, repeat-y;
  background-position: left, right;
}

paned.vertical>separator {
  margin: 0 0 -8px 0;
  padding: 0 0 8px 0;
  background-repeat: repeat-x;
  background-position: top;
}

paned.vertical>separator.wide {
  margin: 0;
  padding: 0;
  background-repeat: repeat-x, repeat-x;
  background-position: bottom, top;
}

/************
 * GtkVideo *
 ************/
video {
  background: black;
  border-radius: 6px;
}

video image.osd {
  min-width: 64px;
  min-height: 64px;
  border-radius: 9999px;
  border: none;
}

/**************
 * GtkInfoBar *
 **************/
infobar>revealer>box {
  padding: 6px;
  border-spacing: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: none;
}

infobar.info>revealer>box,
infobar.info:hover>revealer>box,
infobar.info:backdrop>revealer>box {
  background-color: #303446;
  color: #FFFFFF;
}

infobar.info>revealer>box button.text-button:not(:disabled):not(.suggested-action):not(.destructive-action),
infobar.info:hover>revealer>box button.text-button:not(:disabled):not(.suggested-action):not(.destructive-action),
infobar.info:backdrop>revealer>box button.text-button:not(:disabled):not(.suggested-action):not(.destructive-action) {
  color: #8caaee;
}

infobar.action>revealer>box,
infobar.action:backdrop>revealer>box,
infobar.question>revealer>box,
infobar.question:backdrop>revealer>box {
  background-color: #8caaee;
  color: rgba(0, 0, 0, 0.87);
}

infobar.action>revealer>box button,
infobar.action>revealer>box button:hover,
infobar.action>revealer>box button:focus,
infobar.action>revealer>box button:active,
infobar.action>revealer>box button:checked,
infobar.action>revealer>box button.text-button:not(:disabled),
infobar.action:backdrop>revealer>box button,
infobar.action:backdrop>revealer>box button:hover,
infobar.action:backdrop>revealer>box button:focus,
infobar.action:backdrop>revealer>box button:active,
infobar.action:backdrop>revealer>box button:checked,
infobar.action:backdrop>revealer>box button.text-button:not(:disabled),
infobar.question>revealer>box button,
infobar.question>revealer>box button:hover,
infobar.question>revealer>box button:focus,
infobar.question>revealer>box button:active,
infobar.question>revealer>box button:checked,
infobar.question>revealer>box button.text-button:not(:disabled),
infobar.question:backdrop>revealer>box button,
infobar.question:backdrop>revealer>box button:hover,
infobar.question:backdrop>revealer>box button:focus,
infobar.question:backdrop>revealer>box button:active,
infobar.question:backdrop>revealer>box button:checked,
infobar.question:backdrop>revealer>box button.text-button:not(:disabled) {
  color: rgba(0, 0, 0, 0.87);
}

infobar.action>revealer>box *:link,
infobar.action:backdrop>revealer>box *:link,
infobar.question>revealer>box *:link,
infobar.question:backdrop>revealer>box *:link {
  color: rgba(0, 0, 0, 0.87);
}

infobar.action:hover>revealer>box,
infobar.question:hover>revealer>box {
  background-color: #769aeb;
}

infobar.warning>revealer>box,
infobar.warning:backdrop>revealer>box {
  background-color: #FBC02D;
  color: rgba(0, 0, 0, 0.87);
}

infobar.warning>revealer>box button,
infobar.warning>revealer>box button:hover,
infobar.warning>revealer>box button:focus,
infobar.warning>revealer>box button:active,
infobar.warning>revealer>box button:checked,
infobar.warning>revealer>box button.text-button:not(:disabled),
infobar.warning:backdrop>revealer>box button,
infobar.warning:backdrop>revealer>box button:hover,
infobar.warning:backdrop>revealer>box button:focus,
infobar.warning:backdrop>revealer>box button:active,
infobar.warning:backdrop>revealer>box button:checked,
infobar.warning:backdrop>revealer>box button.text-button:not(:disabled) {
  color: rgba(0, 0, 0, 0.87);
}

infobar.warning>revealer>box *:link,
infobar.warning:backdrop>revealer>box *:link {
  color: rgba(0, 0, 0, 0.87);
}

infobar.warning:hover>revealer>box {
  background-color: #fbb814;
}

infobar.error>revealer>box,
infobar.error:backdrop>revealer>box {
  background-color: #F44336;
  color: #FFFFFF;
}

infobar.error>revealer>box button,
infobar.error>revealer>box button:hover,
infobar.error>revealer>box button:focus,
infobar.error>revealer>box button:active,
infobar.error>revealer>box button:checked,
infobar.error>revealer>box button.text-button:not(:disabled),
infobar.error:backdrop>revealer>box button,
infobar.error:backdrop>revealer>box button:hover,
infobar.error:backdrop>revealer>box button:focus,
infobar.error:backdrop>revealer>box button:active,
infobar.error:backdrop>revealer>box button:checked,
infobar.error:backdrop>revealer>box button.text-button:not(:disabled) {
  color: #FFFFFF;
}

infobar.error>revealer>box *:link,
infobar.error:backdrop>revealer>box *:link {
  color: #FFFFFF;
}

infobar.error:hover>revealer>box {
  background-color: #f32c1e;
}

/************
 * Tooltips *
 ************/
tooltip {
  padding: 6px 12px;
  box-shadow: none;
  border: none;
}

tooltip.background {
  background-color: rgba(29, 31, 43, 0.9);
  color: #FFFFFF;
  box-shadow: 0 3px 2px -2px rgba(0, 0, 0, 0.05), 0 2px 3px -1px rgba(0, 0, 0, 0.06), 0 1px 4px 0 rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  margin: 2px 6px 8px 6px;
}

tooltip>box {
  border-spacing: 6px;
}

/*****************
 * Color Chooser *
 *****************/
colorswatch.top {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

colorswatch.top overlay {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

colorswatch.bottom {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

colorswatch.bottom overlay {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

colorswatch.left,
colorswatch:first-child:not(.top) {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

colorswatch.left overlay,
colorswatch:first-child:not(.top) overlay {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

colorswatch.right,
colorswatch:last-child:not(.bottom) {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

colorswatch.right overlay,
colorswatch:last-child:not(.bottom) overlay {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

colorswatch.dark {
  color: #FFFFFF;
}

colorswatch.light {
  color: rgba(0, 0, 0, 0.87);
}

colorchooser colorswatch:hover {
  transition: box-shadow 75ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: 0 0 0 2px #8caaee;
}

colorswatch#add-color-button {
  border-radius: 6px 0 0 6px;
  color: #FFFFFF;
}

colorswatch#add-color-button:only-child {
  border-radius: 6px;
}

colorswatch#add-color-button overlay {
  background-color: rgba(255, 255, 255, 0.04);
}

colorswatch#add-color-button overlay:hover {
  background-color: rgba(255, 255, 255, 0.12);
  box-shadow: none;
}

colorswatch#add-color-button overlay:active {
  background-color: rgba(255, 255, 255, 0.3);
}

colorswatch:disabled {
  opacity: 0.5;
}

colorswatch:disabled overlay {
  box-shadow: none;
}

colorswatch#editor-color-sample {
  border-radius: 6px;
}

colorswatch#editor-color-sample overlay {
  border-radius: 6px;
}

colorswatch#editor-color-sample overlay:hover {
  box-shadow: 0 2px 2px -2px rgba(0, 0, 0, 0.3), 0 1px 2px -1px rgba(0, 0, 0, 0.24), 0 1px 2px -0.6px rgba(0, 0, 0, 0.17);
}

colorchooser .popover.osd {
  transition: box-shadow 75ms cubic-bezier(0, 0, 0.2, 1);
  border-radius: 6px;
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.15), 0 4px 3px 0 rgba(0, 0, 0, 0.18), 0 1px 6px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.1);
  background-color: #414559;
}

colorchooser .popover.osd:backdrop {
  box-shadow: 0 3px 2px -3px rgba(0, 0, 0, 0.2), 0 2px 2px -1px rgba(0, 0, 0, 0.24), 0 1px 3px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.1);
}

/********
 * Misc *
 ********/
.content-view {
  background-color: #303446;
}

/**********************
 * Window Decorations *
 **********************/
window {
  border: none;
}

window.csd {
  border-radius: 12px;
  outline-offset: -1px;
  outline: 1px solid rgba(255, 255, 255, 0.1);
  margin: 0;
  box-shadow: 0 8px 6px -5px rgba(0, 0, 0, 0.2), 0 16px 15px 2px rgba(0, 0, 0, 0.14), 0 6px 18px 5px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.75), 0 0 36px transparent;
}

window.csd:backdrop {
  transition: box-shadow 75ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.15), 0 4px 3px 0 rgba(0, 0, 0, 0.18), 0 1px 6px 0 rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.75), 0 0 36px transparent;
}

window.csd.maximized,
window.csd.fullscreen,
window.csd.tiled,
window.csd.tiled-top,
window.csd.tiled-right,
window.csd.tiled-bottom,
window.csd.tiled-left {
  border-radius: 0;
  transition: none;
}

window.solid-csd {
  margin: 0;
  padding: 2px;
  border-radius: 0;
  background-color: #232634;
  border: 1px solid #494c5c;
}

window.solid-csd:backdrop {
  background-color: #303446;
}

window.ssd {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.12);
}

windowcontrols button:not(.suggested-action):not(.destructive-action) {
  min-height: 16px;
  min-width: 16px;
  padding: 0;
  margin: 0 4px;
}

windowcontrols button.minimize:not(.suggested-action):not(.destructive-action),
windowcontrols button.maximize:not(.suggested-action):not(.destructive-action),
windowcontrols button.close:not(.suggested-action):not(.destructive-action) {
  color: transparent;
}

windowcontrols button.minimize:not(.suggested-action):not(.destructive-action) image,
windowcontrols button.maximize:not(.suggested-action):not(.destructive-action) image,
windowcontrols button.close:not(.suggested-action):not(.destructive-action) image {
  padding: 0;
}

windowcontrols button.minimize:hover:not(.suggested-action):not(.destructive-action),
windowcontrols button.maximize:hover:not(.suggested-action):not(.destructive-action),
windowcontrols button.close:hover:not(.suggested-action):not(.destructive-action) {
  box-shadow: none;
}

windowcontrols button.minimize:active:not(.suggested-action):not(.destructive-action),
windowcontrols button.maximize:active:not(.suggested-action):not(.destructive-action),
windowcontrols button.close:active:not(.suggested-action):not(.destructive-action) {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.25);
}

windowcontrols button.minimize:hover:not(.suggested-action):not(.destructive-action),
windowcontrols button.minimize:active:not(.suggested-action):not(.destructive-action),
windowcontrols button.maximize:hover:not(.suggested-action):not(.destructive-action),
windowcontrols button.maximize:active:not(.suggested-action):not(.destructive-action),
windowcontrols button.close:hover:not(.suggested-action):not(.destructive-action),
windowcontrols button.close:active:not(.suggested-action):not(.destructive-action) {
  color: rgba(0, 0, 0, 0.5);
}

windowcontrols button.minimize:backdrop:not(.suggested-action):not(.destructive-action),
windowcontrols button.maximize:backdrop:not(.suggested-action):not(.destructive-action),
windowcontrols button.close:backdrop:not(.suggested-action):not(.destructive-action) {
  background-color: rgba(255, 255, 255, 0.3);
}

windowcontrols button.minimize:backdrop:hover:not(.suggested-action):not(.destructive-action),
windowcontrols button.minimize:backdrop:active:not(.suggested-action):not(.destructive-action),
windowcontrols button.maximize:backdrop:hover:not(.suggested-action):not(.destructive-action),
windowcontrols button.maximize:backdrop:active:not(.suggested-action):not(.destructive-action),
windowcontrols button.close:backdrop:hover:not(.suggested-action):not(.destructive-action),
windowcontrols button.close:backdrop:active:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.5);
}

windowcontrols button.minimize:not(.suggested-action):not(.destructive-action) {
  background-color: #e5c890;
}

windowcontrols button.minimize:active:not(.suggested-action):not(.destructive-action) {
  background-color: #ecd6ac;
}

windowcontrols button.maximize:not(.suggested-action):not(.destructive-action) {
  background-color: #a6d189;
}

windowcontrols button.maximize:active:not(.suggested-action):not(.destructive-action) {
  background-color: #bcdda7;
}

windowcontrols button.close:not(.suggested-action):not(.destructive-action) {
  background-color: #e78284;
}

windowcontrols button.close:active:not(.suggested-action):not(.destructive-action) {
  background-color: #eda1a3;
}

windowcontrols {
  border-spacing: 6px;
}

windowcontrols:not(.empty).start:dir(ltr),
windowcontrols:not(.empty).end:dir(rtl) {
  margin-right: 9px;
  margin-left: 9px;
}

windowcontrols:not(.empty).start:dir(rtl),
windowcontrols:not(.empty).end:dir(ltr) {
  margin-left: 9px;
  margin-right: 9px;
}

.view:selected,
iconview:selected,
gridview>child:selected,
columnview.view:selected,
treeview.view:selected,
calendar:selected,
calendar>grid>label.day-number:selected {
  background-color: alpha(currentColor, 0.06);
}

flowbox>flowboxchild:selected,
calendar>grid>label.today {
  color: #8caaee;
  background-color: rgba(140, 170, 238, 0.2);
}

textview text selection:focus,
textview text selection,
label>selection,
entry>text>selection,
spinbutton>text>selection,
entry headerbar popover.background entry>text>selection,
headerbar popover.background entry entry>text>selection,
calendar>grid>label.today:selected {
  color: rgba(0, 0, 0, 0.87);
  background-color: #8caaee;
}

/**********************
 * Touch Copy & Paste *
 **********************/
cursor-handle {
  color: #8caaee;
  -gtk-icon-source: -gtk-recolor(url("assets/scalable/cursor-handle-symbolic.svg"));
}

cursor-handle.insertion-cursor:dir(ltr),
cursor-handle.insertion-cursor:dir(rtl) {
  padding-top: 6px;
}

shortcuts-section {
  margin: 20px;
}

.shortcuts-search-results {
  margin: 20px;
  border-spacing: 24px;
}

shortcut {
  border-spacing: 6px;
}

shortcut>.keycap {
  min-width: 12px;
  min-height: 26px;
  margin-top: 2px;
  padding-bottom: 2px;
  padding-left: 8px;
  padding-right: 8px;
  border: solid 1px rgba(255, 255, 255, 0.12);
  border-radius: 7px;
  box-shadow: inset 0 -2px rgba(255, 255, 255, 0.12);
  background-color: #414559;
  color: #FFFFFF;
  font-size: smaller;
}

:not(decoration):not(window):drop(active) {
  caret-color: #8caaee;
}

stackswitcher {
  min-height: 0;
  padding: 3px;
  margin: 6px 0;
  border-radius: 9px;
  background-color: rgba(255, 255, 255, 0.04);
  border: none;
}

stackswitcher.linked:not(.vertical)>button:not(.suggested-action):not(.destructive-action) {
  margin: 0 0;
  background-color: transparent;
  border-radius: 6px;
  padding: 3px 10px;
}

stackswitcher.linked:not(.vertical)>button:not(.suggested-action):not(.destructive-action).text-button {
  min-width: 100px;
}

stackswitcher.linked:not(.vertical)>button:not(.suggested-action):not(.destructive-action):focus:not(:hover):not(:checked) {
  box-shadow: none;
}

stackswitcher.linked:not(.vertical)>button:not(.suggested-action):not(.destructive-action):hover {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}

stackswitcher.linked:not(.vertical)>button:not(.suggested-action):not(.destructive-action):active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, border 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}

stackswitcher.linked:not(.vertical)>button:not(.suggested-action):not(.destructive-action):checked {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, background-color 0ms;
  background-color: rgba(255, 255, 255, 0.15);
  color: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

stackswitcher button.text-button {
  min-width: 100px;
}

stackswitcher button.circular,
stackswitcher button.text-button.circular {
  min-width: 36px;
  min-height: 36px;
  padding: 0;
}

/*************
 * App Icons *
 *************/
.lowres-icon {
  -gtk-icon-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.icon-dropshadow {
  -gtk-icon-shadow: 0 1px 12px rgba(0, 0, 0, 0.05), 0 1px 6px rgba(0, 0, 0, 0.1);
}

/*********
 * Emoji *
 *********/
popover.emoji-picker {
  padding: 0;
}

popover.emoji-picker>contents {
  padding: 0;
}

.emoji-searchbar {
  padding: 6px;
  border-spacing: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  background: none;
}

.emoji-searchbar entry text {
  background: none;
  box-shadow: none;
}

.emoji-toolbar {
  padding: 0;
  border-spacing: 3px;
  border-top: 1px solid rgba(255, 255, 255, 0.12);
  background: none;
}

button.emoji-section {
  margin: 0;
  padding: 6px;
  border-radius: 6px;
}

button.emoji-section:checked {
  color: #8caaee;
}

popover.emoji-picker emoji {
  font-size: x-large;
  padding: 6px;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  border-radius: 6px;
}

popover.emoji-picker emoji:focus,
popover.emoji-picker emoji:hover {
  background: alpha(currentColor, 0.08);
}

emoji-completion-row {
  min-height: 28px;
  padding: 0 12px;
}

emoji-completion-row>box {
  border-spacing: 6px;
  padding: 2px 6px;
}

emoji-completion-row:focus,
emoji-completion-row:hover,
emoji-completion-row emoji:hover,
emoji-completion-row emoji:focus {
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
}

popover.entry-completion>contents {
  padding: 0;
}

.nautilus-window placesview label {
  color: rgba(255, 255, 255, 0.7);
}

.nautilus-window .floating-bar {
  min-height: 32px;
  padding: 0;
  margin: 6px;
  border-style: none;
  border-radius: 6px;
  background-color: #8caaee;
  color: rgba(0, 0, 0, 0.87);
  box-shadow: 0 3px 2px -2px rgba(0, 0, 0, 0.05), 0 2px 3px -1px rgba(0, 0, 0, 0.06), 0 1px 4px 0 rgba(0, 0, 0, 0.05);
}

.nautilus-window .floating-bar button {
  margin: 4px;
  color: rgba(0, 0, 0, 0.87);
}

#NautilusQueryEditor>menubutton>button.image-button {
  min-width: 24px;
  min-height: 24px;
}

#NautilusQueryEditor>text,
#NautilusQueryEditor>box,
#NautilusQueryEditor>menubutton>button.image-button {
  margin: 6px 0;
}

#NautilusQueryEditorTag {
  background-color: rgba(255, 255, 255, 0.12);
}

#NautilusQueryEditorTag>button.image-button {
  margin: 0;
  padding: 0;
}

#NautilusPathBar {
  background-color: rgba(255, 255, 255, 0.04);
  border-radius: 6px;
  margin: 6px 0;
}

#NautilusPathButton {
  margin: 0 3px;
  border-radius: 6px;
}

#NautilusPathButton.current-dir {
  color: #FFFFFF;
}

#NautilusPathButton.current-dir:hover,
#NautilusPathButton.current-dir:active {
  background: none;
  box-shadow: none;
}

#NautilusPathButton:first-child {
  margin-left: 0;
}

#NautilusViewCell clamp box {
  margin: 0;
  border-spacing: 0;
}

window.dialog>box>stack>box>box>notebook.frame {
  border-width: 0 0 0 1px;
  border-radius: 0;
}

.display-container.card {
  border-radius: 0;
  box-shadow: none;
  border-width: 0 0 1px 0;
}

.display-container .history-view {
  background-color: #303446;
}

.display-container #displayitem {
  padding: 0 12px 8px 0;
  font-size: 1.4em;
  border-top: 1px solid rgba(255, 255, 255, 0.12);
}

.math-buttons button {
  font-size: 1.1em;
  padding: 2px 6px;
}

.math-buttons button.text-button {
  padding-left: 16px;
  padding-right: 16px;
}

leaflet button.number-button {
  background-color: rgba(255, 255, 255, 0.1);
}

leaflet button.number-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

leaflet button.number-button:active {
  background-color: rgba(255, 255, 255, 0.3);
}

label.primary-label,
label.month-name,
label.secondary-label {
  font-size: 16pt;
  font-weight: bold;
  padding: 12px;
}

label.primary-label,
label.month-name {
  color: #8caaee;
}

label.secondary-label {
  color: rgba(255, 255, 255, 0.5);
}

calendar-view {
  font-size: 10pt;
}

calendar-view:selected {
  color: #8caaee;
  font-weight: bold;
}

calendar-view.header,
label.header {
  font-size: 10pt;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.5);
}

calendar-view.current,
weekgrid.current {
  background-color: alpha(#8caaee, 0.3);
}

popover.events {
  background-color: #303446;
  padding: 0;
}

popover.events box {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

popover.events list {
  background-color: #303446;
  border-radius: 6px;
}

popover.events scrolledwindow {
  border-width: 0;
}

popover.events button {
  border-radius: 6px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-style: solid none none;
  box-shadow: none;
}

event {
  margin: 1px;
  font-size: 0.9rem;
}

event widget.content {
  margin: 4px;
}

event.timed,
event:not(.slanted):not(.slanted-start):not(.slanted-end) {
  border-radius: 6px;
}

event.timed widget.edge {
  border-radius: 3px;
  min-width: 5px;
}

event.slanted-start,
event.slanted-end:dir(rtl) {
  padding-left: 16px;
  border-radius: 0 3px 3px 0;
}

event.slanted-end,
event.slanted-start:dir(rtl) {
  padding-right: 16px;
  border-radius: 3px 0 0 3px;
}

event:not(.timed).color-dark {
  color: white;
  outline-color: rgba(0, 0, 0, 0.3);
}

event.timed,
event:not(.timed).color-light {
  color: alpha(black, 0.75);
  outline-color: rgba(255, 255, 255, 0.5);
}

popover.event-popover,
popover.event-popover>contents {
  padding: 0;
}

.search-viewport {
  background-color: #303446;
}

.calendar-list {
  background-color: transparent;
}

.calendar-list>list {
  border-radius: 4px;
}

.sources-button {
  margin-top: 0;
  margin-bottom: 0;
  border-radius: 0;
  border-top-style: none;
  border-bottom-style: none;
}

.sources-button:hover:not(:backdrop) {
  background-image: none;
  text-shadow: none;
}

.calendar-color-image {
  -gtk-icon-filter: none;
}

image.calendar-color-image,
button:active:not(:backdrop) .calendar-color-image,
button:checked:not(:backdrop) .calendar-color-image,
.calendars-list .calendar-color-image:not(:backdrop):not(:disabled),
.calendar-list .calendar-color-image:not(:backdrop):not(:disabled),
.sources-button:not(:backdrop):not(:disabled) .calendar-color-image {
  -gtk-icon-shadow: 0 1px alpha(black, 0.1);
}

datechooser navigator {
  margin-right: 6px;
  margin-left: 6px;
  margin-bottom: 6px;
}

datechooser navigator label {
  font-weight: bold;
}

datechooser navigator button,
datechooser navigator button.image-button {
  min-height: 36px;
  min-width: 36px;
  padding: 0;
}

datechooser .weeknum,
datechooser .weekday {
  color: rgba(255, 255, 255, 0.5);
  font-size: smaller;
}

datechooser button.day {
  font-size: 10pt;
  font-weight: normal;
  margin: 3px;
  padding: 0;
  min-height: 36px;
  min-width: 36px;
  transition: none;
}

datechooser button.day dot {
  background-color: #FFFFFF;
  border-radius: 50%;
  min-height: 3px;
  min-width: 3px;
}

datechooser button.day:selected,
datechooser button.day.today:selected {
  background-color: #8caaee;
  color: rgba(0, 0, 0, 0.87);
  font-weight: bold;
}

datechooser button.day:selected dot,
datechooser button.day.today:selected dot {
  background-color: rgba(0, 0, 0, 0.87);
}

datechooser button.day.today {
  color: #8caaee;
}

datechooser button.day.today dot {
  background-color: #8caaee;
}

datechooser button.day.other-month:not(:hover),
datechooser button.day.other-month:backdrop {
  color: alpha(currentColor, 0.1);
}

datechooser button.day.other-month:not(:hover) dot,
datechooser button.day.other-month:backdrop dot {
  background-color: alpha(currentColor, 0.1);
}

datechooser button.day.other-month:hover:not(:backdrop) {
  color: rgba(255, 255, 255, 0.5);
}

datechooser button.day.other-month:hover:not(:backdrop) dot {
  background-color: rgba(255, 255, 255, 0.5);
}

.week-header {
  padding: 0;
}

.week-header>box:first-child {
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

.week-header .week-number {
  font-size: 16pt;
  font-weight: bold;
  padding: 12px 12px 18px 12px;
  color: rgba(255, 255, 255, 0.3);
}

.week-header.week-temperature {
  font-size: 10pt;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.5);
}

.week-header.lines {
  color: rgba(255, 255, 255, 0.12);
}

weekhourbar>label {
  font-size: 10pt;
  padding: 4px 6px;
}

.week-view .lines {
  color: rgba(255, 255, 255, 0.12);
}

weekgrid>widget.now-strip {
  background-color: alpha(#8caaee, 0.8);
  margin: 0 0 0 1px;
  min-height: 3px;
}

weekgrid:selected,
weekgrid.dnd,
.week-header:selected,
.week-header.dnd {
  background-color: alpha(#8caaee, 0.25);
}

monthcell {
  border: solid 1px rgba(255, 255, 255, 0.12);
  border-width: 1px 0 0 1px;
  background-color: transparent;
  transition: background-color 200ms;
}

monthcell:hover:not(.out-of-month):not(.today) {
  background-color: #232634;
  transition: background-color 200ms;
  color: #FFFFFF;
}

monthcell:selected {
  background-color: alpha(#8caaee, 0.1);
}

monthcell:selected:hover {
  background-color: alpha(#8caaee, 0.2);
}

monthcell:selected label.day-label {
  font-weight: bold;
}

monthcell:nth-child(7n + 1) {
  border-left-width: 0;
}

monthcell.today {
  background-color: alpha(#8caaee, 0.2);
}

monthcell.today:hover {
  background-color: alpha(#8caaee, 0.3);
  color: #8caaee;
}

monthcell.today:selected {
  background-color: alpha(#8caaee, 0.25);
}

monthcell.today:selected:hover {
  background-color: alpha(#8caaee, 0.35);
}

monthcell label {
  color: #FFFFFF;
  font-size: 0.9rem;
}

monthcell label.day-label {
  font-size: 1rem;
}

monthcell.out-of-month {
  background-color: rgba(255, 255, 255, 0.04);
}

monthcell.out-of-month label {
  color: rgba(255, 255, 255, 0.7);
}

monthcell button {
  padding: 0 6px;
  border-radius: 0;
  border-bottom: none;
  border-right: none;
}

monthpopover>box {
  margin: 0;
  padding: 0;
  background-color: transparent;
}

.notes-section box>textview {
  border-radius: 6px;
  padding: 6px;
}

.notes-section box>textview>text {
  background: none;
}

agenda-view list>row {
  padding: 2px 12px;
}

agenda-view list>label {
  padding: 6px 12px;
}

label.no-events {
  font-style: italic;
}

searchbutton>popover>arrow {
  background: none;
  border: none;
}

datechooser {
  padding: 6px;
}

datechooser .current-week {
  background: alpha(#232634, 0.7);
  color: #FFFFFF;
  border-radius: 6px;
}

menubutton stack>box {
  border-spacing: 6px;
}

.contacts-contact-list list.navigation-sidebar {
  background: none;
}

.details-page {
  margin: 24px 0px;
}

.installed-overlay-box {
  font-size: smaller;
  background-color: #8caaee;
  border-radius: 0;
  color: rgba(0, 0, 0, 0.87);
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
}

screenshot-carousel box.frame {
  border-width: 1px 0;
  border-radius: 0;
}

screenshot-carousel button,
.featured-carousel button {
  margin: 12px;
}

.screenshot-image-main .image1,
.screenshot-image-main .image2 {
  margin-top: 6px;
  margin-bottom: 12px;
  margin-left: 6px;
  margin-right: 6px;
}

.app-tile-label {
  font-size: 105%;
}

.review-textbox {
  padding: 6px;
}

.origin-rounded-box {
  background-color: rgba(255, 255, 255, 0.12);
  border-radius: 9999px;
  padding: 4px;
}

.origin-beta {
  color: #FBC02D;
}

.origin-button>button {
  padding: 3px 9px;
}

button.card.category-tile {
  padding: 21px;
  border: none;
  border-radius: 6px;
  min-width: 140px;
  font-weight: 900;
  font-size: larger;
}

button.card.category-tile.category-tile-iconless {
  padding: 9px 15px;
  min-width: 130px;
  font-size: 105%;
  font-weight: normal;
}

button.card.category-tile.category-create {
  background: linear-gradient(180deg, #ce8cd7 0%, #2861c6 100%);
  color: white;
}

button.card.category-tile.category-create:hover {
  background: linear-gradient(180deg, shade(#ce8cd7, 1.07) 0%, shade(#2861c6, 1.1) 100%);
  color: white;
}

button.card.category-tile.category-create:active {
  background: linear-gradient(180deg, shade(#ce8cd7, 0.95) 0%, shade(#2861c6, 0.95) 100%);
  color: white;
}

button.card.category-tile.category-develop {
  background: #5e5c64;
  color: white;
}

button.card.category-tile.category-develop:hover {
  background: shade(#5e5c64, 1.2);
  color: white;
}

button.card.category-tile.category-develop:active {
  background-color: shade(#5e5c64, 0.95);
  color: white;
}

button.card.category-tile.category-learn {
  background: linear-gradient(180deg, #2ec27e 30%, #27a66c 100%);
  color: white;
}

button.card.category-tile.category-learn:hover {
  background: linear-gradient(180deg, shade(#2ec27e, 1.06) 30%, shade(#27a66c, 1.06) 100%);
  color: white;
}

button.card.category-tile.category-learn:active {
  background: linear-gradient(180deg, shade(#2ec27e, 0.95) 30%, shade(#27a66c, 0.95) 100%);
  color: white;
}

button.card.category-tile.category-play {
  background: linear-gradient(75deg, #f9e2a7 0%, #eb5ec3 50%, #6d53e0 100%);
  color: #393484;
}

button.card.category-tile.category-play:hover {
  background: linear-gradient(75deg, shade(#f9e2a7, 1.07) 0%, shade(#eb5ec3, 1.07) 50%, shade(#6d53e0, 1.07) 100%);
  color: #393484;
}

button.card.category-tile.category-play:active {
  background: linear-gradient(75deg, shade(#f9e2a7, 0.97) 0%, shade(#eb5ec3, 0.95) 50%, shade(#6d53e0, 1.07) 100%);
  color: #393484;
}

button.card.category-tile.category-socialize {
  background: linear-gradient(90deg, #ef4e9b 0%, #f77466 100%);
  color: rgba(255, 255, 255, 0.7);
}

button.card.category-tile.category-socialize:hover {
  background: linear-gradient(90deg, shade(#ef4e9b, 1.08) 0%, shade(#f77466, 1.08) 100%);
}

button.card.category-tile.category-socialize:active {
  background: linear-gradient(90deg, shade(#ef4e9b, 0.95) 0%, shade(#f77466, 0.95) 100%);
}

button.card.category-tile.category-work {
  padding: 1px;
  /* FIXME: work around https://gitlab.gnome.org/GNOME/gtk/-/issues/4324 */
  color: #1c71d8;
  background-color: #fdf8d7;
  background-image: linear-gradient(rgba(255, 255, 255, 0.12) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.12) 1px, transparent 1px);
  background-size: 10px 10px, 10px 10px;
  background-position: -1px -4px, center -1px;
}

button.card.category-tile.category-work:hover {
  color: #1c71d8;
  background-color: #fefcef;
  background-image: linear-gradient(rgba(255, 255, 255, 0.12) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.12) 1px, transparent 1px);
}

button.card.category-tile.category-work:active {
  color: #1c71d8;
  background-color: #fcf4bf;
  background-image: linear-gradient(rgba(255, 255, 255, 0.12) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.12) 1px, transparent 1px);
}

clamp.medium .category-tile:not(.category-tile-iconless),
clamp.large .category-tile:not(.category-tile-iconless) {
  font-size: larger;
}

.featured-tile {
  all: unset;
  padding: 0;
  box-shadow: none;
  color: #FFFFFF;
}

.featured-tile label.title-1 {
  margin-top: 6px;
  margin-bottom: 6px;
}

.featured-tile.narrow label.title-1 {
  font-size: 16pt;
}

.application-details-infobar,
.application-details-infobar.info {
  background-color: rgba(255, 255, 255, 0.04);
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.12);
}

.application-details-infobar.warning {
  background-color: #FBC02D;
  color: rgba(0, 0, 0, 0.87);
  border: 1px solid rgba(0, 0, 0, 0.12);
}

@keyframes install-progress-unknown-move {
  0% {
    background-position: 0%;
  }

  50% {
    background-position: 100%;
  }

  100% {
    background-position: 0%;
  }
}

.application-details-description .button {
  padding-left: 24px;
  padding-right: 24px;
}

.install-progress {
  background-image: linear-gradient(to top, #8caaee 2px, alpha(#8caaee, 0) 2px);
  background-repeat: no-repeat;
  background-position: 0 bottom;
  background-size: 0;
  transition: none;
}

.install-progress:dir(rtl) {
  background-position: 100% bottom;
}

.review-row>* {
  margin: 12px;
}

.review-row button {
  font-size: smaller;
}

.review-row .vote-buttons button {
  margin-right: -1px;
}

.review-row .vote-buttons button:not(:first-child) {
  border-image: linear-gradient(to top, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.12)) 0 0 0 1/5px 0 5px 1px;
}

.review-row .vote-buttons button:hover,
.review-row .vote-buttons button:active,
.review-row .vote-buttons button:hover+button,
.review-row .vote-buttons button:active+button {
  border-image: none;
}

review-bar {
  color: rgba(255, 255, 255, 0.5);
  background-image: none;
  background-color: rgba(255, 255, 255, 0.3);
}

.review-histogram star-image {
  color: rgba(255, 255, 255, 0.5);
}

.version-arrow-label {
  font-size: x-small;
}

.overview-more-button {
  font-size: smaller;
  padding: 0 16px;
}

.app-row-origin-text {
  font-size: smaller;
}

.app-listbox-header {
  padding: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

.image-list {
  background-color: transparent;
}

box.star {
  background-color: transparent;
  background-image: none;
}

button.star {
  outline-offset: 0;
  background-color: transparent;
  background-image: none;
  border-image: none;
  border-radius: 0;
  border-width: 0;
  padding: 0;
  box-shadow: none;
  outline-offset: -1px;
}

star-image {
  color: #FFD600;
}

.dimmer-label {
  opacity: 0.25;
}

.update-failed-details {
  font-family: Monospace;
  font-size: smaller;
  padding: 16px;
}

.upgrade-banner {
  padding: 0px;
  border-radius: 6px;
  border: none;
}

.upgrade-banner-background {
  background: linear-gradient(to bottom, #66BB6A, #8caaee);
  color: white;
}

.upgrade-buttons #button_upgrades_install {
  padding-left: 16px;
  padding-right: 16px;
}

scrolledwindow.list-page>viewport>clamp>box {
  margin: 24px 12px;
  border-spacing: 24px;
}

.update-preferences preferencesgroup>box>box {
  margin-top: 18px;
}

.section>label:not(:first-child) {
  margin-top: 6px;
}

.section>box:not(:first-child) {
  margin-top: 12px;
}

clamp.status-page {
  margin: 36px 12px;
}

clamp.status-page .iconbox {
  min-height: 128px;
  min-width: 128px;
}

clamp.status-page .icon {
  color: rgba(255, 255, 255, 0.5);
  min-height: 32px;
  min-width: 32px;
}

clamp.status-page .icon:not(:last-child) {
  margin-bottom: 36px;
}

clamp.status-page .title:not(:last-child) {
  margin-bottom: 12px;
}

app-context-bar .context-tile {
  border: 1px solid rgba(255, 255, 255, 0.12);
  background-color: transparent;
  border-radius: 0;
  padding: 24px 12px 21px 12px;
  outline-offset: 5px;
  transition-property: outline, outline-offset, background-image;
  border-bottom: none;
  border-right: none;
}

app-context-bar .context-tile:hover {
  background-image: none;
  background-color: alpha(currentColor, 0.08);
}

app-context-bar .context-tile.keyboard-activating,
app-context-bar .context-tile:active {
  background-color: alpha(currentColor, 0.12);
}

app-context-bar .context-tile:focus:focus-visible {
  outline-offset: -1px;
}

app-context-bar.horizontal box:first-child .context-tile:first-child,
app-context-bar.vertical .context-tile:first-child {
  border-left: none;
}

app-context-bar.horizontal .context-tile,
app-context-bar.vertical box:first-child .context-tile {
  border-top: none;
}

app-context-bar>box:not(:first-child)>button.flat {
  border-radius: 0;
}

app-context-bar>box:not(:first-child)>button.flat:last-child {
  border-radius: 0 6px 6px 0;
}

app-context-bar>box:first-child>button.flat {
  border-radius: 0;
}

app-context-bar>box:first-child>button.flat:first-child {
  border-radius: 6px 0 0 6px;
}

app-context-bar>box>button.flat {
  border-left-color: rgba(255, 255, 255, 0.12);
}

carousel.card {
  border: none;
  background-color: rgba(255, 255, 255, 0.04);
}

.context-tile-lozenge {
  min-height: 28px;
  min-width: 28px;
  padding: 6px;
  font-size: 18px;
  font-weight: bold;
  border-radius: 9999px;
}

.context-tile-lozenge.large {
  font-size: 24px;
  padding: 16px;
  min-width: 24px;
  /* 60px minus the left and right padding */
  min-height: 24px;
  /* 60px minus the top and bottom padding */
}

.context-tile-lozenge.wide-image image {
  margin-top: -28px;
  margin-bottom: -28px;
}

.context-tile-lozenge image {
  -gtk-icon-style: symbolic;
}

.context-tile-lozenge.grey {
  color: #FFFFFF;
  background-color: rgba(255, 255, 255, 0.12);
}

.context-tile-lozenge.green,
.context-tile-lozenge.details-rating-0 {
  color: #419345;
  background-color: rgba(102, 187, 106, 0.15);
}

.context-tile-lozenge.blue,
.context-tile-lozenge.details-rating-5 {
  color: #8caaee;
  background-color: rgba(140, 170, 238, 0.15);
}

.context-tile-lozenge.yellow,
.context-tile-lozenge.details-rating-12 {
  color: #d79b04;
  background-color: rgba(251, 192, 45, 0.15);
}

.context-tile-lozenge.details-rating-15 {
  color: #FF8A65;
  background-color: rgba(255, 138, 101, 0.15);
}

.context-tile-lozenge.red,
.context-tile-lozenge.details-rating-18 {
  color: #d2190b;
  background-color: rgba(244, 67, 54, 0.15);
}

.eol-red {
  font-weight: bold;
  color: #F44336;
}

window.narrow .app-title {
  font-size: 16pt;
}

window.narrow .app-developer {
  font-size: small;
}

.install-progress-label {
  font-size: smaller;
  font-feature-settings: "tnum";
}

scrolledwindow.fake-adw-status-page>viewport>box {
  margin: 36px 12px;
}

scrolledwindow.fake-adw-status-page>viewport>box>clamp:not(:last-child)>box {
  margin-bottom: 36px;
}

scrolledwindow.fake-adw-status-page>viewport>box>clamp>box>.icon:not(:last-child) {
  margin-bottom: 36px;
}

scrolledwindow.fake-adw-status-page>viewport>box>clamp>box>.title:not(:last-child) {
  margin-bottom: 12px;
}

statuspage.icon-dropshadow image.icon {
  -gtk-icon-shadow: 0 1px 12px rgba(0, 0, 0, 0.05), 0 -1px rgba(0, 0, 0, 0.05), 1px 0 rgba(0, 0, 0, 0.1), 0 1px rgba(0, 0, 0, 0.3), -1px 0 rgba(0, 0, 0, 0.1);
}

window.info scrollbar.vertical {
  margin-top: 48px;
  background: none;
  box-shadow: none;
}

window.info scrollbar.vertical trough {
  margin-top: 0;
}

row.app>box.header {
  margin-left: 12px;
  margin-right: 12px;
}

row.app>box.header {
  border-spacing: 12px;
}

row.app>box.header>image {
  margin-top: 12px;
  margin-bottom: 12px;
}

row.app label.warning {
  color: #F44336;
}

@keyframes pre-delay {
  from {
    opacity: 0;
  }

  to {
    opacity: 0;
  }
}

@keyframes fade-in {
  from {
    filter: opacity(0%);
  }
}

/* Give a fade-in animation to spinners. */
spinner.fade-in:checked {
  animation: pre-delay 0.5s linear 1, fade-in 1s linear 1, spin 1s linear infinite;
  animation-delay: 0s, 0.5s, 0.5s;
}

window>contents>leaflet>box>stack.background {
  background-color: transparent;
  background-image: linear-gradient(to bottom, transparent, transparent 48px, #303446 48px, #303446);
}

@define-color weather_temp_chart_fill_color rgba(251, 192, 45, 0.5);
@define-color weather_temp_chart_stroke_color #f0ad05;
@define-color weather_thermometer_warm_color #FBC02D;
@define-color weather_thermometer_cold_color #8caaee;

#places-label {
  font-weight: bold;
}

#temperature-label {
  font-size: 32pt;
  font-weight: 900;
  margin-left: 9px;
}

#conditions-grid *:backdrop {
  color: rgba(255, 255, 255, 0.7);
}

.content-view.cell {
  font-weight: bold;
}

#locationEntry {
  margin: 6px;
}

.weather-popover {
  margin-top: 6px;
}

.forecast-card {
  transition: border-radius 100ms ease-out;
  border-radius: 6px;
}

.forecast-card separator {
  background-color: rgba(255, 255, 255, 0.12);
}

#daily-forecast-box>separator:last-child {
  background-color: transparent;
  min-width: 0;
}

#conditions-grid,
#attributionGrid {
  margin-left: 18px;
  margin-right: 18px;
}

#weather-page .small .forecast-card {
  margin-left: 0;
  margin-right: 0;
  border-radius: 0;
  border-width: 1px 0;
}

.forecast-temperature-label {
  font-weight: bold;
  color: #ae7b03;
}

WeatherThermometer {
  margin-bottom: 12px;
}

WeatherThermometer>label.high {
  font-weight: bold;
  color: #FBC02D;
}

WeatherThermometer>label.low {
  font-weight: bold;
  color: #8caaee;
}

.forecast-button {
  margin: 0 12px;
}

.forecast-graphic {
  margin: 18px;
}

button.osd.circular {
  border-radius: 9999px;
  min-width: 24px;
  min-height: 24px;
}

button.osd.circular>image {
  padding: 0;
}

scrolledwindow.inline list,
scrolledwindow.inline listview {
  background: none;
  color: inherit;
}

scrolledwindow.inline undershoot.top {
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.12);
}

.search-view {
  background-color: #1e66f5;
  color: #FFFFFF;
}

.search-view menubutton button:focus:focus-visible {
  outline-color: rgba(255, 255, 255, 0.3);
}

image.circular {
  min-width: 36px;
  min-height: 36px;
  padding: 0;
  border-radius: 9999px;
}

.large-button {
  padding: 6px;
}

.alarm-time {
  font-size: 2.5em;
  font-weight: 300;
}

.clocks-ampm-toggle-button,
.clocks-secondary-standalone-label {
  font-size: 18pt;
}

.clocks-standalone-label,
.clocks-ringing-label {
  font-size: 6em;
  font-weight: 300;
}

.clocks-ringing-title {
  font-size: 1.5em;
  font-weight: bold;
}

.clocks-alarm-setup-time {
  font-size: 32pt;
}

.clocks-timer-label,
.clocks-spinbutton {
  font-size: 48pt;
}

.timer-panel .timer-header {
  font-size: 20pt;
  font-weight: 300;
}

.timer-countdown {
  font-size: 40pt;
  font-weight: 300;
}

/* Stopwatch Panel */
.lap-time {
  font-weight: bold;
}

.stopped-stopwatch label,
.running-stopwatch label,
.paused-stopwatch label {
  font-size: 70px;
  font-weight: lighter;
}

.stopped-stopwatch .seconds-label {
  font-weight: 300;
}

.running-stopwatch .seconds-label,
.running-stopwatch .miliseconds-label {
  color: #8caaee;
}

.stopped-stopwatch .miliseconds-label,
.running-stopwatch .miliseconds-label,
.paused-stopwatch .miliseconds-label {
  font-size: 50px;
}

.running-stopwatch .seconds-label,
.paused-stopwatch .seconds-label {
  font-weight: 300;
}

.clock-location {
  font-weight: bolder;
}

.hidden {
  opacity: 0;
}

.clock-time {
  font-size: 2em;
  padding: 0.2em 0.5em;
  border-radius: 1em;
}

.none .clock-time {
  background: alpha(currentColor, 0.2);
}

.night .clock-time {
  color: #1e66f5;
  background-color: alpha(#8caaee, 0.25);
}

.naut .clock-time,
.astro .clock-time {
  color: #FF7043;
  background-color: alpha(#FF8A65, 0.25);
}

.civil .clock-time,
.day .clock-time {
  color: #FFD600;
  background: alpha(#FBC02D, 0.25);
}

headerbar splitbutton notification button {
  margin: 0;
  min-height: 24px;
  min-width: 24px;
  padding: 0;
}

popover.background.global-search>arrow,
popover.background.global-search>contents {
  padding: 0;
}

panelframeswitcher {
  padding: 3px;
}

.frameheader.header {
  min-height: 24px;
  background-color: #232634;
}

.frameheader.header>button {
  border: none;
  margin: 0;
  padding: 3px;
}

.frameheader.header tabbar.inline>revealer>box {
  min-height: 24px;
}

.frameheader.header tabbar.inline>revealer>box .start-action {
  padding: 0;
  border: none;
}

.frameheader.header tabbar.inline>revealer>box .end-action {
  padding: 0;
  border-left: 1px solid rgba(255, 255, 255, 0.12);
}

.frameheader.header tabbar.inline>revealer>box tabbox {
  border: none;
  background: none;
}

panelstatusbar>menubutton>button,
panelstatusbar>paneltogglebutton button {
  border-radius: 0;
}

.style-variant {
  padding: 0 12px;
}

.style-variant button.toggle {
  padding: 0;
}

.style-variant button.toggle,
.style-variant button.toggle:hover,
.style-variant button.toggle:focus,
.style-variant button.toggle:active,
.style-variant button.toggle:checked {
  background: none;
  outline: none;
  border: none;
  box-shadow: none;
}

.style-variant button.toggle>stylevariantpreview>.wallpaper {
  border-radius: 6px;
  outline-color: transparent;
  outline-width: 3px;
  outline-offset: 3px;
  outline-style: solid;
  box-shadow: none;
}

.style-variant button.toggle:hover>stylevariantpreview>.wallpaper {
  outline-color: rgba(255, 255, 255, 0.04);
}

.style-variant button.toggle:active>stylevariantpreview>.wallpaper {
  outline-color: rgba(255, 255, 255, 0.3);
}

.style-variant button.toggle:checked>stylevariantpreview>.wallpaper {
  outline-color: #8caaee;
}

window.dialog>.dialog-vbox>box>scrolledwindow>viewport>widget>list.boxed-list {
  border: none;
  border-radius: 0;
}

window.dialog>.dialog-vbox>box>scrolledwindow>viewport>widget>list.boxed-list>row:first-child,
window.dialog>.dialog-vbox>box>scrolledwindow>viewport>widget>list.boxed-list>row:last-child {
  border-radius: 0;
}

avatar {
  border-radius: 9999px;
  font-weight: bold;
}

avatar.color1 {
  background-image: linear-gradient(#83b6ec, #337fdc);
  color: #cfe1f5;
}

avatar.color2 {
  background-image: linear-gradient(#7ad9f1, #0f9ac8);
  color: #caeaf2;
}

avatar.color3 {
  background-image: linear-gradient(#8de6b1, #29ae74);
  color: #cef8d8;
}

avatar.color4 {
  background-image: linear-gradient(#b5e98a, #6ab85b);
  color: #e6f9d7;
}

avatar.color5 {
  background-image: linear-gradient(#f8e359, #d29d09);
  color: #f9f4e1;
}

avatar.color6 {
  background-image: linear-gradient(#ffcb62, #d68400);
  color: #ffead1;
}

avatar.color7 {
  background-image: linear-gradient(#ffa95a, #ed5b00);
  color: #ffe5c5;
}

avatar.color8 {
  background-image: linear-gradient(#f78773, #e62d42);
  color: #f8d2ce;
}

avatar.color9 {
  background-image: linear-gradient(#e973ab, #e33b6a);
  color: #fac7de;
}

avatar.color10 {
  background-image: linear-gradient(#cb78d4, #9945b5);
  color: #e7c2e8;
}

avatar.color11 {
  background-image: linear-gradient(#9e91e8, #7a59ca);
  color: #d5d2f5;
}

avatar.color12 {
  background-image: linear-gradient(#e3cf9c, #b08952);
  color: #f2eade;
}

avatar.color13 {
  background-image: linear-gradient(#be916d, #785336);
  color: #e5d6ca;
}

avatar.color14 {
  background-image: linear-gradient(#c0bfbc, #6e6d71);
  color: #d8d7d3;
}

avatar.contrasted {
  color: white;
}

avatar.image {
  background: none;
}

.card {
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  background-clip: border-box;
  color: rgba(255, 255, 255, 0.7);
  box-shadow: none;
  outline: none;
  background-color: #303446;
  color: #FFFFFF;
}

.card.activatable {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
}

.card.activatable:hover {
  background-image: none;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}

.card.activatable:active {
  background-image: none;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, border 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-image: radial-gradient(circle, alpha(currentColor, 0.08) 10%, transparent 0%);
  background-size: 0% 0%;
  background-color: alpha(currentColor, 0.08);
  color: #FFFFFF;
  outline: 0 solid transparent;
}

toast {
  margin: 12px;
  margin-bottom: 24px;
  border-radius: 9999px;
  border-spacing: 6px;
  padding: 6px;
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.1), 0 4px 6px 0 rgba(0, 0, 0, 0.12), 0 1px 10px 0 rgba(0, 0, 0, 0.1), inset 0 1px rgba(255, 255, 255, 0.1);
  background-color: #303446;
  color: #FFFFFF;
  border: none;
}

toast:dir(ltr) {
  padding-left: 12px;
}

toast:dir(rtl) {
  padding-right: 12px;
}

toast>label {
  margin: 0 6px;
}

viewswitcher {
  margin: 0;
}

viewswitcher.wide {
  border-spacing: 3px;
}

viewswitcher.narrow button.toggle {
  border-radius: 0;
  margin: 0;
}

viewswitcher.narrow button.toggle:focus-within,
viewswitcher.narrow button.toggle:focus {
  box-shadow: none;
}

viewswitcher button.toggle {
  font-weight: bold;
  padding: 0;
}

viewswitcher button.toggle>stack>box.narrow {
  font-size: 0.75rem;
  padding-top: 6px;
  padding-bottom: 4px;
  border-spacing: 4px;
}

viewswitcher button.toggle>stack>box.narrow>stack>label {
  padding-left: 6px;
  padding-right: 6px;
}

viewswitcher button.toggle>stack>box.wide {
  padding: 2px 12px;
  border-spacing: 6px;
}

viewswitcherbar actionbar>revealer>box {
  padding: 0;
}

viewswitchertitle viewswitcher {
  margin-left: 12px;
  margin-right: 12px;
}

indicatorbin>indicator,
indicatorbin>mask {
  min-width: 6px;
  min-height: 6px;
  border-radius: 9999px;
}

indicatorbin>indicator {
  margin: 1px;
  background-color: alpha(currentColor, 0.4);
}

indicatorbin>indicator>label {
  font-size: 0.6rem;
  font-weight: bold;
  padding: 1px 4px;
  color: white;
}

indicatorbin>mask {
  padding: 1px;
  background: black;
}

indicatorbin.needs-attention>indicator {
  background-color: #8caaee;
}

indicatorbin.needs-attention>indicator>label {
  color: rgba(0, 0, 0, 0.87);
}

preferencespage>scrolledwindow>viewport>clamp>box {
  margin: 24px 12px;
  border-spacing: 24px;
}

preferencesgroup>box,
preferencesgroup>box .labels {
  border-spacing: 6px;
}

preferencesgroup>box>box.header:not(.single-line) {
  margin-bottom: 6px;
}

preferencesgroup>box>box.single-line {
  min-height: 34px;
}

preferencesgroup>box button.background-preview-button.toggle {
  padding: 0;
  background: none;
  box-shadow: none;
  outline-color: transparent;
  outline-width: 3px;
  outline-offset: 3px;
  outline-style: solid;
}

preferencesgroup>box button.background-preview-button.toggle,
preferencesgroup>box button.background-preview-button.toggle>background-preview {
  border-radius: 6px;
}

preferencesgroup>box button.background-preview-button.toggle:hover {
  outline-color: rgba(255, 255, 255, 0.04);
}

preferencesgroup>box button.background-preview-button.toggle:active {
  outline-color: rgba(255, 255, 255, 0.3);
}

preferencesgroup>box button.background-preview-button.toggle:checked {
  outline-color: #8caaee;
}

preferencesgroup>box .cutout-button {
  background-color: #232634;
}

window.about .main-page>viewport>clamp>box {
  margin: 12px;
  border-spacing: 6px;
}

window.about .main-page>viewport>clamp>box>box {
  margin-top: 18px;
  border-spacing: 18px;
  margin-bottom: 6px;
}

window.about .main-page .app-version {
  padding: 3px 18px;
  color: #8caaee;
  border-radius: 6px;
  margin-top: 3px;
}

window.about .subpage>viewport>clamp>box {
  margin: 18px 12px;
  border-spacing: 18px;
}

window.about .subpage>clamp>textview {
  background: none;
  color: inherit;
}

statuspage>scrolledwindow>viewport>box {
  margin: 36px 12px;
  border-spacing: 36px;
}

statuspage>scrolledwindow>viewport>box>clamp>box {
  border-spacing: 12px;
}

statuspage>scrolledwindow>viewport>box>clamp>box>.icon {
  -gtk-icon-size: 128px;
  color: alpha(currentColor, 0.55);
}

statuspage>scrolledwindow>viewport>box>clamp>box>.icon:disabled {
  opacity: 0.35;
}

statuspage>scrolledwindow>viewport>box>clamp>box>.icon:not(:last-child) {
  margin-bottom: 24px;
}

statuspage.compact>scrolledwindow>viewport>box {
  margin: 24px 12px;
  border-spacing: 24px;
}

statuspage.compact>scrolledwindow>viewport>box>clamp>box>.icon {
  -gtk-icon-size: 96px;
}

statuspage.compact>scrolledwindow>viewport>box>clamp>box>.icon:not(:last-child) {
  margin-bottom: 12px;
}

statuspage.compact>scrolledwindow>viewport>box>clamp>box>.title {
  font-size: 18pt;
}

/* GTK NAMED COLORS
   ----------------
   use responsibly! */
/*
widget text/foreground color */
@define-color theme_fg_color #FFFFFF;
/*
text color for entries, views and content in general */
@define-color theme_text_color #FFFFFF;
/*
widget base background color */
@define-color theme_bg_color #303446;
/*
text widgets and the like base background color */
@define-color theme_base_color #303446;
/*
base background color of selections */
@define-color theme_selected_bg_color #8caaee;
/*
text/foreground color of selections */
@define-color theme_selected_fg_color rgba(0, 0, 0, 0.87);
/*
base background color of insensitive widgets */
@define-color insensitive_bg_color #303446;
/*
text foreground color of insensitive widgets */
@define-color insensitive_fg_color rgba(255, 255, 255, 0.5);
/*
insensitive text widgets and the like base background color */
@define-color insensitive_base_color #232634;
/*
widget text/foreground color on backdrop windows */
@define-color theme_unfocused_fg_color #FFFFFF;
/*
text color for entries, views and content in general on backdrop windows */
@define-color theme_unfocused_text_color #FFFFFF;
/*
widget base background color on backdrop windows */
@define-color theme_unfocused_bg_color #303446;
/*
text widgets and the like base background color on backdrop windows */
@define-color theme_unfocused_base_color #303446;
/*
base background color of selections on backdrop windows */
@define-color theme_unfocused_selected_bg_color #8caaee;
/*
text/foreground color of selections on backdrop windows */
@define-color theme_unfocused_selected_fg_color rgba(0, 0, 0, 0.87);
/*
insensitive color on backdrop windows */
@define-color unfocused_insensitive_color rgba(255, 255, 255, 0.5);
/*
widgets main borders color */
@define-color borders rgba(255, 255, 255, 0.12);
/*
widgets main borders color on backdrop windows */
@define-color unfocused_borders rgba(255, 255, 255, 0.12);
/*
these are pretty self explicative */
@define-color warning_color #FBC02D;
@define-color error_color #F44336;
@define-color success_color #66BB6A;
/*
these colors are exported for the window manager and shouldn't be used in applications,
read if you used those and something break with a version upgrade you're on your own... */
@define-color wm_title #FFFFFF;
@define-color wm_unfocused_title rgba(255, 255, 255, 0.7);
@define-color wm_highlight rgba(255, 255, 255, 0.1);
@define-color wm_border #0a0a0e;
@define-color wm_bg #232634;
@define-color wm_unfocused_bg #303446;
@define-color wm_button_icon white;
@define-color wm_button_close_hover_bg #e78284;
@define-color wm_button_close_active_bg #dd4f51;
@define-color wm_button_max_hover_bg #a6d189;
@define-color wm_button_max_active_bg #85c05d;
@define-color wm_button_min_hover_bg #e5c890;
@define-color wm_button_min_active_bg #d9af5e;
/*
FIXME this is really an API */
@define-color content_view_bg #303446;
@define-color placeholder_text_color #c1c2c8;
/* Very contrasty background for text views (@theme_text_color foreground) */
@define-color text_view_bg #303446;
@define-color budgie_tasklist_indicator_color rgba(255, 255, 255, 0.3);
@define-color budgie_tasklist_indicator_color_active #8caaee;
@define-color budgie_tasklist_indicator_color_active_window #5c6d98;
@define-color budgie_tasklist_indicator_color_attention #FBC02D;
@define-color STRAWBERRY_100 #FF9262;
@define-color STRAWBERRY_300 #FF793E;
@define-color STRAWBERRY_500 #F15D22;
@define-color STRAWBERRY_700 #CF3B00;
@define-color STRAWBERRY_900 #AC1800;
@define-color ORANGE_100 #FFDB91;
@define-color ORANGE_300 #FFCA40;
@define-color ORANGE_500 #FAA41A;
@define-color ORANGE_700 #DE8800;
@define-color ORANGE_900 #C26C00;
@define-color BANANA_100 #FFFFA8;
@define-color BANANA_300 #FFFA7D;
@define-color BANANA_500 #FFCE51;
@define-color BANANA_700 #D1A023;
@define-color BANANA_900 #A27100;
@define-color LIME_100 #A2F3BE;
@define-color LIME_300 #8ADBA6;
@define-color LIME_500 #73C48F;
@define-color LIME_700 #479863;
@define-color LIME_900 #1C6D38;
@define-color BLUEBERRY_100 #94A6FF;
@define-color BLUEBERRY_300 #6A7CE0;
@define-color BLUEBERRY_500 #3F51B5;
@define-color BLUEBERRY_700 #213397;
@define-color BLUEBERRY_900 #031579;
@define-color GRAPE_100 #D25DE6;
@define-color GRAPE_300 #B84ACB;
@define-color GRAPE_500 #9C27B0;
@define-color GRAPE_700 #830E97;
@define-color GRAPE_900 #6A007E;
@define-color COCOA_100 #9F9792;
@define-color COCOA_300 #7B736E;
@define-color COCOA_500 #574F4A;
@define-color COCOA_700 #463E39;
@define-color COCOA_900 #342C27;
@define-color SILVER_100 #EEE;
@define-color SILVER_300 #CCC;
@define-color SILVER_500 #AAA;
@define-color SILVER_700 #888;
@define-color SILVER_900 #666;
@define-color SLATE_100 #888;
@define-color SLATE_300 #666;
@define-color SLATE_500 #444;
@define-color SLATE_700 #222;
@define-color SLATE_900 #111;
@define-color BLACK_100 #474341;
@define-color BLACK_300 #403C3A;
@define-color BLACK_500 #393634;
@define-color BLACK_700 #33302F;
@define-color BLACK_900 #2B2928;
@define-color blue_1 #99c1f1;
@define-color blue_2 #62a0ea;
@define-color blue_3 #3584e4;
@define-color blue_4 #1c71d8;
@define-color blue_5 #1a5fb4;
@define-color green_1 #8ff0a4;
@define-color green_2 #57e389;
@define-color green_3 #33d17a;
@define-color green_4 #2ec27e;
@define-color green_5 #26a269;
@define-color yellow_1 #f9f06b;
@define-color yellow_2 #f8e45c;
@define-color yellow_3 #f6d32d;
@define-color yellow_4 #f5c211;
@define-color yellow_5 #e5a50a;
@define-color orange_1 #ffbe6f;
@define-color orange_2 #ffa348;
@define-color orange_3 #ff7800;
@define-color orange_4 #e66100;
@define-color orange_5 #c64600;
@define-color red_1 #f66151;
@define-color red_2 #ed333b;
@define-color red_3 #e01b24;
@define-color red_4 #c01c28;
@define-color red_5 #a51d2d;
@define-color purple_1 #dc8add;
@define-color purple_2 #c061cb;
@define-color purple_3 #9141ac;
@define-color purple_4 #813d9c;
@define-color purple_5 #613583;
@define-color brown_1 #cdab8f;
@define-color brown_2 #b5835a;
@define-color brown_3 #986a44;
@define-color brown_4 #865e3c;
@define-color brown_5 #63452c;
@define-color light_1 #ffffff;
@define-color light_2 #f6f5f4;
@define-color light_3 #deddda;
@define-color light_4 #c0bfbc;
@define-color light_5 #9a9996;
@define-color dark_1 #77767b;
@define-color dark_2 #5e5c64;
@define-color dark_3 #3d3846;
@define-color dark_4 #241f31;
@define-color dark_5 #000000;
/* GTK NAMED COLORS
   ----------------
   use responsibly! */
@define-color accent_bg_color #8caaee;
@define-color accent_fg_color rgba(0, 0, 0, 0.87);
@define-color accent_color #8caaee;
@define-color destructive_bg_color #F44336;
@define-color destructive_fg_color #FFFFFF;
@define-color destructive_color #F44336;
@define-color success_bg_color #66BB6A;
@define-color success_fg_color #FFFFFF;
@define-color success_color #66BB6A;
@define-color warning_bg_color #FBC02D;
@define-color warning_fg_color rgba(0, 0, 0, 0.87);
@define-color warning_color #FBC02D;
@define-color error_bg_color #F44336;
@define-color error_fg_color #FFFFFF;
@define-color error_color #F44336;
@define-color window_bg_color #303446;
@define-color window_fg_color #FFFFFF;
@define-color view_bg_color #303446;
@define-color view_fg_color #FFFFFF;
@define-color headerbar_bg_color #232634;
@define-color headerbar_fg_color #FFFFFF;
@define-color headerbar_border_color rgba(255, 255, 255, 0.12);
@define-color headerbar_backdrop_color #303446;
@define-color headerbar_shade_color rgba(255, 255, 255, 0.12);
@define-color card_bg_color #303446;
@define-color card_fg_color #FFFFFF;
@define-color card_shade_color rgba(255, 255, 255, 0.12);
@define-color dialog_bg_color #232634;
@define-color dialog_fg_color #FFFFFF;
@define-color popover_bg_color #303446;
@define-color popover_fg_color #FFFFFF;
@define-color shade_color rgba(255, 255, 255, 0.12);
@define-color scrollbar_outline_color rgba(255, 255, 255, 0.12);
