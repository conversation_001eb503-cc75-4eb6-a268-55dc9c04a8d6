[files]
recent_files=/home/<USER>/.config/sddm-config-editor/sddm-config-editor.conf;/home/<USER>/.config/session/dolphin_dolphin_dolphin;/home/<USER>/hyprland-dots/Hyprland-blizz/sddm.conf;/home/<USER>/Documents/HDT/HDETE/dosdevices/c:/users/<USER>/Documents/Overwatch/Settings/Settings_v0.ini;/home/<USER>/bin/autounzip.sh;/home/<USER>/.local/share/applications/autounzip.desktop;/home/<USER>/Downloads/MSET9-v2.0/_INSTRUCTIONS.txt;/home/<USER>/Downloads/key.1.txt;/home/<USER>/Downloads/BepInEx-BepInExPack_PEAK-5.4.2403/README.md;/home/<USER>/.config/kwalletmanager5rc;
recent_projects=
current_page=-1

[project]
session_file=
project_file_path=/home/<USER>/projects

[geany]
treeview_position=156
msgwindow_position=758
geometry=0;0;845;1385;1;
sidebar_page=0

[VTE]
last_dir=/home/<USER>

[search]
find_all_expanded=false
replace_all_expanded=false
position_find_x=-1
position_find_y=-1
position_replace_x=-1
position_replace_y=-1
position_fif_x=-1
position_fif_y=-1

[plugins]
load_plugins=true
custom_plugin_path=
active_plugins=;
