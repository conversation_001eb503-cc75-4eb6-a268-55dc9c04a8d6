2025-07-16 12:12:24.662 [info] Using the Electron fetcher.
2025-07-16 12:12:24.662 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-07-16 12:12:24.662 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-07-16 12:12:24.662 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-07-16 12:12:24.662 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-07-16 12:12:24.719 [info] Logged in as Bananonymous
2025-07-16 12:12:26.052 [info] Got Copilot token for Bananonymous
2025-07-16 12:12:26.057 [info] activationBlocker from 'languageModelAccess' took for 2056ms
2025-07-16 12:12:26.978 [info] copilot token chat_enabled: true, sku: free_educational_quota
2025-07-16 12:12:26.978 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-07-16 12:12:26.987 [info] Registering default platform agent...
2025-07-16 12:12:26.987 [info] activationBlocker from 'conversationFeature' took for 2987ms
2025-07-16 12:12:27.003 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-07-16 12:12:27.337 [info] Fetched model metadata in 1280ms c5320441-062c-4e1f-bf8e-8f6474460a0c
