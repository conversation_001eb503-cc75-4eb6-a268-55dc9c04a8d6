2025-07-16 12:12:21.158 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-16 12:12:21.167 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-16 12:12:21.167 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-16 12:12:21.404 [info] Started local extension host with pid 18596.
2025-07-16 12:12:21.942 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:22.054 [error] Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-16 12:12:22.094 [warning] [twxs.cmake]: Cannot register 'cmake.cmakePath'. This property is already registered.
2025-07-16 12:12:23.714 [error] [Extension Host] (node:18596) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `exe --trace-deprecation ...` to show where the warning was created)
2025-07-16 12:12:23.981 [info] Settings Sync: Account status changed from uninitialized to available
2025-07-16 12:12:24.242 [info] Auto updating outdated extensions. augment.vscode-augment, github.copilot, github.copilot-chat, ms-python.debugpy, ms-python.python, ms-vscode.cmake-tools
2025-07-16 12:12:24.251 [info] [perf] Render performance baseline is 10ms
2025-07-16 12:12:24.747 [info] Auto updating outdated extensions. augment.vscode-augment, github.copilot, github.copilot-chat, ms-python.debugpy, ms-python.python, ms-vscode.cmake-tools
2025-07-16 12:12:24.880 [error] [File Watcher ('parcel')] Unexpected error: inotify_add_watch on '/home/<USER>/.config/Code/User/sync/globalState/preview' failed: No such file or directory (EUNKNOWN) (path: /home/<USER>/.config)
2025-07-16 12:12:24.881 [error] [File Watcher (universal)] inotify_add_watch on '/home/<USER>/.config/Code/User/sync/globalState/preview' failed: No such file or directory
2025-07-16 12:12:25.795 [error] Unable to write to User Settings because print.browser.useAlternate is not a registered configuration.
2025-07-16 12:12:25.921 [error] [Extension Host] Failed to get remote url: Error: Failed to get remote url, no remote found
    at vH.handleGetRemoteUrlRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:1597:492)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:546:3966
    at e.runTimed (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:1890:38780)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:546:3924
2025-07-16 12:12:26.214 [error] [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:310:21581)
    at NL.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:539:4279)
    at NL.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:543:58912)
    at NL.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:543:23301)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:1797:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1/out/extension.js:546:5052
