2025-07-16 12:12:21.648 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:21.659 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:21.673 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:21.702 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:21.957 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:22.186 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:22.259 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:22.316 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:22.371 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:22.416 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:22.469 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:22.632 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:23.906 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:24.391 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:24.407 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:24.596 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:24.610 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:24.865 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:25.750 [info] Getting Manifest... augment.vscode-augment
2025-07-16 12:12:25.750 [info] Getting Manifest... github.copilot
2025-07-16 12:12:25.751 [info] Getting Manifest... github.copilot-chat
2025-07-16 12:12:25.751 [info] Getting Manifest... ms-python.debugpy
2025-07-16 12:12:25.752 [info] Getting Manifest... ms-python.python
2025-07-16 12:12:25.752 [info] Getting Manifest... ms-vscode.cmake-tools
2025-07-16 12:12:25.798 [info] Installing extension: ms-python.debugpy {"productVersion":{"version":"1.102.0","date":"2025-07-09T22:10:34.600Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-16 12:12:25.808 [info] Installing extension: ms-python.python {"productVersion":{"version":"1.102.0","date":"2025-07-09T22:10:34.600Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-16 12:12:25.810 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:25.814 [info] Installing extension: ms-vscode.cmake-tools {"productVersion":{"version":"1.102.0","date":"2025-07-09T22:10:34.600Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-16 12:12:25.814 [info] Installing extension: github.copilot {"productVersion":{"version":"1.102.0","date":"2025-07-09T22:10:34.600Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-16 12:12:25.828 [info] Installing extension: github.copilot-chat {"productVersion":{"version":"1.102.0","date":"2025-07-09T22:10:34.600Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-16 12:12:25.837 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:25.852 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:25.878 [info] Getting Manifest... ms-python.python
2025-07-16 12:12:25.919 [info] Installing extension: augment.vscode-augment {"productVersion":{"version":"1.102.0","date":"2025-07-09T22:10:34.600Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-16 12:12:25.929 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:25.939 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:26.193 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:26.794 [info] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 592ms.
2025-07-16 12:12:26.920 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/github.copilot-chat-0.29.0: github.copilot-chat
2025-07-16 12:12:26.924 [info] Renamed to /home/<USER>/.vscode/extensions/github.copilot-chat-0.29.0
2025-07-16 12:12:26.943 [info] Marked extension as removed github.copilot-chat-0.28.5
2025-07-16 12:12:26.945 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:26.953 [info] Extension installed successfully: github.copilot-chat vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-16 12:12:27.020 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.067 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 357ms.
2025-07-16 12:12:27.133 [info] Extension signature verification result for ms-python.debugpy: Success. Internal Code: 0. Executed: true. Duration: 385ms.
2025-07-16 12:12:27.265 [info] Extension signature verification result for ms-vscode.cmake-tools: Success. Internal Code: 0. Executed: true. Duration: 504ms.
2025-07-16 12:12:27.268 [info] Extension signature verification result for ms-python.python: Success. Internal Code: 0. Executed: true. Duration: 419ms.
2025-07-16 12:12:27.428 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64: ms-python.debugpy
2025-07-16 12:12:27.438 [info] Renamed to /home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64
2025-07-16 12:12:27.458 [info] Marked extension as removed ms-python.debugpy-2025.8.0-linux-x64
2025-07-16 12:12:27.461 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.466 [info] Extension installed successfully: ms-python.debugpy vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-16 12:12:27.468 [info] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 419ms.
2025-07-16 12:12:27.508 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/ms-vscode.cmake-tools-1.21.36: ms-vscode.cmake-tools
2025-07-16 12:12:27.515 [info] Renamed to /home/<USER>/.vscode/extensions/ms-vscode.cmake-tools-1.21.36
2025-07-16 12:12:27.529 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.544 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.546 [info] Marked extension as removed ms-vscode.cmake-tools-1.20.53
2025-07-16 12:12:27.547 [info] Extension installed successfully: ms-vscode.cmake-tools vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-16 12:12:27.612 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.764 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.793 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.828 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/augment.vscode-augment-0.502.1: augment.vscode-augment
2025-07-16 12:12:27.843 [info] Renamed to /home/<USER>/.vscode/extensions/augment.vscode-augment-0.502.1
2025-07-16 12:12:27.850 [info] Marked extension as removed augment.vscode-augment-0.496.1
2025-07-16 12:12:27.853 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.856 [info] Extension installed successfully: augment.vscode-augment vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-16 12:12:27.866 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/github.copilot-1.344.0: github.copilot
2025-07-16 12:12:27.868 [info] Renamed to /home/<USER>/.vscode/extensions/github.copilot-1.344.0
2025-07-16 12:12:27.875 [info] Marked extension as removed github.copilot-1.341.0
2025-07-16 12:12:27.880 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:27.882 [info] Extension installed successfully: github.copilot vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-16 12:12:27.941 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:28.064 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/ms-python.python-2025.10.0-linux-x64: ms-python.python
2025-07-16 12:12:28.097 [info] Renamed to /home/<USER>/.vscode/extensions/ms-python.python-2025.10.0-linux-x64
2025-07-16 12:12:28.107 [info] Marked extension as removed ms-python.python-2025.8.0-linux-x64
2025-07-16 12:12:28.109 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-16 12:12:28.113 [info] Extension installed successfully: ms-python.python vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-16 12:12:28.174 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
