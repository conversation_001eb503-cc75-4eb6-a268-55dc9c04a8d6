2025-07-07 12:31:15.698 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-07 12:31:15.707 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-07 12:31:15.707 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-07 12:31:15.935 [info] Started local extension host with pid 133729.
2025-07-07 12:31:16.400 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:16.545 [info] ComputeTargetPlatform: linux-x64
2025-07-07 12:31:16.546 [error] Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-07 12:31:16.583 [warning] [twxs.cmake]: Cannot register 'cmake.cmakePath'. This property is already registered.
2025-07-07 12:31:18.023 [error] [Extension Host] (node:133729) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `exe --trace-deprecation ...` to show where the warning was created)
2025-07-07 12:31:18.267 [info] Settings Sync: Token updated for the account Bananonymous
2025-07-07 12:31:18.267 [info] Settings Sync: Account status changed from uninitialized to available
2025-07-07 12:31:18.575 [info] [perf] Render performance baseline is 16ms
