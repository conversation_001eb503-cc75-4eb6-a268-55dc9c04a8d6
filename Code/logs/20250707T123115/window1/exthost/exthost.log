2025-07-07 12:31:16.580 [info] Extension host with pid 133729 started
2025-07-07 12:31:16.580 [info] Skipping acquiring lock for /home/<USER>/.config/Code/User/workspaceStorage/d5b7691100371468af1049358fc41669.
2025-07-07 12:31:16.745 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-07 12:31:16.758 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-07 12:31:16.819 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-07 12:31:16.820 [info] ExtensionService#_doActivateExtension tal7aouy.rainbow-bracket, startup: true, activationEvent: '*'
2025-07-07 12:31:16.846 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-07 12:31:16.870 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-07 12:31:16.946 [info] Eager extensions activated
2025-07-07 12:31:16.949 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:16.951 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:16.960 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:17.137 [info] ExtensionService#_doActivateExtension dbaeumer.vscode-eslint, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:17.155 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:17.868 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:18.022 [info] ExtensionService#_doActivateExtension Gruntfuggly.todo-tree, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:18.038 [info] ExtensionService#_doActivateExtension ms-vscode-remote.remote-wsl, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:18.088 [info] ExtensionService#_doActivateExtension pdconsec.vscode-print, startup: false, activationEvent: 'onStartupFinished'
2025-07-07 12:31:18.329 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-07-07 12:31:18.333 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-07-07 12:31:18.358 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-07-07 12:31:19.917 [error] CodeExpectedError: Unable to write to User Settings because print.browser.useAlternate is not a registered configuration.
    at Rvt.z (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:3311:6793)
    at Rvt.F (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:3311:9281)
    at Rvt.o (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:3311:3874)
    at Object.factory (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:3311:3774)
    at pw.j (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:30:75393)
    at vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:30:75305
    at new Promise (<anonymous>)
    at pw.queue (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:30:75247)
    at Rvt.writeConfiguration (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:3311:3743)
    at tin.Hb (vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/workbench/workbench.desktop.main.js:3311:42635)
2025-07-07 12:31:19.980 [info] Extension host terminating: renderer closed the MessagePort
2025-07-07 12:31:19.987 [error] An error occurred when disposing the subscriptions for extension 'Gruntfuggly.todo-tree':
2025-07-07 12:31:19.987 [error] AggregateError: Encountered errors while disposing of store
    at xr (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:822)
    at file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:124:15046
    at file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:25:1056
    at Object.dispose (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:8:705)
    at yY.eb (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:124:11919)
    at file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:124:9874
    at Array.map (<anonymous>)
    at yY.$ (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:124:9862)
    at yY.terminate (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:124:10105)
    at zA.terminate (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:126:914)
    at gs (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:361:5228)
    at MessagePortMain.<anonymous> (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:361:1775)
    at MessagePortMain.emit (node:events:518:28)
    at Object.MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:3073)
2025-07-07 12:31:20.004 [info] Extension host with pid 133729 exiting with code 0
