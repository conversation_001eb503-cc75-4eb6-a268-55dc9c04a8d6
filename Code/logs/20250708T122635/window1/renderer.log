2025-07-08 12:26:35.592 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-08 12:26:35.600 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-08 12:26:35.601 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-08 12:26:35.766 [info] Started local extension host with pid 388868.
2025-07-08 12:26:35.779 [info] ComputeTargetPlatform: linux-x64
2025-07-08 12:26:35.839 [error] Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-08 12:26:35.928 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-07-08 12:26:36.241 [info] Settings Sync: Token updated for the account Bananonymous
2025-07-08 12:26:36.260 [info] Settings Sync: Account status changed from uninitialized to available
2025-07-08 12:26:38.380 [info] [perf] Render performance baseline is 17ms
2025-07-08 12:26:38.560 [info] Auto updating outdated extensions. augment.vscode-augment, github.copilot
2025-07-08 12:26:38.813 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:39.042 [info] Auto updating outdated extensions. augment.vscode-augment, github.copilot
2025-07-08 12:26:40.253 [warning] [twxs.cmake]: Cannot register 'cmake.cmakePath'. This property is already registered.
2025-07-08 12:26:41.495 [error] [Extension Host] (node:388868) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `exe --trace-deprecation ...` to show where the warning was created)
2025-07-08 12:26:42.864 [error] Unable to write to User Settings because print.browser.useAlternate is not a registered configuration.
2025-07-08 12:26:42.999 [error] No registered selector for ID: copilot-chat.fixWithCopilot
2025-07-08 12:26:42.999 [error] No registered selector for ID: copilot-chat.generateCommitMessage
2025-07-08 12:26:43.001 [error] No registered selector for ID: copilot-chat.terminalToDebugging
2025-07-08 12:26:43.001 [error] No registered selector for ID: copilot-chat.terminalToDebuggingSuccess
2025-07-08 12:26:44.851 [error] [Extension Host] Failed to get remote url: Error: Failed to get remote url, no remote found
    at UF.handleGetRemoteUrlRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1583:492)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:3966
    at e.runTimed (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1876:38780)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:3924
2025-07-08 12:26:45.007 [error] [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:293:19295)
    at jR.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:522:4279)
    at jR.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:58674)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23262)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
2025-07-08 12:27:17.781 [error] [Extension Host] Failed to list user repos: Error: HTTP error: 500 Internal Server Error
    at e.fromResponse (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:293:19727)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:12953)
    at jR.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:57845)
    at jR.listGithubReposForAuthenticatedUser (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:24418)
    at UF.handleListUserReposRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1583:4090)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:3966
    at e.runTimed (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1876:38780)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:3924
2025-07-08 12:27:19.752 [error] [Extension Host] Failed to list user repos: Error: HTTP error: 500 Internal Server Error
    at e.fromResponse (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:293:19727)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:12953)
    at jR.callApi (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:57845)
    at jR.listGithubReposForAuthenticatedUser (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:24418)
    at UF.handleListUserReposRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1583:4090)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:3966
    at e.runTimed (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1876:38780)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:3924
2025-07-08 12:28:44.774 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:524:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23364)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
2025-07-08 12:30:44.784 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:524:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23364)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
2025-07-08 12:32:44.794 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:524:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23364)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
2025-07-08 12:34:44.805 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:524:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23364)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
2025-07-08 12:36:44.815 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:524:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23364)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
2025-07-08 12:38:44.824 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:524:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23364)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
2025-07-08 12:40:44.835 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:524:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23364)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
2025-07-08 12:42:44.845 [error] [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:524:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at jR.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:526:23364)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:1783:22423)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2/out/extension.js:529:5052
