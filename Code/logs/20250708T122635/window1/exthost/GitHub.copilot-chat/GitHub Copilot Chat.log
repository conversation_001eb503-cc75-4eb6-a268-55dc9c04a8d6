2025-07-08 12:26:41.983 [info] Using the Electron fetcher.
2025-07-08 12:26:41.983 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-07-08 12:26:41.983 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-07-08 12:26:41.983 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-07-08 12:26:41.983 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-07-08 12:26:42.212 [info] Logged in as Bananonymous
2025-07-08 12:26:42.989 [info] Got Copilot token for Bananonymous
2025-07-08 12:26:42.994 [info] activationBlocker from 'languageModelAccess' took for 1262ms
2025-07-08 12:26:43.007 [info] copilot token chat_enabled: true, sku: free_educational_quota
2025-07-08 12:26:43.007 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-07-08 12:26:43.017 [info] Registering default platform agent...
2025-07-08 12:26:43.017 [info] activationBlocker from 'conversationFeature' took for 1286ms
2025-07-08 12:26:43.024 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-07-08 12:26:43.382 [info] Fetched model metadata in 388ms ccb6719c-88e4-4831-922d-16fde546a4b0
