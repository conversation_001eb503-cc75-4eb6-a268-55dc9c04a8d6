2025-07-08 12:26:35.923 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:35.939 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:35.946 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:35.947 [info] ComputeTargetPlatform: linux-x64
2025-07-08 12:26:35.993 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.018 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.069 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.123 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.173 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.194 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.234 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.259 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.624 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:36.635 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:37.058 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:38.217 [info] ComputeTargetPlatform: linux-x64
2025-07-08 12:26:38.228 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:38.879 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:38.890 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:40.044 [info] Getting Manifest... augment.vscode-augment
2025-07-08 12:26:40.045 [info] Getting Manifest... github.copilot
2025-07-08 12:26:40.096 [info] Installing extension: github.copilot {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-08 12:26:40.107 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:40.124 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:40.274 [info] Installing extension: augment.vscode-augment {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-08 12:26:40.287 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:40.300 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:41.039 [info] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 358ms.
2025-07-08 12:26:41.172 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 342ms.
2025-07-08 12:26:41.264 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/github.copilot-1.341.0: github.copilot
2025-07-08 12:26:41.271 [info] Renamed to /home/<USER>/.vscode/extensions/github.copilot-1.341.0
2025-07-08 12:26:41.277 [info] Marked extension as removed github.copilot-1.338.0
2025-07-08 12:26:41.285 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:41.289 [info] Extension installed successfully: github.copilot vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-08 12:26:41.353 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:41.563 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1: augment.vscode-augment
2025-07-08 12:26:41.579 [info] Renamed to /home/<USER>/.vscode/extensions/augment.vscode-augment-0.496.1
2025-07-08 12:26:41.591 [info] Marked extension as removed augment.vscode-augment-0.492.2
2025-07-08 12:26:41.595 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:41.600 [info] Extension installed successfully: augment.vscode-augment vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-08 12:26:41.670 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:43.594 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:26:44.002 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:27:09.822 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:28:53.679 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:29:17.904 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:32:27.082 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:33:21.206 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:38:21.401 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-08 12:43:21.571 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
