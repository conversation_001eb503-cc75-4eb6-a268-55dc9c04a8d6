2025-06-25 05:03:40.670 [info] ComputeTargetPlatform: linux-x64
2025-06-25 05:03:40.687 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:40.709 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:40.712 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:40.764 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:40.863 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:40.905 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.052 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.145 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.221 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.284 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.321 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.355 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.637 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.775 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:41.781 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:42.268 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:42.978 [info] ComputeTargetPlatform: linux-x64
2025-06-25 05:03:42.992 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:43.662 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:43.675 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:44.768 [info] Getting Manifest... github.copilot-chat
2025-06-25 05:03:44.909 [info] Installing extension: github.copilot-chat {"productVersion":{"version":"1.101.1","date":"2025-06-18T13:35:12.605Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-06-25 05:03:44.920 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:44.932 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:45.286 [info] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 326ms.
2025-06-25 05:03:45.388 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/github.copilot-chat-0.28.2: github.copilot-chat
2025-06-25 05:03:45.391 [info] Renamed to /home/<USER>/.vscode/extensions/github.copilot-chat-0.28.2
2025-06-25 05:03:45.400 [info] Marked extension as removed github.copilot-chat-0.28.1
2025-06-25 05:03:45.406 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:03:45.410 [info] Extension installed successfully: github.copilot-chat vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-06-25 05:03:45.471 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:04:01.318 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-06-25 05:04:01.342 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
