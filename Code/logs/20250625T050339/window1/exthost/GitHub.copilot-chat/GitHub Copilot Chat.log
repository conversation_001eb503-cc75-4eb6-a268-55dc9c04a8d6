2025-06-25 05:03:41.477 [info] Using the Electron fetcher.
2025-06-25 05:03:41.477 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-25 05:03:41.477 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-25 05:03:41.477 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-25 05:03:41.477 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-25 05:03:41.495 [info] Logged in as Bananonymous
2025-06-25 05:03:43.322 [info] Got Copilot token for Bananonymous
2025-06-25 05:03:43.328 [info] activationBlocker from 'languageModelAccess' took for 1936ms
2025-06-25 05:03:43.727 [info] copilot token chat_enabled: true, sku: free_educational_quota
2025-06-25 05:03:43.727 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-06-25 05:03:43.735 [info] Registering default platform agent...
2025-06-25 05:03:43.735 [info] activationBlocker from 'conversationFeature' took for 2345ms
2025-06-25 05:03:43.743 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-06-25 05:03:43.769 [info] Fetched model metadata in 441ms 3dcaa0a6-fea3-4661-8fce-9d33810b2d2e
