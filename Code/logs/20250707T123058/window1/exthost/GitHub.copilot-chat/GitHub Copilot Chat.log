2025-07-07 12:31:02.175 [info] Using the Electron fetcher.
2025-07-07 12:31:02.175 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-07-07 12:31:02.175 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-07-07 12:31:02.175 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-07-07 12:31:02.175 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-07-07 12:31:02.575 [info] Logged in as Bananonymous
2025-07-07 12:31:04.201 [info] Got Copilot token for Bananonymous
2025-07-07 12:31:04.207 [info] activationBlocker from 'languageModelAccess' took for 2347ms
2025-07-07 12:31:04.295 [info] copilot token chat_enabled: true, sku: free_educational_quota
2025-07-07 12:31:04.295 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-07-07 12:31:04.303 [info] Registering default platform agent...
2025-07-07 12:31:04.303 [info] activationBlocker from 'conversationFeature' took for 2445ms
2025-07-07 12:31:04.318 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-07-07 12:31:04.580 [info] Fetched model metadata in 374ms bdfd6d20-e816-4410-9dac-0892e9155a3e
