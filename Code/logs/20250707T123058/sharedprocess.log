2025-07-07 12:30:59.658 [info] ComputeTargetPlatform: linux-x64
2025-07-07 12:30:59.673 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:30:59.695 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:30:59.699 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:30:59.765 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:30:59.992 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:00.038 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:00.194 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:00.278 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:00.332 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:00.377 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:00.424 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:00.458 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:00.620 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:01.978 [info] ComputeTargetPlatform: linux-x64
2025-07-07 12:31:01.990 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:02.014 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:02.055 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:02.645 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:02.656 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:03.796 [info] Getting Manifest... pdconsec.vscode-print
2025-07-07 12:31:03.843 [info] Installing extension: pdconsec.vscode-print {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-07 12:31:03.852 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:03.868 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:04.525 [info] Extension signature verification result for pdconsec.vscode-print: Success. Internal Code: 0. Executed: true. Duration: 343ms.
2025-07-07 12:31:04.667 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/pdconsec.vscode-print-1.5.1: pdconsec.vscode-print
2025-07-07 12:31:04.674 [info] Renamed to /home/<USER>/.vscode/extensions/pdconsec.vscode-print-1.5.1
2025-07-07 12:31:04.676 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:04.681 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:04.682 [info] Marked extension as removed pdconsec.vscode-print-1.4.1
2025-07-07 12:31:04.689 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:31:04.692 [info] Extension installed successfully: pdconsec.vscode-print vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-07 12:31:04.754 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
