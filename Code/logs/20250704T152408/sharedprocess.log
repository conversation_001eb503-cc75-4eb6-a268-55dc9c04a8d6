2025-07-04 15:24:09.849 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:09.868 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:09.870 [info] ComputeTargetPlatform: linux-x64
2025-07-04 15:24:09.874 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:09.894 [info] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode/extensions/github.copilot-chat-0.28.1
2025-07-04 15:24:09.900 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:09.910 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.176 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.205 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.393 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.490 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.546 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.587 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.633 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.685 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.795 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:12.146 [info] ComputeTargetPlatform: linux-x64
2025-07-04 15:24:12.159 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:12.501 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:12.508 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:12.811 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:12.827 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:12.992 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:13.969 [info] Getting Manifest... augment.vscode-augment
2025-07-04 15:24:13.970 [info] Getting Manifest... github.copilot
2025-07-04 15:24:13.972 [info] Getting Manifest... github.copilot-chat
2025-07-04 15:24:13.972 [info] Getting Manifest... ms-python.vscode-pylance
2025-07-04 15:24:13.973 [info] Getting Manifest... ms-vscode.cpptools
2025-07-04 15:24:14.025 [info] Installing extension: augment.vscode-augment {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-04 15:24:14.036 [info] Installing extension: github.copilot {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-04 15:24:14.038 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:14.040 [info] Installing extension: github.copilot-chat {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-04 15:24:14.060 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:14.128 [info] Installing extension: ms-vscode.cpptools {"installPreReleaseVersion":true,"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-04 15:24:14.137 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:14.154 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:14.194 [info] Installing extension: ms-python.vscode-pylance {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
2025-07-04 15:24:14.204 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:14.275 [info] Getting Manifest... ms-python.python
2025-07-04 15:24:14.473 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:14.988 [info] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 594ms.
2025-07-04 15:24:15.112 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/github.copilot-chat-0.28.5: github.copilot-chat
2025-07-04 15:24:15.116 [info] Renamed to /home/<USER>/.vscode/extensions/github.copilot-chat-0.28.5
2025-07-04 15:24:15.134 [info] Marked extension as removed github.copilot-chat-0.28.2
2025-07-04 15:24:15.136 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:15.145 [info] Extension installed successfully: github.copilot-chat vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-04 15:24:15.217 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:15.552 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 341ms.
2025-07-04 15:24:15.778 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2: augment.vscode-augment
2025-07-04 15:24:15.784 [info] Renamed to /home/<USER>/.vscode/extensions/augment.vscode-augment-0.492.2
2025-07-04 15:24:15.790 [info] Marked extension as removed augment.vscode-augment-0.487.1
2025-07-04 15:24:15.796 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:15.801 [info] Extension installed successfully: augment.vscode-augment vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-04 15:24:15.822 [info] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 366ms.
2025-07-04 15:24:15.865 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:16.094 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/github.copilot-1.338.0: github.copilot
2025-07-04 15:24:16.098 [info] Renamed to /home/<USER>/.vscode/extensions/github.copilot-1.338.0
2025-07-04 15:24:16.108 [info] Marked extension as removed github.copilot-1.336.0
2025-07-04 15:24:16.119 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:16.126 [info] Extension installed successfully: github.copilot vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-04 15:24:16.195 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:16.468 [info] Extension signature verification result for ms-python.vscode-pylance: Success. Internal Code: 0. Executed: true. Duration: 366ms.
2025-07-04 15:24:17.531 [info] Extension signature verification result for ms-vscode.cpptools: Success. Internal Code: 0. Executed: true. Duration: 439ms.
2025-07-04 15:24:18.203 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/ms-python.vscode-pylance-2025.6.2: ms-python.vscode-pylance
2025-07-04 15:24:18.289 [info] Renamed to /home/<USER>/.vscode/extensions/ms-python.vscode-pylance-2025.6.2
2025-07-04 15:24:18.301 [info] Marked extension as removed ms-python.vscode-pylance-2025.6.1
2025-07-04 15:24:18.302 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:18.306 [info] Extension installed successfully: ms-python.vscode-pylance vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-04 15:24:18.366 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:18.842 [info] Extracted extension to file:///home/<USER>/.vscode/extensions/ms-vscode.cpptools-1.26.3-linux-x64: ms-vscode.cpptools
2025-07-04 15:24:18.852 [info] Renamed to /home/<USER>/.vscode/extensions/ms-vscode.cpptools-1.26.3-linux-x64
2025-07-04 15:24:18.866 [info] Marked extension as removed ms-vscode.cpptools-1.26.2-linux-x64
2025-07-04 15:24:18.868 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:18.873 [info] Extension installed successfully: ms-vscode.cpptools vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json
2025-07-04 15:24:18.935 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
