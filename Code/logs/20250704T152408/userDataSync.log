2025-07-04 15:24:09.806 [info] [AutoSync] Using settings sync service https://vscode-sync.trafficmanager.net/
2025-07-04 15:24:09.806 [info] [AutoSync] Enabled.
2025-07-04 15:24:09.806 [info] [AutoSync] Suspended until auth token is available.
2025-07-04 15:24:12.083 [info] [AutoSync] Triggered by Interval
2025-07-04 15:24:12.083 [info] Sync started.
2025-07-04 15:24:12.517 [info] Settings: No changes found during synchronizing settings.
2025-07-04 15:24:12.526 [info] Keybindings: No changes found during synchronizing keybindings.
2025-07-04 15:24:12.531 [info] Snippets: No changes found during synchronizing snippets.
2025-07-04 15:24:12.532 [info] Tasks: No changes found during synchronizing tasks.
2025-07-04 15:24:12.976 [info] GlobalState: Updated remote ui state. Updated: cpp.1.lastSessionDate,cpp.1.sessionCount,java.2.lastSessionDate,java.2.sessionCount,typescript.1.lastSessionDate,typescript.1.sessionCount,csharp.1.lastSessionDate,csharp.1.sessionCount,extension.features.state.
2025-07-04 15:24:12.978 [info] GlobalState: Updated last synchronized ui state
2025-07-04 15:24:13.000 [info] Extensions: No changes found during synchronizing extensions.
2025-07-04 15:24:13.001 [info] Prompts: No changes found during synchronizing prompts.
2025-07-04 15:24:13.002 [info] Profiles: No changes found during synchronizing profiles.
2025-07-04 15:24:13.002 [info] Sync done. Took 920ms
