2025-07-04 15:24:12.438 [info] Using the Electron fetcher.
2025-07-04 15:24:12.438 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-07-04 15:24:12.438 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-07-04 15:24:12.438 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-07-04 15:24:12.438 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-07-04 15:24:12.989 [info] Logged in as Bananonymous
2025-07-04 15:24:14.787 [info] Got Copilot token for Bananonymous
2025-07-04 15:24:14.793 [info] activationBlocker from 'languageModelAccess' took for 2685ms
2025-07-04 15:24:14.903 [info] copilot token chat_enabled: true, sku: free_educational_quota
2025-07-04 15:24:14.903 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-07-04 15:24:14.912 [info] Registering default platform agent...
2025-07-04 15:24:14.912 [info] activationBlocker from 'conversationFeature' took for 2806ms
2025-07-04 15:24:14.954 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-07-04 15:24:15.224 [info] Fetched model metadata in 431ms f3477636-a76b-4224-b35c-770bf5e8dad9
