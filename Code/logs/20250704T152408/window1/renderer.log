2025-07-04 15:24:09.398 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-04 15:24:09.407 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-04 15:24:09.407 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-04 15:24:09.643 [info] Started local extension host with pid 394455.
2025-07-04 15:24:10.123 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-04 15:24:10.263 [info] ComputeTargetPlatform: linux-x64
2025-07-04 15:24:10.264 [error] Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-04 15:24:10.314 [warning] [twxs.cmake]: Cannot register 'cmake.cmakePath'. This property is already registered.
2025-07-04 15:24:11.816 [error] [Extension Host] (node:394455) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `exe --trace-deprecation ...` to show where the warning was created)
2025-07-04 15:24:12.081 [info] Settings Sync: Token updated for the account Bananonymous
2025-07-04 15:24:12.082 [info] Settings Sync: Account status changed from uninitialized to available
2025-07-04 15:24:12.487 [info] [perf] Render performance baseline is 12ms
2025-07-04 15:24:12.506 [info] Auto updating outdated extensions. augment.vscode-augment, github.copilot, github.copilot-chat, ms-python.vscode-pylance, ms-vscode.cpptools
2025-07-04 15:24:12.966 [info] Auto updating outdated extensions. augment.vscode-augment, github.copilot, github.copilot-chat, ms-python.vscode-pylance, ms-vscode.cpptools
2025-07-04 15:24:13.708 [error] Unable to write to User Settings because print.browser.useAlternate is not a registered configuration.
2025-07-04 15:24:14.055 [error] [Extension Host] Failed to get remote url: Error: Failed to get remote url, no remote found
    at eN.handleGetRemoteUrlRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1574:492)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:3966
    at e.runTimed (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1864:38780)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:3924
2025-07-04 15:24:14.741 [error] [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:293:13968)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:518:4279)
    at wM.callApiStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (/home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:1771:21995)
    at /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/out/extension.js:527:5052
