2025-07-07 12:28:06.720 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-07 12:28:06.729 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-07 12:28:06.729 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-07 12:28:06.962 [info] Started local extension host with pid 124075.
2025-07-07 12:28:07.459 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.618 [info] ComputeTargetPlatform: linux-x64
2025-07-07 12:28:07.619 [error] Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-07 12:28:07.659 [warning] [twxs.cmake]: Cannot register 'cmake.cmakePath'. This property is already registered.
2025-07-07 12:28:09.256 [error] [Extension Host] (node:124075) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `exe --trace-deprecation ...` to show where the warning was created)
2025-07-07 12:28:09.543 [info] Settings Sync: Token updated for the account Bananonymous
2025-07-07 12:28:09.549 [info] Settings Sync: Account status changed from uninitialized to available
2025-07-07 12:28:09.622 [info] [perf] Render performance baseline is 12ms
2025-07-07 12:28:09.867 [info] Auto updating outdated extensions. pdconsec.vscode-print
2025-07-07 12:28:10.370 [info] Auto updating outdated extensions. pdconsec.vscode-print
2025-07-07 12:28:11.151 [error] Unable to write to User Settings because print.browser.useAlternate is not a registered configuration.
