2025-07-07 12:28:07.168 [info] ComputeTargetPlatform: linux-x64
2025-07-07 12:28:07.195 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.227 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.230 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.277 [info] Deleted marked for removal extension from disk augment.vscode-augment /home/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1
2025-07-07 12:28:07.330 [info] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode/extensions/github.copilot-chat-0.28.2
2025-07-07 12:28:07.334 [info] Deleted marked for removal extension from disk ms-vscode.cpptools /home/<USER>/.vscode/extensions/ms-vscode.cpptools-1.26.2-linux-x64
2025-07-07 12:28:07.350 [info] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode/extensions/github.copilot-1.336.0
2025-07-07 12:28:07.351 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.362 [info] Deleted marked for removal extension from disk ms-python.vscode-pylance /home/<USER>/.vscode/extensions/ms-python.vscode-pylance-2025.6.1
2025-07-07 12:28:07.372 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.522 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.552 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.738 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.829 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.870 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.922 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:07.973 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:08.036 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:08.190 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:09.451 [info] ComputeTargetPlatform: linux-x64
2025-07-07 12:28:09.462 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:10.009 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:10.026 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:10.155 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:10.171 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:10.610 [warning] [vscode-print]: Couldn't find message for key print.alternateBrowser.title.
2025-07-07 12:28:11.372 [info] Getting Manifest... pdconsec.vscode-print
2025-07-07 12:28:11.482 [info] Installing extension: pdconsec.vscode-print {"productVersion":{"version":"1.101.2","date":"2025-06-24T20:27:15.391Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"profileLocation":{"$mid":1,"external":"vscode-userdata:/home/<USER>/.vscode/extensions/extensions.json","path":"/home/<USER>/.vscode/extensions/extensions.json","scheme":"vscode-userdata"}}
