{"ref": "0c06d809-1f20-4d0b-8a0e-96ed71d87421", "syncData": {"version": 1, "machineId": "c948ff01-eb06-4430-b927-5dd4cafd5f19", "content": "{\"storage\":{\"workbench.panel.markers.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\"},\"workbench.panel.output.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\"},\"terminal.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\"},\"workbench.explorer.views.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.classroom.activeAssignment\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.session.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"copilot-chat-requests\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.session\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.help\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.devtools\\\",\\\"isHidden\\\":false}]\"},\"workbench.scm.views.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false}]\"},\"workbench.view.search.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\"},\"workbench.activityBar.location\":{\"version\":1,\"value\":\"default\"},\"workbench.activity.pinnedViewlets2\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.copilot-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.cmake-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.github-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.github-pull-request\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.hexExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.makefile__viewContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.todo-tree-container\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.augment-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.chatSidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"userDataProfiles\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.sync\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.editSessions\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\"},\"workbench.panel.pinnedPanels\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.augment-panel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\"},\"workbench.panel.repl.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\"},\"workbench.view.debug.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"CppSshTargetsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false}]\"},\"colorThemeData\":{\"version\":1,\"value\":\"{\\\"id\\\":\\\"vs-dark akamud-vscode-theme-onedark-themes-OneDark-json\\\",\\\"label\\\":\\\"Atom One Dark\\\",\\\"settingsId\\\":\\\"Atom One Dark\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"foreground\\\":\\\"#5C6370\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"comment\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5C6370\\\"},\\\"scope\\\":[\\\"comment markup.link\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"entity.name.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"entity.other.inherited-class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"keyword\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"keyword.control\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"keyword.other.special-method\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"keyword.other.unit\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"storage\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"storage.type.annotation\\\",\\\"storage.type.primitive\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"storage.modifier.package\\\",\\\"storage.modifier.import\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"constant\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"constant.variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"constant.character.escape\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"constant.numeric\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"constant.other.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"constant.other.symbol\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BE5046\\\"},\\\"scope\\\":[\\\"variable.interpolation\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"variable.parameter\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":[\\\"string\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"string > source\\\",\\\"string embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"string.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"string.regexp source.ruby.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"string.other.link\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5C6370\\\"},\\\"scope\\\":[\\\"punctuation.definition.comment\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"punctuation.definition.method-parameters\\\",\\\"punctuation.definition.function-parameters\\\",\\\"punctuation.definition.parameters\\\",\\\"punctuation.definition.separator\\\",\\\"punctuation.definition.seperator\\\",\\\"punctuation.definition.array\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"punctuation.definition.heading\\\",\\\"punctuation.definition.identity\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"punctuation.definition.bold\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"punctuation.definition.italic\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BE5046\\\"},\\\"scope\\\":[\\\"punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"punctuation.section.method\\\",\\\"punctuation.section.class\\\",\\\"punctuation.section.inner-class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"support.class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"support.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"support.function\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"support.function.any-method\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"entity.name.function\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"entity.name.class\\\",\\\"entity.name.type.class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"entity.name.section\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"entity.name.tag\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.id\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"meta.class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"meta.class.body\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"meta.method-call\\\",\\\"meta.method\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"meta.definition.variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"meta.link\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"meta.require\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"meta.selector\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"meta.separator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"meta.tag\\\"]},{\\\"settings\\\":{\\\"text-decoration\\\":\\\"underline\\\"},\\\"scope\\\":[\\\"underline\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"none\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#523D14\\\",\\\"background\\\":\\\"#E0C285\\\"},\\\"scope\\\":[\\\"invalid.deprecated\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"white\\\",\\\"background\\\":\\\"#E05252\\\"},\\\"scope\\\":[\\\"invalid.illegal\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"markup.bold\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"markup.changed\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"markup.deleted\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"markup.italic\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"markup.heading\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"markup.heading punctuation.definition.heading\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"markup.link\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":[\\\"markup.inserted\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"markup.quote\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":[\\\"markup.raw\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"source.c keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"source.cpp keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"source.cs keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#828997\\\"},\\\"scope\\\":[\\\"source.css property-name\\\",\\\"source.css property-value\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.css property-name.support\\\",\\\"source.css property-value.support\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.elixir source.embedded.source\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"source.elixir constant.language\\\",\\\"source.elixir constant.numeric\\\",\\\"source.elixir constant.definition\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"source.elixir variable.definition\\\",\\\"source.elixir variable.anonymous\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"source.elixir parameter.variable.function\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":[\\\"source.elixir quoted\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"source.elixir keyword.special-method\\\",\\\"source.elixir embedded.section\\\",\\\"source.elixir embedded.source.empty\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"source.elixir readwrite.module punctuation\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BE5046\\\"},\\\"scope\\\":[\\\"source.elixir regexp.section\\\",\\\"source.elixir regexp.string\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"source.elixir separator\\\",\\\"source.elixir keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"source.elixir variable.constant\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#828997\\\"},\\\"scope\\\":[\\\"source.elixir array\\\",\\\"source.elixir scope\\\",\\\"source.elixir section\\\"]},{\\\"settings\\\":{\\\"-webkit-font-smoothing\\\":\\\"auto\\\"},\\\"scope\\\":[\\\"source.gfm markup\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"source.gfm link entity\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"source.go storage.type.string\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"source.ini keyword.other.definition.ini\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"source.java storage.modifier.import\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"source.java storage.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"source.java keyword.operator.instanceof\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"source.java-properties meta.key-pair\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.java-properties meta.key-pair > punctuation\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"source.js keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"source.js keyword.operator.delete\\\",\\\"source.js keyword.operator.in\\\",\\\"source.js keyword.operator.of\\\",\\\"source.js keyword.operator.instanceof\\\",\\\"source.js keyword.operator.new\\\",\\\"source.js keyword.operator.typeof\\\",\\\"source.js keyword.operator.void\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"source.ts keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"source.flow keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"source.json meta.structure.dictionary.json > string.quoted.json\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"source.json meta.structure.dictionary.json > string.quoted.json > punctuation.string\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":[\\\"source.json meta.structure.dictionary.json > value.json > string.quoted.json\\\",\\\"source.json meta.structure.array.json > value.json > string.quoted.json\\\",\\\"source.json meta.structure.dictionary.json > value.json > string.quoted.json > punctuation\\\",\\\"source.json meta.structure.array.json > value.json > string.quoted.json > punctuation\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"source.json meta.structure.dictionary.json > constant.language.json\\\",\\\"source.json meta.structure.array.json > constant.language.json\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"ng.interpolation\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"ng.interpolation.begin\\\",\\\"ng.interpolation.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"ng.interpolation function\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"ng.interpolation function.begin\\\",\\\"ng.interpolation function.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"ng.interpolation bool\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"ng.interpolation bracket\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"ng.pipe\\\",\\\"ng.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"ng.tag\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"ng.attribute-with-value attribute-name\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"ng.attribute-with-value string\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"ng.attribute-with-value string.begin\\\",\\\"ng.attribute-with-value string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"inherit\\\"},\\\"scope\\\":[\\\"source.ruby constant.other.symbol > punctuation\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.php class.bracket\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"source.python keyword.operator.logical.python\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"source.python variable.parameter\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"customrule\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"support.type.property-name\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":\\\"string.quoted.double punctuation\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"support.constant\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"support.type.property-name.json\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"support.type.property-name.json punctuation\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"punctuation.separator.key-value.ts\\\",\\\"punctuation.separator.key-value.js\\\",\\\"punctuation.separator.key-value.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"source.js.embedded.html keyword.operator\\\",\\\"source.ts.embedded.html keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"variable.other.readwrite.js\\\",\\\"variable.other.readwrite.ts\\\",\\\"variable.other.readwrite.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"support.variable.dom.js\\\",\\\"support.variable.dom.ts\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"support.variable.property.dom.js\\\",\\\"support.variable.property.dom.ts\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BE5046\\\"},\\\"scope\\\":[\\\"meta.template.expression.js punctuation.definition\\\",\\\"meta.template.expression.ts punctuation.definition\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.ts punctuation.definition.typeparameters\\\",\\\"source.js punctuation.definition.typeparameters\\\",\\\"source.tsx punctuation.definition.typeparameters\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.ts punctuation.definition.block\\\",\\\"source.js punctuation.definition.block\\\",\\\"source.tsx punctuation.definition.block\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.ts punctuation.separator.comma\\\",\\\"source.js punctuation.separator.comma\\\",\\\"source.tsx punctuation.separator.comma\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"support.variable.property.js\\\",\\\"support.variable.property.ts\\\",\\\"support.variable.property.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"keyword.control.default.js\\\",\\\"keyword.control.default.ts\\\",\\\"keyword.control.default.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"keyword.operator.expression.instanceof.js\\\",\\\"keyword.operator.expression.instanceof.ts\\\",\\\"keyword.operator.expression.instanceof.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"keyword.operator.expression.of.js\\\",\\\"keyword.operator.expression.of.ts\\\",\\\"keyword.operator.expression.of.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"meta.brace.round.js\\\",\\\"meta.array-binding-pattern-variable.js\\\",\\\"meta.brace.square.js\\\",\\\"meta.brace.round.ts\\\",\\\"meta.array-binding-pattern-variable.ts\\\",\\\"meta.brace.square.ts\\\",\\\"meta.brace.round.tsx\\\",\\\"meta.array-binding-pattern-variable.tsx\\\",\\\"meta.brace.square.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.js punctuation.accessor\\\",\\\"source.ts punctuation.accessor\\\",\\\"source.tsx punctuation.accessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"punctuation.terminator.statement.js\\\",\\\"punctuation.terminator.statement.ts\\\",\\\"punctuation.terminator.statement.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"meta.array-binding-pattern-variable.js variable.other.readwrite.js\\\",\\\"meta.array-binding-pattern-variable.ts variable.other.readwrite.ts\\\",\\\"meta.array-binding-pattern-variable.tsx variable.other.readwrite.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"source.js support.variable\\\",\\\"source.ts support.variable\\\",\\\"source.tsx support.variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"variable.other.constant.property.js\\\",\\\"variable.other.constant.property.ts\\\",\\\"variable.other.constant.property.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"keyword.operator.new.ts\\\",\\\"keyword.operator.new.j\\\",\\\"keyword.operator.new.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"source.ts keyword.operator\\\",\\\"source.tsx keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"punctuation.separator.parameter.js\\\",\\\"punctuation.separator.parameter.ts\\\",\\\"punctuation.separator.parameter.tsx \\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"constant.language.import-export-all.js\\\",\\\"constant.language.import-export-all.ts\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"constant.language.import-export-all.jsx\\\",\\\"constant.language.import-export-all.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"keyword.control.as.js\\\",\\\"keyword.control.as.ts\\\",\\\"keyword.control.as.jsx\\\",\\\"keyword.control.as.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"variable.other.readwrite.alias.js\\\",\\\"variable.other.readwrite.alias.ts\\\",\\\"variable.other.readwrite.alias.jsx\\\",\\\"variable.other.readwrite.alias.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"variable.other.constant.js\\\",\\\"variable.other.constant.ts\\\",\\\"variable.other.constant.jsx\\\",\\\"variable.other.constant.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"meta.export.default.js variable.other.readwrite.js\\\",\\\"meta.export.default.ts variable.other.readwrite.ts\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":[\\\"source.js meta.template.expression.js punctuation.accessor\\\",\\\"source.ts meta.template.expression.ts punctuation.accessor\\\",\\\"source.tsx meta.template.expression.tsx punctuation.accessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.js meta.import-equals.external.js keyword.operator\\\",\\\"source.jsx meta.import-equals.external.jsx keyword.operator\\\",\\\"source.ts meta.import-equals.external.ts keyword.operator\\\",\\\"source.tsx meta.import-equals.external.tsx keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":\\\"entity.name.type.module.js,entity.name.type.module.ts,entity.name.type.module.jsx,entity.name.type.module.tsx\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"meta.class.js,meta.class.ts,meta.class.jsx,meta.class.tsx\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"meta.definition.property.js variable\\\",\\\"meta.definition.property.ts variable\\\",\\\"meta.definition.property.jsx variable\\\",\\\"meta.definition.property.tsx variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"meta.type.parameters.js support.type\\\",\\\"meta.type.parameters.jsx support.type\\\",\\\"meta.type.parameters.ts support.type\\\",\\\"meta.type.parameters.tsx support.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"source.js meta.tag.js keyword.operator\\\",\\\"source.jsx meta.tag.jsx keyword.operator\\\",\\\"source.ts meta.tag.ts keyword.operator\\\",\\\"source.tsx meta.tag.tsx keyword.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"meta.tag.js punctuation.section.embedded\\\",\\\"meta.tag.jsx punctuation.section.embedded\\\",\\\"meta.tag.ts punctuation.section.embedded\\\",\\\"meta.tag.tsx punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":[\\\"meta.array.literal.js variable\\\",\\\"meta.array.literal.jsx variable\\\",\\\"meta.array.literal.ts variable\\\",\\\"meta.array.literal.tsx variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"support.type.object.module.js\\\",\\\"support.type.object.module.jsx\\\",\\\"support.type.object.module.ts\\\",\\\"support.type.object.module.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"constant.language.json\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":[\\\"variable.other.constant.object.js\\\",\\\"variable.other.constant.object.jsx\\\",\\\"variable.other.constant.object.ts\\\",\\\"variable.other.constant.object.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":[\\\"storage.type.property.js\\\",\\\"storage.type.property.jsx\\\",\\\"storage.type.property.ts\\\",\\\"storage.type.property.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":[\\\"meta.template.expression.js string.quoted punctuation.definition\\\",\\\"meta.template.expression.jsx string.quoted punctuation.definition\\\",\\\"meta.template.expression.ts string.quoted punctuation.definition\\\",\\\"meta.template.expression.tsx string.quoted punctuation.definition\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":[\\\"meta.template.expression.js string.template punctuation.definition.string.template\\\",\\\"meta.template.expression.jsx string.template punctuation.definition.string.template\\\",\\\"meta.template.expression.ts string.template punctuation.definition.string.template\\\",\\\"meta.template.expression.tsx string.template punctuation.definition.string.template\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"keyword.operator.expression.in.js\\\",\\\"keyword.operator.expression.in.jsx\\\",\\\"keyword.operator.expression.in.ts\\\",\\\"keyword.operator.expression.in.tsx\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"variable.other.object.js\\\",\\\"variable.other.object.ts\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":[\\\"meta.object-literal.key.js\\\",\\\"meta.object-literal.key.ts\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"source.python constant.other\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"source.python constant\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"constant.character.format.placeholder.other.python storage\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"support.variable.magic.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"meta.function.parameters.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"punctuation.separator.annotation.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"punctuation.separator.parameters.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"entity.name.variable.field.cs\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"source.cs keyword.operator\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"variable.other.readwrite.cs\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"variable.other.object.cs\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"variable.other.object.property.cs\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":\\\"entity.name.variable.property.cs\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":\\\"storage.type.cs\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":\\\"keyword.other.unsafe.rust\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":\\\"entity.name.type.rust\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"storage.modifier.lifetime.rust\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"entity.name.lifetime.rust\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":\\\"storage.type.core.rust\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"meta.attribute.rust\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#56B6C2\\\"},\\\"scope\\\":\\\"storage.class.std.rust\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"markup.raw.block.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"punctuation.definition.variable.shell\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"support.constant.property-value.css\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"punctuation.definition.constant.css\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"punctuation.separator.key-value.scss\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"punctuation.definition.constant.scss\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"meta.property-list.scss punctuation.separator.key-value.scss\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E5C07B\\\"},\\\"scope\\\":\\\"storage.type.primitive.array.java\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"entity.name.section.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"punctuation.definition.heading.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"markup.heading.setext\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"punctuation.definition.bold.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":\\\"markup.inline.raw.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"beginning.punctuation.definition.list.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5C6370\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"markup.quote.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":[\\\"punctuation.definition.string.begin.markdown\\\",\\\"punctuation.definition.string.end.markdown\\\",\\\"punctuation.definition.metadata.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":\\\"punctuation.definition.metadata.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C678DD\\\"},\\\"scope\\\":[\\\"markup.underline.link.markdown\\\",\\\"markup.underline.link.image.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#61AFEF\\\"},\\\"scope\\\":[\\\"string.other.link.title.markdown\\\",\\\"string.other.link.description.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"punctuation.separator.variable.ruby\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#D19A66\\\"},\\\"scope\\\":\\\"variable.other.constant.ruby\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#98C379\\\"},\\\"scope\\\":\\\"keyword.operator.other.ruby\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E06C75\\\"},\\\"scope\\\":\\\"punctuation.definition.variable.php\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ABB2BF\\\"},\\\"scope\\\":\\\"meta.class.php\\\"}],\\\"semanticTokenRules\\\":[],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"akamud.vscode-theme-onedark\\\",\\\"_extensionIsBuiltin\\\":false,\\\"_extensionName\\\":\\\"vscode-theme-onedark\\\",\\\"_extensionPublisher\\\":\\\"akamud\\\"},\\\"colorMap\\\":{\\\"activityBar.background\\\":\\\"#333842\\\",\\\"activityBar.foreground\\\":\\\"#d7dae0\\\",\\\"editorInlayHint.background\\\":\\\"#2c313a\\\",\\\"editorInlayHint.foreground\\\":\\\"#636e83\\\",\\\"notebook.cellEditorBackground\\\":\\\"#2c313a\\\",\\\"activityBarBadge.background\\\":\\\"#528bff\\\",\\\"activityBarBadge.foreground\\\":\\\"#d7dae0\\\",\\\"button.background\\\":\\\"#4d78cc\\\",\\\"button.foreground\\\":\\\"#ffffff\\\",\\\"button.hoverBackground\\\":\\\"#6087cf\\\",\\\"diffEditor.insertedTextBackground\\\":\\\"#00809b33\\\",\\\"dropdown.background\\\":\\\"#353b45\\\",\\\"dropdown.border\\\":\\\"#181a1f\\\",\\\"editorIndentGuide.activeBackground\\\":\\\"#626772\\\",\\\"editor.background\\\":\\\"#282c34\\\",\\\"editor.foreground\\\":\\\"#abb2bf\\\",\\\"editor.lineHighlightBackground\\\":\\\"#99bbff0a\\\",\\\"editor.selectionBackground\\\":\\\"#3e4451\\\",\\\"editorCursor.foreground\\\":\\\"#528bff\\\",\\\"editor.findMatchHighlightBackground\\\":\\\"#528bff3d\\\",\\\"editorGroup.background\\\":\\\"#21252b\\\",\\\"editorGroup.border\\\":\\\"#181a1f\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#21252b\\\",\\\"editorIndentGuide.background\\\":\\\"#abb2bf26\\\",\\\"editorLineNumber.foreground\\\":\\\"#636d83\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#abb2bf\\\",\\\"editorWhitespace.foreground\\\":\\\"#abb2bf26\\\",\\\"editorRuler.foreground\\\":\\\"#abb2bf26\\\",\\\"editorHoverWidget.background\\\":\\\"#21252b\\\",\\\"editorHoverWidget.border\\\":\\\"#181a1f\\\",\\\"editorSuggestWidget.background\\\":\\\"#21252b\\\",\\\"editorSuggestWidget.border\\\":\\\"#181a1f\\\",\\\"editorSuggestWidget.selectedBackground\\\":\\\"#2c313a\\\",\\\"editorWidget.background\\\":\\\"#21252b\\\",\\\"editorWidget.border\\\":\\\"#3a3f4b\\\",\\\"input.background\\\":\\\"#1b1d23\\\",\\\"input.border\\\":\\\"#181a1f\\\",\\\"focusBorder\\\":\\\"#528bff\\\",\\\"list.activeSelectionBackground\\\":\\\"#2c313a\\\",\\\"list.activeSelectionForeground\\\":\\\"#d7dae0\\\",\\\"list.focusBackground\\\":\\\"#2c313a\\\",\\\"list.hoverBackground\\\":\\\"#2c313a66\\\",\\\"list.highlightForeground\\\":\\\"#d7dae0\\\",\\\"list.inactiveSelectionBackground\\\":\\\"#2c313a\\\",\\\"list.inactiveSelectionForeground\\\":\\\"#d7dae0\\\",\\\"notification.background\\\":\\\"#21252b\\\",\\\"pickerGroup.border\\\":\\\"#528bff\\\",\\\"scrollbarSlider.background\\\":\\\"#4e566680\\\",\\\"scrollbarSlider.activeBackground\\\":\\\"#747d9180\\\",\\\"scrollbarSlider.hoverBackground\\\":\\\"#5a637580\\\",\\\"sideBar.background\\\":\\\"#21252b\\\",\\\"sideBarSectionHeader.background\\\":\\\"#333842\\\",\\\"statusBar.background\\\":\\\"#21252b\\\",\\\"statusBar.foreground\\\":\\\"#9da5b4\\\",\\\"statusBarItem.hoverBackground\\\":\\\"#2c313a\\\",\\\"statusBar.noFolderBackground\\\":\\\"#21252b\\\",\\\"tab.activeBackground\\\":\\\"#282c34\\\",\\\"tab.activeForeground\\\":\\\"#d7dae0\\\",\\\"tab.border\\\":\\\"#181a1f\\\",\\\"tab.inactiveBackground\\\":\\\"#21252b\\\",\\\"titleBar.activeBackground\\\":\\\"#21252b\\\",\\\"titleBar.activeForeground\\\":\\\"#9da5b4\\\",\\\"titleBar.inactiveBackground\\\":\\\"#21252b\\\",\\\"titleBar.inactiveForeground\\\":\\\"#9da5b4\\\",\\\"statusBar.debuggingForeground\\\":\\\"#ffffff\\\",\\\"extensionButton.prominentBackground\\\":\\\"#2ba143\\\",\\\"extensionButton.prominentHoverBackground\\\":\\\"#37af4e\\\",\\\"badge.background\\\":\\\"#528bff\\\",\\\"badge.foreground\\\":\\\"#d7dae0\\\",\\\"peekView.border\\\":\\\"#528bff\\\",\\\"peekViewResult.background\\\":\\\"#21252b\\\",\\\"peekViewResult.selectionBackground\\\":\\\"#2c313a\\\",\\\"peekViewTitle.background\\\":\\\"#1b1d23\\\",\\\"peekViewEditor.background\\\":\\\"#1b1d23\\\"},\\\"watch\\\":false}\"},\"cpp.1.lastSessionDate\":{\"version\":1,\"value\":\"Wed Jul 16 2025\"},\"cpp.1.sessionCount\":{\"version\":1,\"value\":\"106\"},\"java.2.lastSessionDate\":{\"version\":1,\"value\":\"Wed Jul 16 2025\"},\"java.2.sessionCount\":{\"version\":1,\"value\":\"106\"},\"javascript.1.lastSessionDate\":{\"version\":1,\"value\":\"Sun Apr 20 2025\"},\"javascript.1.sessionCount\":{\"version\":1,\"value\":\"62\"},\"typescript.1.lastSessionDate\":{\"version\":1,\"value\":\"Wed Jul 16 2025\"},\"typescript.1.sessionCount\":{\"version\":1,\"value\":\"106\"},\"csharp.1.lastSessionDate\":{\"version\":1,\"value\":\"Wed Jul 16 2025\"},\"csharp.1.sessionCount\":{\"version\":1,\"value\":\"106\"},\"~remote.forwardedPortsContainer.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"~remote.forwardedPorts\\\",\\\"isHidden\\\":false}]\"},\"workbench.telemetryOptOutShown\":{\"version\":1,\"value\":\"true\"},\"memento/gettingStartedService\":{\"version\":1,\"value\":\"{\\\"installGit\\\":{\\\"done\\\":true},\\\"pickColorTheme\\\":{\\\"done\\\":true},\\\"pickColorThemeWeb\\\":{\\\"done\\\":true},\\\"newPickColorTheme\\\":{\\\"done\\\":true},\\\"settingsAndSync\\\":{\\\"done\\\":true},\\\"settingsSyncWeb\\\":{\\\"done\\\":true},\\\"newSettingsAndSync\\\":{\\\"done\\\":true},\\\"ms-vscode-remote.remote-wsl#wslWalkthrough#create.project\\\":{\\\"done\\\":true},\\\"scmSetup\\\":{\\\"done\\\":true},\\\"ms-vscode-remote.remote-wsl#wslWalkthrough#install.tools\\\":{\\\"done\\\":true},\\\"quickOpen\\\":{\\\"done\\\":true},\\\"quickOpenWeb\\\":{\\\"done\\\":true}}\"},\"encryption.migratedToGnomeLibsecret\":{\"version\":1,\"value\":\"true\"},\"themeUpdatedNotificationShown\":{\"version\":1,\"value\":\"true\"},\"workbench.view.extensions.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.local.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.remote.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.mcp.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.mcp.marketplace\\\",\\\"isHidden\\\":false}]\"},\"workbench.welcomePage.walkthroughMetadata\":{\"version\":1,\"value\":\"[[\\\"GitHub.copilot-chat#copilotWelcome\\\",{\\\"firstSeen\\\":1746950195793,\\\"stepIDs\\\":[\\\"copilot.setup.signIn\\\",\\\"copilot.setup.signInNoAction\\\",\\\"copilot.setup.signUp\\\",\\\"copilot.setup.signUpNoAction\\\",\\\"copilot.panelChat\\\",\\\"copilot.edits\\\",\\\"copilot.firstSuggest\\\",\\\"copilot.inlineChatNotMac\\\",\\\"copilot.inlineChatMac\\\",\\\"copilot.sparkle\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonWelcome\\\",{\\\"firstSeen\\\":1746950195793,\\\"stepIDs\\\":[\\\"python.createPythonFolder\\\",\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.createEnvironment\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonDataScienceWelcome\\\",{\\\"firstSeen\\\":1746950195793,\\\"stepIDs\\\":[\\\"python.installJupyterExt\\\",\\\"python.createNewNotebook\\\",\\\"python.openInteractiveWindow\\\",\\\"python.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode-remote.remote-wsl#wslWalkthrough\\\",{\\\"firstSeen\\\":1746950195793,\\\"stepIDs\\\":[\\\"explore.commands\\\",\\\"open.wslwindow\\\",\\\"create.project\\\",\\\"open.project\\\",\\\"linux.environment\\\",\\\"install.tools\\\",\\\"run.debug\\\",\\\"come.back\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode.cpptools#cppWelcome\\\",{\\\"firstSeen\\\":1746950195793,\\\"stepIDs\\\":[\\\"awaiting.activation.mac\\\",\\\"awaiting.activation.linux\\\",\\\"awaiting.activation.windows\\\",\\\"awaiting.activation.windows10\\\",\\\"awaiting.activation.windows11\\\",\\\"no.compilers.found.mac\\\",\\\"no.compilers.found.linux\\\",\\\"no.compilers.found.windows\\\",\\\"no.compilers.found.windows10\\\",\\\"no.compilers.found.windows11\\\",\\\"verify.compiler.mac\\\",\\\"verify.compiler.linux\\\",\\\"verify.compiler.windows\\\",\\\"verify.compiler.windows10\\\",\\\"verify.compiler.windows11\\\",\\\"create.cpp.file\\\",\\\"relaunch.developer.command.prompt.windows\\\",\\\"run.project.mac\\\",\\\"run.project.linux\\\",\\\"run.project.windows\\\",\\\"customize.debugging.linux\\\",\\\"customize.debugging.windows\\\",\\\"customize.debugging.mac\\\"],\\\"manaullyOpened\\\":false}],[\\\"pdconsec.vscode-print#how-to-print\\\",{\\\"firstSeen\\\":1746950195793,\\\"stepIDs\\\":[\\\"print-editor-active\\\",\\\"print-multiline-selection\\\",\\\"print-markdown-rendered\\\",\\\"print-remote\\\"],\\\"manaullyOpened\\\":false}]]\"},\"workbench.panel.chatSidebar.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.chat.movedView.welcomeView\\\",\\\"isHidden\\\":false}]\"},\"memento/workbench.editor.keybindings\":{\"version\":1,\"value\":\"{\\\"searchHistory\\\":[\\\"re\\\",\\\"re \\\",\\\"re re\\\",\\\"re run\\\",\\\"\\\\\\\"shift+f6\\\\\\\"\\\",\\\"\\\\\\\"shift+f6 backspace\\\\\\\"\\\",\\\"\\\\\\\"shift+f5\\\\\\\"\\\",\\\"debugg\\\",\\\"restart\\\",\\\"build\\\",\\\"run\\\",\\\"\\\\\\\"ctrl+enter\\\\\\\"\\\",\\\"\\\\\\\"ctrl+enter f5\\\\\\\"\\\",\\\"\\\\\\\"f5\\\\\\\"\\\",\\\"\\\\\\\"backspace backspace\\\\\\\"\\\",\\\"start\\\",\\\"start de\\\",\\\"\\\\\\\"ctrl+t enter\\\\\\\"\\\",\\\"\\\\\\\"ctrl\\\\\\\"\\\",\\\"\\\\\\\"ctrl+t\\\\\\\"\\\",\\\"comment\\\",\\\"completion\\\",\\\"\\\\\\\"ctrl+i\\\\\\\"\\\"]}\"},\"github-Bananonymous\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"vscode.github\\\",\\\"name\\\":\\\"GitHub\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"github.copilot\\\",\\\"name\\\":\\\"GitHub Copilot\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"github.copilot-chat\\\",\\\"name\\\":\\\"GitHub Copilot Chat\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"github.classroom\\\",\\\"name\\\":\\\"GitHub Classroom\\\",\\\"allowed\\\":true}]\"},\"workbench.panel.alignment\":{\"version\":1,\"value\":\"center\"},\"inline-chat-history\":{\"version\":1,\"value\":\"[\\\"bitburner hacking script\\\",\\\"/fix identifier \\\\\\\"LONG_LONG_MAX\\\\\\\" is undefined, ‘LONG_LONG_MAX’ undeclared (first use in this function); did you mean ‘LLONG_MAX’?\\\",\\\"/fix too few arguments in function call\\\",\\\"/fix implicit declaration of function ‘strlen’ [-Wimplicit-function-declaration]\\\",\\\"/usr/bin/ld: cannot find 10: No such file or directory\\\\n/usr/bin/ld: cannot find 24: No such file or directory\\\",\\\"add arguments \\\\\\\"10\\\\\\\" and \\\\\\\"24\\\\\\\" so it is added when it runs\\\",\\\"tasks.json for c files that uses launch.json\\\",\\\"make me a launch .json for c files\\\",\\\"make me a launch.json\\\",\\\"/fix implicit declaration of function ‘strtoll’ [-Wimplicit-function-declaration]\\\",\\\"make default main with argc & argv\\\",\\\"make default main with argc & arv\\\",\\\"make default main\\\",\\\"/fix implicit declaration of function ‘malloc’ [-Wimplicit-function-declaration]\\\",\\\"get the index of the subject\\\",\\\"/fix expression must have pointer-to-object type but it has type \\\\\\\"float\\\\\\\"\\\",\\\"access a 1d array with two sets of []\\\",\\\"/fix identifier \\\\\\\"string\\\\\\\" is undefined\\\",\\\"/fix invalid initializer\\\",\\\"comment the function swap() in doxygen format\\\",\\\"comment the next function in doxygen format\\\",\\\"What's wrong?\\\",\\\"doesn't work\\\",\\\"/fix comparison between pointer and integer\\\",\\\"/fix passing argument 2 of ‘input’ from incompatible pointer type [-Wincompatible-pointer-types]\\\",\\\"/fix \\\",\\\"Hello World\\\",\\\"rewrite this\\\",\\\"/fix a value of type \\\\\\\"float\\\\\\\" cannot be assigned to an entity of type \\\\\\\"float *\\\\\\\"\\\",\\\"/fix expression must have integral type\\\",\\\"/fix passing argument 1 of ‘addGrade’ from incompatible pointer type [-Wincompatible-pointer-types]\\\",\\\"seg faut\\\",\\\"/fix expected a ')'\\\",\\\"/fix new types may not be defined in a return type\\\",\\\"/fix no declaration matches ‘PuzzlePiece::PuzzlePiece(short int, char, Piece)’\\\",\\\"/fix identifier \\\\\\\"define\\\\\\\" is undefined\\\",\\\"/fix variable \\\\\\\"tab\\\\\\\" may not be initialized\\\",\\\"    // Example 2 - Modifie this line for a result of 255\\\\n    printf(\\\\\\\"Exemple 2 : %u\\\\\\\\n\\\\\\\", (char)i);\\\",\\\"Déclare une variable nombre de type int et lui attribue une valeur entière de votre choix.\\\\nDéclare un pointeur ptr vers un entier.\\\\nAffecte à ce pointeur l'adresse de la variable nombre.\\\\nAffiche la valeur de nombre à l'aide du pointeur ptr.\\\",\\\"Genereate a hello world\\\"]\"},\"workbench.view.extension.references-view.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"references-view.tree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"CppReferencesView\\\",\\\"isHidden\\\":false}]\"},\"ces/skipSurvey\":{\"version\":1,\"value\":\"1.87.0\"},\"terminal.integrated.showTerminalConfigPrompt\":{\"version\":1,\"value\":\"false\"},\"nps/lastSessionDate\":{\"version\":1,\"value\":\"Thu Apr 04 2024\"},\"nps/sessionCount\":{\"version\":1,\"value\":\"9\"},\"workbench.view.extension.github-pull-requests.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"github.classroom.assignmentExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:login\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"issues:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:conflictResolution\\\",\\\"isHidden\\\":false}]\"},\"workbench.view.extension.liveshare.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"liveshare.session\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.help\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.devtools\\\",\\\"isHidden\\\":false}]\"},\"Comments.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"isHidden\\\":false}]\"},\"workbench.view.extension.cmake-view.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"cmake.projectStatus\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cmake.outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cmake.pinnedCommands\\\",\\\"isHidden\\\":false}]\"},\"cpp.1.editedCount\":{\"version\":1,\"value\":\"3\"},\"cpp.1.editedDate\":{\"version\":1,\"value\":\"Mon Oct 14 2024\"},\"extensionsAssistant/deprecated\":{\"version\":1,\"value\":\"[\\\"github.classroom\\\"]\"},\"workbench.view.extension.github-pull-request.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"github:createPullRequestWebview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesFiles\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesCommits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"prStatus:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest:welcome\\\",\\\"isHidden\\\":false}]\"},\"refactorPreview.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"refactorPreview\\\",\\\"isHidden\\\":false}]\"},\"nps/isCandidate\":{\"version\":1,\"value\":\"false\"},\"nps/skipVersion\":{\"version\":1,\"value\":\"1.87.2\"},\"scm.input.lastActionId\":{\"version\":1,\"value\":\"github.copilot.git.generateCommitMessage\"},\"javascript.1.editedCount\":{\"version\":1,\"value\":\"10\"},\"javascript.1.editedDate\":{\"version\":1,\"value\":\"Fri Apr 11 2025\"},\"extension.features.state\":{\"version\":1,\"value\":\"{\\\"github.copilot-chat\\\":{\\\"languageModels\\\":{\\\"disabled\\\":false,\\\"accessTimes\\\":[]},\\\"copilot\\\":{\\\"disabled\\\":false,\\\"accessTimes\\\":[1750093502135,1750093542827,1750093549677,1750093565739,1750093575479,1750093598364,1750093617960,1750093626975,1750093632084,1750093644266,1750093650899,1750093659886,1750093668283,1750093687804,1750093699335,1750093714913,1750576168202,1750576172502,1750576176318,1750576179676,1750576188992,1750576192139,1750576195179,1750576217634,1750576221689,1750576226174,1750576261856,1750576267522,1750576270968,1750576274592,1750576282091,1750576291068,1750576297745,1750576303316,1750576307154,1750576314578,1750576322206,1750576333399,1750576340062,1750576346500,1750576351191,1750576357257,1750576393493,1750576400335,1750576406320,1750576430935,1750576447466,1750576459635,1750576465029,1750576473456,1750576481301,1750576486414,1750576490205,1750576495488,1750576502355,1750576507832,1750576514747,1750576521377,1750576529133,1750576535596,1750576541756,1750576548714,1750576876351,1750576888227,1750576893642,1750576898430,1750576910287,1750576937139,1750576984237,1750577333802,1750577342818,1750577350127,1750577358332,1750577367120,1750577375050,1750577382206,1750577390296,1750577395178,1750577403049,1750577408650,1750577420204,1750577432275,1750577442122,1750577459153,1750577466028,1750577479176,1750577487548,1750577495463,1750577514375,1750577522210,1750577529607,1750606257126,1750606319281,1750606344136,1750606375428,1750606383605,1750606397302,1750606402367,1750606405065,1750606407563,1750606414434,1750606418902,1750606428089,1750606433180,1750606440756,1750606445244,1750606449904,1750606482587,1750606528729,1750606589641,1750606597788,1750606602842,1750606607977,1750606623414,1750606628962,1750606636489,1750606642579,1750606907881,1750606911572,1750606933597,1750606942817,1750606958210,1750606971962,1750606981182,1750606987952,1750606998541,1750607007398,1750607021146,1750607027525,1750607032912,1750607036517,1750607043064,1750607049101,1750609679343,1750609693487,1750609699865,1750609707144,1750609713883,1750609721464,1750609729720,1750609736055,1750609742065,1750609754138,1750609761260,1750609791161,1750609800461,1750609816347,1750609820924,1750609825590,1750609833949,1750609838004,1750609843591,1750609858537,1750609866013,1750609871608,1750609877027,1750609884192,1750609895889,1750609905988,1750609914078,1750609930825,1750609944306,1750609952214,1750609961116,1750609965971,1750609973373,1750609978857,1750609983201,1750610011662,1750610018667,1750610022798,1750610029346,1750610036639,1750610042776,1750610048121,1750610053001,1750610058992,1750610062973,1750610068038,1750610071842,1750610662239,1750610666225,1750610669258,1750610672571,1750610676049,1750610678908,1750610681441,1750610684196,1750610687866,1750610691140,1750610704357,1750610711735,1750610721688,1750610906568,1750610912923,1750612774772,1750612826928,1750612836627,1750612840536,1750612844216,1750612847748,1750612852586,1750612856914,1750612864625,1750612875034,1750613200145,1750613200484,1750613204272,1750613217987,1750623691735,1750623693135,1750623709669,1750623745421,1750623759063,1750623765560,1750624918297,1750624925112,1750624998545,1750625017513,1750625236929,1750625262091,1750625272287,1750625281844,1750629308782,1750629312361,1750629327669,1750629334200,1750629339649,1750629344733,1750629351682,1750629362008,1750629376203,1750629693335,1750629708436,1750629712887,1750629718537,1750629730521,1750687739061,1750687747044,1750687752978,1750687758459,1750687766774,1750687847590,1750687853298,1750687860102,1750687868792,1750687881086,1750687888147,1750687903873,1750687911264,1750687925538,1750687934604,1750687947090,1750687962086,1750688002133,1750688015397]}}}\"},\"languageModelAccess.copilot-gpt-3.5-turbo\":{\"version\":1,\"value\":\"[\\\"github.copilot-chat\\\"]\"},\"languageModelStats.copilot-gpt-3.5-turbo\":{\"version\":1,\"value\":\"{\\\"extensions\\\":[{\\\"extensionId\\\":\\\"GitHub.copilot-chat\\\",\\\"requestCount\\\":0,\\\"tokenCount\\\":0,\\\"participants\\\":[{\\\"id\\\":\\\"github.copilot.editor\\\",\\\"requestCount\\\":13,\\\"tokenCount\\\":8264},{\\\"id\\\":\\\"git-commit\\\",\\\"requestCount\\\":6,\\\"tokenCount\\\":6376}]}]}\"},\"languageModelAccess.gpt-3.5-turbo\":{\"version\":1,\"value\":\"[\\\"github.copilot-chat\\\"]\"},\"languageModelStats.gpt-3.5-turbo\":{\"version\":1,\"value\":\"{\\\"extensions\\\":[{\\\"extensionId\\\":\\\"GitHub.copilot-chat\\\",\\\"requestCount\\\":36,\\\"tokenCount\\\":34172,\\\"participants\\\":[{\\\"id\\\":\\\"git-commit\\\",\\\"requestCount\\\":3,\\\"tokenCount\\\":3160}]}]}\"},\"workbench.welcomePage.hiddenCategories\":{\"version\":1,\"value\":\"[\\\"Beginner\\\"]\"},\"fileBasedRecommendations/promptedRecommendations\":{\"version\":1,\"value\":\"{\\\"makefile\\\":[\\\"ms-vscode.makefile-tools\\\"],\\\"plaintext\\\":[\\\"tomoki1207.pdf\\\"],\\\"python\\\":[\\\"ms-python.python\\\"]}\"},\"workbench.view.extension.makefile__viewContainer.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"makefile.outline\\\",\\\"isHidden\\\":false}]\"},\"workbench.view.extension.test.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.view.testCoverage\\\",\\\"isHidden\\\":false}]\"},\"commandPalette.mru.cache\":{\"version\":1,\"value\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"workbench.action.keybindingsReference\\\",\\\"value\\\":1},{\\\"key\\\":\\\"workbench.action.openDefaultKeybindingsFile\\\",\\\"value\\\":3},{\\\"key\\\":\\\"workbench.action.openGlobalKeybindingsFile\\\",\\\"value\\\":5},{\\\"key\\\":\\\"workbench.action.openGlobalKeybindings\\\",\\\"value\\\":6},{\\\"key\\\":\\\"remote-wsl.connect\\\",\\\"value\\\":7},{\\\"key\\\":\\\"plantuml.exportCurrent\\\",\\\"value\\\":8},{\\\"key\\\":\\\"python.setInterpreter\\\",\\\"value\\\":10},{\\\"key\\\":\\\"workbench.action.openSettingsJson\\\",\\\"value\\\":11},{\\\"key\\\":\\\"workbench.action.terminal.selectDefaultShell\\\",\\\"value\\\":12}]}\"},\"commandPalette.mru.counter\":{\"version\":1,\"value\":\"13\"},\"languageModelAccess.gpt-4o\":{\"version\":1,\"value\":\"[\\\"github.copilot-chat\\\"]\"},\"languageModelStats.gpt-4o\":{\"version\":1,\"value\":\"{\\\"extensions\\\":[{\\\"extensionId\\\":\\\"GitHub.copilot-chat\\\",\\\"requestCount\\\":89,\\\"tokenCount\\\":200315,\\\"participants\\\":[]}]}\"},\"java.2.editedCount\":{\"version\":1,\"value\":\"3\"},\"java.2.editedDate\":{\"version\":1,\"value\":\"Wed Apr 30 2025\"},\"extensionTips/promptedExecutableTips\":{\"version\":1,\"value\":\"{\\\"docker\\\":[\\\"ms-azuretools.vscode-docker\\\",\\\"ms-vscode-remote.remote-containers\\\"],\\\"mvn\\\":[\\\"vscjava.vscode-java-pack\\\"],\\\"wsl\\\":[\\\"ms-vscode-remote.remote-wsl\\\"]}\"},\"workbench.auxiliarybar.pinnedPanels\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.chatEditing\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":101},{\\\"id\\\":\\\"workbench.panel.chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100}]\"},\"workbench.panel.chat.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\"},\"workbench.panel.chatEditing.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.edits\\\",\\\"isHidden\\\":false}]\"},\"extensionsAssistant/importantRecommendationsIgnore\":{\"version\":1,\"value\":\"[\\\"ms-azuretools.vscode-docker\\\",\\\"ms-vscode-remote.remote-containers\\\",\\\"vscjava.vscode-java-pack\\\"]\"},\"languageModelAccess.gpt-4o-mini\":{\"version\":1,\"value\":\"[\\\"github.copilot-chat\\\"]\"},\"languageModelStats.gpt-4o-mini\":{\"version\":1,\"value\":\"{\\\"extensions\\\":[{\\\"extensionId\\\":\\\"GitHub.copilot-chat\\\",\\\"requestCount\\\":51,\\\"tokenCount\\\":86237,\\\"participants\\\":[]}]}\"},\"workbench.view.remote.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"targetsWsl\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"~remote.helpPanel\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteTargets\\\",\\\"isHidden\\\":false}]\"},\"remote.explorerType\":{\"version\":1,\"value\":\"wsl\"},\"tabs-list-width-horizontal\":{\"version\":1,\"value\":\"438\"},\"extensions.trustedPublishers\":{\"version\":1,\"value\":\"{\\\"akamud\\\":{\\\"publisher\\\":\\\"akamud\\\",\\\"publisherDisplayName\\\":\\\"Mahmoud Ali\\\"},\\\"bianxianyang\\\":{\\\"publisher\\\":\\\"bianxianyang\\\",\\\"publisherDisplayName\\\":\\\"bianxianyang\\\"},\\\"pdconsec\\\":{\\\"publisher\\\":\\\"pdconsec\\\",\\\"publisherDisplayName\\\":\\\"PD Consulting\\\"},\\\"shd101wyy\\\":{\\\"publisher\\\":\\\"shd101wyy\\\",\\\"publisherDisplayName\\\":\\\"Yiyi Wang\\\"},\\\"tal7aouy\\\":{\\\"publisher\\\":\\\"tal7aouy\\\",\\\"publisherDisplayName\\\":\\\"Mhammed Talhaouy\\\"},\\\"tomoki1207\\\":{\\\"publisher\\\":\\\"tomoki1207\\\",\\\"publisherDisplayName\\\":\\\"tomoki1207\\\"},\\\"twxs\\\":{\\\"publisher\\\":\\\"twxs\\\",\\\"publisherDisplayName\\\":\\\"twxs\\\"},\\\"gruntfuggly\\\":{\\\"publisher\\\":\\\"Gruntfuggly\\\",\\\"publisherDisplayName\\\":\\\"Gruntfuggly\\\"},\\\"jebbs\\\":{\\\"publisher\\\":\\\"jebbs\\\",\\\"publisherDisplayName\\\":\\\"jebbs\\\"},\\\"well-ar\\\":{\\\"publisher\\\":\\\"well-ar\\\",\\\"publisherDisplayName\\\":\\\"well-ar\\\"},\\\"mebrahtom\\\":{\\\"publisher\\\":\\\"Mebrahtom\\\",\\\"publisherDisplayName\\\":\\\"Mebrahtom Guesh\\\"},\\\"13xforever\\\":{\\\"publisher\\\":\\\"13xforever\\\",\\\"publisherDisplayName\\\":\\\"13xforever\\\"},\\\"hediet\\\":{\\\"publisher\\\":\\\"hediet\\\",\\\"publisherDisplayName\\\":\\\"Henning Dieterichs\\\"},\\\"lov3\\\":{\\\"publisher\\\":\\\"Lov3\\\",\\\"publisherDisplayName\\\":\\\"Lov3\\\"},\\\"augment\\\":{\\\"publisher\\\":\\\"augment\\\",\\\"publisherDisplayName\\\":\\\"Augment Computing\\\"}}\"},\"snippets.usageTimestamps\":{\"version\":1,\"value\":\"[[\\\"snippets/javascript.code-snippets/For-Each Loop using =>\\\",1741960211405]]\"},\"workbench.view.extension.todo-tree-container.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"todo-tree-view\\\",\\\"isHidden\\\":false}]\"},\"chat.currentLanguageModel.editor\":{\"version\":1,\"value\":\"github.copilot-chat/claude-3.7-sonnet-thought\"},\"chatEditsView.hideMovedEditsView\":{\"version\":1,\"value\":\"true\"},\"workbench.statusbar.hidden\":{\"version\":1,\"value\":\"[\\\"GitHub.copilot.status\\\"]\"},\"chat.currentLanguageModel.panel\":{\"version\":1,\"value\":\"github.copilot-chat/claude-sonnet-4\"},\"javascript.1.isCandidate\":{\"version\":1,\"value\":\"false\"},\"javascript.1.skipVersion\":{\"version\":1,\"value\":\"1.99.2\"},\"workbench.view.extension.augment-chat.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\"},\"workbench.view.extension.augment-panel.state.hidden\":{\"version\":1,\"value\":\"[{\\\"id\\\":\\\"augment-next-edit\\\",\\\"isHidden\\\":false}]\"},\"chat.currentLanguageModel.editor.isDefault\":{\"version\":1,\"value\":\"false\"},\"chat.currentLanguageModel.panel.isDefault\":{\"version\":1,\"value\":\"false\"},\"chat.lastChatMode\":{\"version\":1,\"value\":\"agent\"},\"csharp.1.editedCount\":{\"version\":1,\"value\":\"1\"},\"csharp.1.editedDate\":{\"version\":1,\"value\":\"Sun Jun 22 2025\"}}}"}}