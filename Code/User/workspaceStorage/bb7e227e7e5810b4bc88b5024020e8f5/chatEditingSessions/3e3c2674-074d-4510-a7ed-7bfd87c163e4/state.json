{"version": 2, "sessionId": "3e3c2674-074d-4510-a7ed-7bfd87c163e4", "linearHistory": [{"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "30b73ad", "currentHash": "30b73ad", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "30b73ad", "currentHash": "e354353", "originalToCurrentEdit": [{"txt": "", "pos": 178, "len": 13}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "127b759", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset HOUSE_DIR \"/mnt/data/media/House_MD\"  # Change this to your House MD directory\nset WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f $WATCHED_FILE\n        touch $WATCHED_FILE\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f $EPISODES_LIST\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files and sort them naturally\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V > $EPISODES_LIST\n    echo \"Scanned (count (cat $EPISODES_LIST)) episodes\"\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" $WATCHED_FILE\n        echo \"$episode_path\" >> $WATCHED_FILE\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat $EPISODES_LIST)\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f $WATCHED_FILE\n        set watched_episodes (cat $WATCHED_FILE)\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        mpv \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat $EPISODES_LIST))\n    set watched_count 0\n    \n    if test -f $WATCHED_FILE\n        set watched_count (count (cat $WATCHED_FILE))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > $WATCHED_FILE\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "7d0a11b", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files and sort them naturally\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V > \"$EPISODES_LIST\"\n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes\"\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        mpv \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "30b73ad", "currentHash": "e354353", "originalToCurrentEdit": [{"txt": "", "pos": 178, "len": 13}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "df9e417", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files and sort them naturally\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V > \"$EPISODES_LIST\"\n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes\"\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        mpv \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "df9e417", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files and sort them naturally\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V > \"$EPISODES_LIST\"\n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes\"\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        mpv \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "30b73ad", "currentHash": "e354353", "originalToCurrentEdit": [{"txt": "", "pos": 178, "len": 13}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_fbf827a4-95cc-438e-891c-a98e284963b7", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "df9e417", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files and sort them naturally\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V > \"$EPISODES_LIST\"\n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes\"\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        mpv \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_fbf827a4-95cc-438e-891c-a98e284963b7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_1b651c43-4d9a-42cc-a8fe-7c98f269c6c5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_fbf827a4-95cc-438e-891c-a98e284963b7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "30b73ad", "currentHash": "e354353", "originalToCurrentEdit": [{"txt": "", "pos": 178, "len": 13}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_fbf827a4-95cc-438e-891c-a98e284963b7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_fbf827a4-95cc-438e-891c-a98e284963b7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_fbf827a4-95cc-438e-891c-a98e284963b7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_fbf827a4-95cc-438e-891c-a98e284963b7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_fbf827a4-95cc-438e-891c-a98e284963b7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b38f367", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files and sort them naturally\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V > \"$EPISODES_LIST\"\n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes\"\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference and other optimizations\n        mpv --alang=en,eng,english --slang=en,eng,english --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_fbf827a4-95cc-438e-891c-a98e284963b7%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_fbf827a4-95cc-438e-891c-a98e284963b7", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b38f367", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files and sort them naturally\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V > \"$EPISODES_LIST\"\n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes\"\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference and other optimizations\n        mpv --alang=en,eng,english --slang=en,eng,english --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_fbf827a4-95cc-438e-891c-a98e284963b7", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "30b73ad", "currentHash": "e354353", "originalToCurrentEdit": [{"txt": "", "pos": 178, "len": 13}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ab84246", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files and sort them naturally\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V > \"$EPISODES_LIST\"\n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes\"\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_71bbe7c5-9e6a-4f2e-9f30-056c29935f60", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "43bd1ef", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "e354353", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "dfaef78", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_f5b00866-b870-4a9b-a438-479365354e43%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "dfaef78", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "e354353", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "4ead7a8", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\n", "pos": 189, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "dfaef78", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "4ead7a8", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\n", "pos": 189, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_5cf040ae-4a1a-41d6-acee-c64bf096f7e7", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "ba7c36a", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\n\n# SAO aliases\n", "pos": 178, "len": 0}, {"txt": "ss=\"", "pos": 184, "len": 0}, {"txt": "_progress\"    # SAO Status\nalias sj=\"sao_jump\"        # SAO Jump to season", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "8ff0354", "originalToCurrentEdit": [{"txt": "function sao_scan --description 'Rescan SAO directory for episodes'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ac13e2e", "originalToCurrentEdit": [{"txt": "function sao_reset --description 'Reset SAO watched episodes list'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "e140397", "originalToCurrentEdit": [{"txt": "function sao --description 'Play next unwatched SAO episode'\n    # Source the tracker script\n    source $HOME/.config/fish/sao_tracker.fish\n    \n    # Play the next episode\n    sao_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f457569", "originalToCurrentEdit": [{"txt": "function sao_seasons --description 'Show available SAO seasons'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b492c53", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/home/<USER>/Downloads/SAO\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $SAO_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$SAO_EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$SAO_EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$SAO_EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$SAO_EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$SAO_EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$SAO_WATCHED_FILE\"\n            set season_watched (cat \"$SAO_WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$SAO_EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5bf945d", "originalToCurrentEdit": [{"txt": "function sao_progress --description 'Show SAO watching progress'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "96ea733", "originalToCurrentEdit": [{"txt": "function sao_jump --description 'Jump to a specific SAO season'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_2e7aedb7-aea2-4de8-924b-4df141df3845%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_1283af4b-c940-4f7a-b853-bd345dab9726", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "dfaef78", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "ba7c36a", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\n\n# SAO aliases\n", "pos": 178, "len": 0}, {"txt": "ss=\"", "pos": 184, "len": 0}, {"txt": "_progress\"    # SAO Status\nalias sj=\"sao_jump\"        # SAO Jump to season", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "a046e74", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files in season subdirectories and sort them naturally\n    # Convert the folder structure (S1/E01.mkv) to a unified format for tracking\n    find $SAO_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        sort -V > \"$SAO_EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$SAO_EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            set episode_count (find \"$season_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            echo \"  $season_name: $episode_count episodes\"\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$SAO_EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            set season_total (find \"$season_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$SAO_EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_1283af4b-c940-4f7a-b853-bd345dab9726", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "e140397", "originalToCurrentEdit": [{"txt": "function sao --description 'Play next unwatched SAO episode'\n    # Source the tracker script\n    source $HOME/.config/fish/sao_tracker.fish\n    \n    # Play the next episode\n    sao_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5bf945d", "originalToCurrentEdit": [{"txt": "function sao_progress --description 'Show SAO watching progress'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "8ff0354", "originalToCurrentEdit": [{"txt": "function sao_scan --description 'Rescan SAO directory for episodes'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ac13e2e", "originalToCurrentEdit": [{"txt": "function sao_reset --description 'Reset SAO watched episodes list'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f457569", "originalToCurrentEdit": [{"txt": "function sao_seasons --description 'Show available SAO seasons'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "96ea733", "originalToCurrentEdit": [{"txt": "function sao_jump --description 'Jump to a specific SAO season'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6d0aa8b", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files in season subdirectories and sort them naturally\n    # Convert the folder structure (S1/E01.mkv) to a unified format for tracking\n    find $SAO_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        sort -V > \"$SAO_EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$SAO_EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            set episode_count (find \"$season_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            echo \"  $season_name: $episode_count episodes\"\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$SAO_EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            set season_total (find \"$season_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    set first_episode (find \"$SAO_DIR/$formatted_season\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_1283af4b-c940-4f7a-b853-bd345dab9726%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_1283af4b-c940-4f7a-b853-bd345dab9726", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "dfaef78", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "ba7c36a", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\n\n# SAO aliases\n", "pos": 178, "len": 0}, {"txt": "ss=\"", "pos": 184, "len": 0}, {"txt": "_progress\"    # SAO Status\nalias sj=\"sao_jump\"        # SAO Jump to season", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b419a36", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$SAO_EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "e140397", "originalToCurrentEdit": [{"txt": "function sao --description 'Play next unwatched SAO episode'\n    # Source the tracker script\n    source $HOME/.config/fish/sao_tracker.fish\n    \n    # Play the next episode\n    sao_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5bf945d", "originalToCurrentEdit": [{"txt": "function sao_progress --description 'Show SAO watching progress'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "8ff0354", "originalToCurrentEdit": [{"txt": "function sao_scan --description 'Rescan SAO directory for episodes'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ac13e2e", "originalToCurrentEdit": [{"txt": "function sao_reset --description 'Reset SAO watched episodes list'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f457569", "originalToCurrentEdit": [{"txt": "function sao_seasons --description 'Show available SAO seasons'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "96ea733", "originalToCurrentEdit": [{"txt": "function sao_jump --description 'Jump to a specific SAO season'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "0d2856b", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes (skip empty lines)\n    set all_episodes (cat \"$SAO_EPISODES_LIST\" | grep -v '^$')\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\" | grep -v '^$')\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "dfaef78", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "ba7c36a", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\n\n# SAO aliases\n", "pos": 178, "len": 0}, {"txt": "ss=\"", "pos": 184, "len": 0}, {"txt": "_progress\"    # SAO Status\nalias sj=\"sao_jump\"        # SAO Jump to season", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "0d2856b", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes (skip empty lines)\n    set all_episodes (cat \"$SAO_EPISODES_LIST\" | grep -v '^$')\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\" | grep -v '^$')\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "e140397", "originalToCurrentEdit": [{"txt": "function sao --description 'Play next unwatched SAO episode'\n    # Source the tracker script\n    source $HOME/.config/fish/sao_tracker.fish\n    \n    # Play the next episode\n    sao_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5bf945d", "originalToCurrentEdit": [{"txt": "function sao_progress --description 'Show SAO watching progress'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "8ff0354", "originalToCurrentEdit": [{"txt": "function sao_scan --description 'Rescan SAO directory for episodes'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ac13e2e", "originalToCurrentEdit": [{"txt": "function sao_reset --description 'Reset SAO watched episodes list'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f457569", "originalToCurrentEdit": [{"txt": "function sao_seasons --description 'Show available SAO seasons'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "96ea733", "originalToCurrentEdit": [{"txt": "function sao_jump --description 'Jump to a specific SAO season'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_af1bfdc0-1ea3-49d9-a079-d1dbb8aca185%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "dfaef78", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "ba7c36a", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\n\n# SAO aliases\n", "pos": 178, "len": 0}, {"txt": "ss=\"", "pos": 184, "len": 0}, {"txt": "_progress\"    # SAO Status\nalias sj=\"sao_jump\"        # SAO Jump to season", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "0d2856b", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes (skip empty lines)\n    set all_episodes (cat \"$SAO_EPISODES_LIST\" | grep -v '^$')\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\" | grep -v '^$')\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_63bf4546-d3e4-4f7e-b80d-a215c6cd1223", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "e140397", "originalToCurrentEdit": [{"txt": "function sao --description 'Play next unwatched SAO episode'\n    # Source the tracker script\n    source $HOME/.config/fish/sao_tracker.fish\n    \n    # Play the next episode\n    sao_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5bf945d", "originalToCurrentEdit": [{"txt": "function sao_progress --description 'Show SAO watching progress'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "8ff0354", "originalToCurrentEdit": [{"txt": "function sao_scan --description 'Rescan SAO directory for episodes'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ac13e2e", "originalToCurrentEdit": [{"txt": "function sao_reset --description 'Reset SAO watched episodes list'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f457569", "originalToCurrentEdit": [{"txt": "function sao_seasons --description 'Show available SAO seasons'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "96ea733", "originalToCurrentEdit": [{"txt": "function sao_jump --description 'Jump to a specific SAO season'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "d00d9804-b838-48f9-9953-e4effe173c93", "entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "dfaef78", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22d00d9804-b838-48f9-9953-e4effe173c93%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "431c4850-ff11-4586-b5e2-6db815db1a30", "entries": [{"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5219272", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes (skip empty lines)\n    set all_episodes (cat \"$SAO_EPISODES_LIST\" | grep -v '^$')\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\" | grep -v '^$')\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\nfunction sao_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: sao_jump_to_episode <season> <episode>\"\n        echo \"Example: sao_jump_to_episode 1 5\"\n        echo \"         sao_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"1\" becomes \"S1\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Format episode number for searching (e.g., \"5\" becomes \"E05\" or \"E5\")\n    set episode_patterns \"E$target_episode\" (printf \"E%02d\" $target_episode)\n    \n    # Find the specific episode\n    set target_episode_path \"\"\n    for pattern in $episode_patterns\n        set target_episode_path (cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | grep \"$pattern\")\n        if test -n \"$target_episode_path\"\n            break\n        end\n    end\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode $target_episode not found in season $target_season!\"\n        echo \"Available episodes in $formatted_season:\"\n        cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | while read ep; echo \"  \"(basename \"$ep\"); end | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n                echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season E$target_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22431c4850-ff11-4586-b5e2-6db815db1a30%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ce55f24", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\nfunction house_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: house_jump_to_episode <season> <episode>\"\n        echo \"Example: house_jump_to_episode 1 5\"\n        echo \"         house_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season and episode (e.g., \"1\" \"5\" becomes \"S01E05\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    set formatted_episode (printf \"E%02d\" $target_episode)\n    set episode_pattern \"$formatted_season$formatted_episode\"\n    \n    # Find the specific episode\n    set target_episode_path (cat \"$EPISODES_LIST\" | grep \"$episode_pattern\")\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode S$target_season E$target_episode not found!\"\n        echo \"Available episodes in season $target_season:\"\n        cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$formatted_season\" | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n                echo \"$episode\" >> \"$WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season$formatted_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22431c4850-ff11-4586-b5e2-6db815db1a30%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_episode.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22431c4850-ff11-4586-b5e2-6db815db1a30%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_episode.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22431c4850-ff11-4586-b5e2-6db815db1a30%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "ba7c36a", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\n\n# SAO aliases\n", "pos": 178, "len": 0}, {"txt": "ss=\"", "pos": 184, "len": 0}, {"txt": "_progress\"    # SAO Status\nalias sj=\"sao_jump\"        # SAO Jump to season", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22431c4850-ff11-4586-b5e2-6db815db1a30%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6563145", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes (skip empty lines)\n    set all_episodes (cat \"$SAO_EPISODES_LIST\" | grep -v '^$')\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\" | grep -v '^$')\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_jump_to_episode <season> <episode> - Jump to a specific episode\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\nfunction sao_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: sao_jump_to_episode <season> <episode>\"\n        echo \"Example: sao_jump_to_episode 1 5\"\n        echo \"         sao_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"1\" becomes \"S1\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Format episode number for searching (e.g., \"5\" becomes \"E05\" or \"E5\")\n    set episode_patterns \"E$target_episode\" (printf \"E%02d\" $target_episode)\n    \n    # Find the specific episode\n    set target_episode_path \"\"\n    for pattern in $episode_patterns\n        set target_episode_path (cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | grep \"$pattern\")\n        if test -n \"$target_episode_path\"\n            break\n        end\n    end\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode $target_episode not found in season $target_season!\"\n        echo \"Available episodes in $formatted_season:\"\n        cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | while read ep; echo \"  \"(basename \"$ep\"); end | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n                echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season E$target_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "af2d469", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_jump_to_episode <season> <episode> - Jump to a specific episode\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\nfunction house_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: house_jump_to_episode <season> <episode>\"\n        echo \"Example: house_jump_to_episode 1 5\"\n        echo \"         house_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season and episode (e.g., \"1\" \"5\" becomes \"S01E05\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    set formatted_episode (printf \"E%02d\" $target_episode)\n    set episode_pattern \"$formatted_season$formatted_episode\"\n    \n    # Find the specific episode\n    set target_episode_path (cat \"$EPISODES_LIST\" | grep \"$episode_pattern\")\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode S$target_season E$target_episode not found!\"\n        echo \"Available episodes in season $target_season:\"\n        cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$formatted_season\" | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n                echo \"$episode\" >> \"$WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season$formatted_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "0bf31bb", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\nalias he=\"house_episode\"   # House Episode jump\n\n# SAO aliases\nalias ss=\"sao_progress\"    # SAO Status\n", "pos": 178, "len": 0}, {"txt": "sj=\"", "pos": 184, "len": 0}, {"txt": "_jump\"        # SAO Jump to season\nalias se=\"sao_episode\"     # SAO Episode jump", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_episode.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "db2b272", "originalToCurrentEdit": [{"txt": "function house_episode --description 'Jump to a specific House MD episode'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_episode $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_episode.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f024675", "originalToCurrentEdit": [{"txt": "function sao_episode --description 'Jump to a specific SAO episode'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_episode $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_591424d7-39e4-4759-8afd-54c495334769%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "af2d469", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/home/<USER>/Downloads/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_jump_to_episode <season> <episode> - Jump to a specific episode\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\nfunction house_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: house_jump_to_episode <season> <episode>\"\n        echo \"Example: house_jump_to_episode 1 5\"\n        echo \"         house_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season and episode (e.g., \"1\" \"5\" becomes \"S01E05\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    set formatted_episode (printf \"E%02d\" $target_episode)\n    set episode_pattern \"$formatted_season$formatted_episode\"\n    \n    # Find the specific episode\n    set target_episode_path (cat \"$EPISODES_LIST\" | grep \"$episode_pattern\")\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode S$target_season E$target_episode not found!\"\n        echo \"Available episodes in season $target_season:\"\n        cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$formatted_season\" | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n                echo \"$episode\" >> \"$WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season$formatted_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "0bf31bb", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\nalias he=\"house_episode\"   # House Episode jump\n\n# SAO aliases\nalias ss=\"sao_progress\"    # SAO Status\n", "pos": 178, "len": 0}, {"txt": "sj=\"", "pos": 184, "len": 0}, {"txt": "_jump\"        # SAO Jump to season\nalias se=\"sao_episode\"     # SAO Episode jump", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6563145", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes (skip empty lines)\n    set all_episodes (cat \"$SAO_EPISODES_LIST\" | grep -v '^$')\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\" | grep -v '^$')\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_jump_to_episode <season> <episode> - Jump to a specific episode\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\nfunction sao_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: sao_jump_to_episode <season> <episode>\"\n        echo \"Example: sao_jump_to_episode 1 5\"\n        echo \"         sao_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"1\" becomes \"S1\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Format episode number for searching (e.g., \"5\" becomes \"E05\" or \"E5\")\n    set episode_patterns \"E$target_episode\" (printf \"E%02d\" $target_episode)\n    \n    # Find the specific episode\n    set target_episode_path \"\"\n    for pattern in $episode_patterns\n        set target_episode_path (cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | grep \"$pattern\")\n        if test -n \"$target_episode_path\"\n            break\n        end\n    end\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode $target_episode not found in season $target_season!\"\n        echo \"Available episodes in $formatted_season:\"\n        cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | while read ep; echo \"  \"(basename \"$ep\"); end | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n                echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season E$target_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "e140397", "originalToCurrentEdit": [{"txt": "function sao --description 'Play next unwatched SAO episode'\n    # Source the tracker script\n    source $HOME/.config/fish/sao_tracker.fish\n    \n    # Play the next episode\n    sao_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5bf945d", "originalToCurrentEdit": [{"txt": "function sao_progress --description 'Show SAO watching progress'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "8ff0354", "originalToCurrentEdit": [{"txt": "function sao_scan --description 'Rescan SAO directory for episodes'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ac13e2e", "originalToCurrentEdit": [{"txt": "function sao_reset --description 'Reset SAO watched episodes list'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f457569", "originalToCurrentEdit": [{"txt": "function sao_seasons --description 'Show available SAO seasons'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "96ea733", "originalToCurrentEdit": [{"txt": "function sao_jump --description 'Jump to a specific SAO season'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_episode.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "db2b272", "originalToCurrentEdit": [{"txt": "function house_episode --description 'Jump to a specific House MD episode'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_episode $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_episode.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f024675", "originalToCurrentEdit": [{"txt": "function sao_episode --description 'Jump to a specific SAO episode'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_episode $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_c1a5baf8-58f0-4027-87dd-bb6bcb6a4900%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "1ac29e7", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# House MD Episode Tracker\n# This script manages tracking and playing House MD episodes\n\nset -g HOUSE_DIR \"/mnt/nas/Series/House\"  # Change this to your House MD directory\nset -g WATCHED_FILE \"$HOME/.config/fish/house_watched.txt\"\nset -g EPISODES_LIST \"$HOME/.config/fish/house_episodes.txt\"\n\nfunction house_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$WATCHED_FILE\"\n        touch \"$WATCHED_FILE\"\n        echo \"Initialized House MD tracker at $WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$EPISODES_LIST\"\n        house_scan_episodes\n    end\nend\n\nfunction house_scan_episodes\n    # Scan the House MD directory and create a sorted list of episodes\n    if not test -d $HOUSE_DIR\n        echo \"Error: House MD directory not found at $HOUSE_DIR\"\n        echo \"Please update HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n        return 1\n    end\n    \n    # Find all video files, sort them naturally by season and episode\n    find $HOUSE_DIR -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | \\\n        grep -E \"[Ss][0-9]+[Ee][0-9]+\" | \\\n        sort -t'S' -k2 -n | \\\n        sort -t'E' -k2 -n | \\\n        sort -V > \"$EPISODES_LIST\"\n    \n    set episode_count (count (cat \"$EPISODES_LIST\"))\n    echo \"Scanned $episode_count episodes across seasons\"\n    \n    # Show season breakdown\n    house_show_season_info\nend\n\nfunction house_show_season_info\n    if not test -f \"$EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    cat \"$EPISODES_LIST\" | while read episode\n        echo (basename \"$episode\")\n    end | grep -o \"S[0-9]\\+\" | sort | uniq -c | while read count season\n        echo \"  $season: $count episodes\"\n    end\nend\n\nfunction house_get_current_season\n    set next_episode (house_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        echo (basename \"$next_episode\") | grep -o \"S[0-9]\\+\" | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction house_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: house_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction house_get_next_episode\n    house_init\n    \n    # Get all episodes\n    set all_episodes (cat \"$EPISODES_LIST\")\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$WATCHED_FILE\"\n        set watched_episodes (cat \"$WATCHED_FILE\")\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction house_play_next\n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            house_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  house_scan_episodes - Rescan for new episodes\"\n        echo \"  house_reset - Start over (clear watched list)\"\n        echo \"  house_list_progress - Show progress\"\n    end\nend\n\nfunction house_list_progress\n    house_init\n    \n    set total_episodes (count (cat \"$EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$WATCHED_FILE\"\n        set watched_count (count (cat \"$WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"House MD Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    house_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (house_get_next_episode)\n        if test $status -eq 0\n            set current_season (house_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n        end\n    end\nend\n\nfunction house_show_season_progress\n    if not test -f \"$EPISODES_LIST\"; or not test -f \"$WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons\n    set seasons (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep -o \"S[0-9]\\+\" | sort | uniq)\n    \n    for season in $seasons\n        # Count total episodes in this season\n        set season_total (cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        \n        # Count watched episodes in this season\n        set season_watched 0\n        if test -f \"$WATCHED_FILE\"\n            set season_watched (cat \"$WATCHED_FILE\" | while read episode; echo (basename \"$episode\"); end | grep \"$season\" | wc -l)\n        end\n        \n        set season_remaining (math $season_total - $season_watched)\n        \n        if test $season_watched -eq $season_total\n            echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n        else if test $season_watched -eq 0\n            echo \"  $season: ⏸️  Not started (0/$season_total)\"\n        else\n            echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n        end\n    end\nend\n\nfunction house_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction house_help\n    echo \"House MD Tracker Commands:\"\n    echo \"  house - Play next unwatched episode\"\n    echo \"  house_scan_episodes - Rescan directory for episodes\"\n    echo \"  house_list_progress - Show watching progress\"\n    echo \"  house_list_seasons - Show available seasons\"\n    echo \"  house_jump_to_season <number> - Jump to a specific season\"\n    echo \"  house_jump_to_episode <season> <episode> - Jump to a specific episode\"\n    echo \"  house_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  house_reset - Clear watched episodes (start over)\"\n    echo \"  house_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit HOUSE_DIR in $HOME/.config/fish/house_md_tracker.fish\"\n    echo \"  Current directory: $HOUSE_DIR\"\nend\n\nfunction house_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: house_jump_to_season <season_number>\"\n        echo \"Example: house_jump_to_season 2\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season (e.g., \"2\" becomes \"S02\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    \n    # Find first episode of the target season\n    set first_episode (cat \"$EPISODES_LIST\" | while read episode; echo \"$episode\"; end | grep \"$formatted_season\" | head -1)\n    \n    if test -z \"$first_episode\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        house_list_seasons\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n            echo \"$episode\" >> \"$WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction house_list_seasons\n    house_init\n    echo \"Available seasons:\"\n    house_show_season_info\nend\n\nfunction house_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: house_jump_to_episode <season> <episode>\"\n        echo \"Example: house_jump_to_episode 1 5\"\n        echo \"         house_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    house_init\n    \n    # Format season and episode (e.g., \"1\" \"5\" becomes \"S01E05\")\n    set formatted_season (printf \"S%02d\" $target_season)\n    set formatted_episode (printf \"E%02d\" $target_episode)\n    set episode_pattern \"$formatted_season$formatted_episode\"\n    \n    # Find the specific episode\n    set target_episode_path (cat \"$EPISODES_LIST\" | grep \"$episode_pattern\")\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode S$target_season E$target_episode not found!\"\n        echo \"Available episodes in season $target_season:\"\n        cat \"$EPISODES_LIST\" | while read episode; echo (basename \"$episode\"); end | grep \"$formatted_season\" | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$WATCHED_FILE\"\n                echo \"$episode\" >> \"$WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season$formatted_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nhouse_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "fa42bb5", "originalToCurrentEdit": [{"txt": "function house --description 'Play next unwatched House MD episode'\n    # Source the tracker script\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    # Play the next episode\n    house_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "e354353", "currentHash": "0bf31bb", "originalToCurrentEdit": [{"txt": "\n# House MD aliases\nalias hs=\"house_progress\"  # House Status\nalias hj=\"house_jump\"      # House Jump to season\nalias he=\"house_episode\"   # House Episode jump\n\n# SAO aliases\nalias ss=\"sao_progress\"    # SAO Status\n", "pos": 178, "len": 0}, {"txt": "sj=\"", "pos": 184, "len": 0}, {"txt": "_jump\"        # SAO Jump to season\nalias se=\"sao_episode\"     # SAO Episode jump", "pos": 187, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "b8c9778", "originalToCurrentEdit": [{"txt": "function house_scan --description 'Rescan House MD directory for episodes'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "56bc612", "originalToCurrentEdit": [{"txt": "function house_progress --description 'Show House MD watching progress'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "be9b7bc", "originalToCurrentEdit": [{"txt": "function house_reset --description 'Reset House MD watched episodes list'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5c86ea2", "originalToCurrentEdit": [{"txt": "function house_demo --description 'Demo the house function without actually playing video'\n    source $HOME/.config/fish/house_md_tracker.fish\n    \n    set next_episode (house_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"🎬 Would play: \"(basename \"$next_episode\")\n        echo \"📁 Full path: $next_episode\"\n        echo \"\"\n        echo \"In the real 'house' command, this would:\"\n        echo \"1. Open mpv with this episode\"\n        echo \"2. After you close mpv, ask if you want to mark it as watched\"\n        echo \"3. If yes, add it to the watched list\"\n        echo \"\"\n        echo \"Current progress:\"\n        house_list_progress\n    else\n        echo \"No unwatched episodes found!\"\n    end\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6c127be", "originalToCurrentEdit": [{"txt": "function house_seasons --description 'Show available House MD seasons'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "3ff8b68", "originalToCurrentEdit": [{"txt": "function house_jump --description 'Jump to a specific House MD season'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "6563145", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes (skip empty lines)\n    set all_episodes (cat \"$SAO_EPISODES_LIST\" | grep -v '^$')\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\" | grep -v '^$')\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, subtitles disabled, and other optimizations\n        mpv --alang=en,eng,english --no-sub --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_jump_to_episode <season> <episode> - Jump to a specific episode\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\nfunction sao_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: sao_jump_to_episode <season> <episode>\"\n        echo \"Example: sao_jump_to_episode 1 5\"\n        echo \"         sao_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"1\" becomes \"S1\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Format episode number for searching (e.g., \"5\" becomes \"E05\" or \"E5\")\n    set episode_patterns \"E$target_episode\" (printf \"E%02d\" $target_episode)\n    \n    # Find the specific episode\n    set target_episode_path \"\"\n    for pattern in $episode_patterns\n        set target_episode_path (cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | grep \"$pattern\")\n        if test -n \"$target_episode_path\"\n            break\n        end\n    end\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode $target_episode not found in season $target_season!\"\n        echo \"Available episodes in $formatted_season:\"\n        cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | while read ep; echo \"  \"(basename \"$ep\"); end | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n                echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season E$target_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "e140397", "originalToCurrentEdit": [{"txt": "function sao --description 'Play next unwatched SAO episode'\n    # Source the tracker script\n    source $HOME/.config/fish/sao_tracker.fish\n    \n    # Play the next episode\n    sao_play_next\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "5bf945d", "originalToCurrentEdit": [{"txt": "function sao_progress --description 'Show SAO watching progress'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_progress\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "8ff0354", "originalToCurrentEdit": [{"txt": "function sao_scan --description 'Rescan SAO directory for episodes'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_scan_episodes\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "ac13e2e", "originalToCurrentEdit": [{"txt": "function sao_reset --description 'Reset SAO watched episodes list'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_reset\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f457569", "originalToCurrentEdit": [{"txt": "function sao_seasons --description 'Show available SAO seasons'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_list_seasons\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "96ea733", "originalToCurrentEdit": [{"txt": "function sao_jump --description 'Jump to a specific SAO season'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_season $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_episode.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "db2b272", "originalToCurrentEdit": [{"txt": "function house_episode --description 'Jump to a specific House MD episode'\n    source $HOME/.config/fish/house_md_tracker.fish\n    house_jump_to_episode $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_episode.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "f024675", "originalToCurrentEdit": [{"txt": "function sao_episode --description 'Jump to a specific SAO episode'\n    source $HOME/.config/fish/sao_tracker.fish\n    sao_jump_to_episode $argv\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "1197c6f", "originalToCurrentEdit": [{"txt": "#!/usr/bin/env fish\n\n# SAO Episode Tracker\n# This script manages tracking and playing SAO episodes\n\nset -g SAO_DIR \"/mnt/nas/Anime\"  # Change this to your SAO directory\nset -g SAO_WATCHED_FILE \"$HOME/.config/fish/sao_watched.txt\"\nset -g SAO_EPISODES_LIST \"$HOME/.config/fish/sao_episodes.txt\"\n\nfunction sao_init\n    # Initialize the system - create watched file if it doesn't exist\n    if not test -f \"$SAO_WATCHED_FILE\"\n        touch \"$SAO_WATCHED_FILE\"\n        echo \"Initialized SAO tracker at $SAO_WATCHED_FILE\"\n    end\n    \n    # Create episodes list if it doesn't exist\n    if not test -f \"$SAO_EPISODES_LIST\"\n        sao_scan_episodes\n    end\nend\n\nfunction sao_scan_episodes\n    # Scan the SAO directory and create a sorted list of episodes\n    if not test -d $SAO_DIR\n        echo \"Error: SAO directory not found at $SAO_DIR\"\n        echo \"Please update SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n        return 1\n    end\n    \n    # Clear existing episodes list\n    echo -n \"\" > \"$SAO_EPISODES_LIST\"\n    \n    # Scan each season directory with proper episode ordering\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            echo \"Scanning $season_name...\"\n            \n            # Handle different folder structures per season\n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders with parts\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n                    end\n                end\n            else\n                # Regular season folders (S1, S2, S3)\n                find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V >> \"$SAO_EPISODES_LIST\"\n            end\n        end\n    end\n    \n    set episode_count (wc -l < \"$SAO_EPISODES_LIST\")\n    echo \"Scanned $episode_count episodes across all seasons\"\n    \n    # Show season breakdown\n    sao_show_season_info\nend\n\nfunction sao_show_season_info\n    if not test -f \"$SAO_EPISODES_LIST\"\n        return\n    end\n    \n    echo \"Season breakdown:\"\n    # Count episodes by season folder\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season_name (basename \"$season_dir\")\n            \n            if test \"$season_name\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set episode_count 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set episode_count (math $episode_count + $part_episodes)\n                    end\n                end\n                echo \"  $season_name: $episode_count episodes (2 parts)\"\n            else\n                # Regular season folders\n                set episode_count (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                echo \"  $season_name: $episode_count episodes\"\n            end\n        end\n    end\nend\n\nfunction sao_get_current_season\n    set next_episode (sao_get_next_episode 2>/dev/null)\n    if test $status -eq 0\n        # Extract season from path (e.g., /path/S1/episode.mkv -> S1)\n        echo \"$next_episode\" | grep -o \"/S[0-9]\\+/\" | sed 's|/||g' | head -1\n    else\n        echo \"All seasons complete\"\n    end\nend\n\nfunction sao_mark_watched\n    set episode_path $argv[1]\n    if test -z \"$episode_path\"\n        echo \"Usage: sao_mark_watched <episode_path>\"\n        return 1\n    end\n    \n    # Add to watched list if not already there\n    if not grep -Fxq \"$episode_path\" \"$SAO_WATCHED_FILE\"\n        echo \"$episode_path\" >> \"$SAO_WATCHED_FILE\"\n        echo \"Marked as watched: \"(basename \"$episode_path\")\n    end\nend\n\nfunction sao_get_next_episode\n    sao_init\n    \n    # Get all episodes (skip empty lines)\n    set all_episodes (cat \"$SAO_EPISODES_LIST\" | grep -v '^$')\n    \n    # Get watched episodes\n    set watched_episodes\n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_episodes (cat \"$SAO_WATCHED_FILE\" | grep -v '^$')\n    end\n    \n    # Find first unwatched episode\n    for episode in $all_episodes\n        set is_watched false\n        for watched in $watched_episodes\n            if test \"$episode\" = \"$watched\"\n                set is_watched true\n                break\n            end\n        end\n        \n        if test \"$is_watched\" = false\n            echo \"$episode\"\n            return 0\n        end\n    end\n    \n    echo \"All episodes watched! Consider rescanning or starting over.\"\n    return 1\nend\n\nfunction sao_play_next\n    set next_episode (sao_get_next_episode)\n    \n    if test $status -eq 0\n        echo \"Playing: \"(basename \"$next_episode\")\n        # Play with English audio track preference, French subtitles, and other optimizations\n        mpv --alang=en,eng,english --slang=fr,fre,french --audio-display=no \"$next_episode\"\n        \n        # After mpv exits, ask if user wants to mark as watched\n        echo \"\"\n        read -P \"Mark this episode as watched? [Y/n]: \" -l response\n        \n        if test -z \"$response\"; or test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n            sao_mark_watched \"$next_episode\"\n        end\n    else\n        echo \"No unwatched episodes found!\"\n        echo \"Commands:\"\n        echo \"  sao_scan_episodes - Rescan for new episodes\"\n        echo \"  sao_reset - Start over (clear watched list)\"\n        echo \"  sao_list_progress - Show progress\"\n    end\nend\n\nfunction sao_list_progress\n    sao_init\n    \n    set total_episodes (count (cat \"$SAO_EPISODES_LIST\"))\n    set watched_count 0\n    \n    if test -f \"$SAO_WATCHED_FILE\"\n        set watched_count (count (cat \"$SAO_WATCHED_FILE\"))\n    end\n    \n    set remaining (math $total_episodes - $watched_count)\n    \n    echo \"SAO Progress:\"\n    echo \"  Total episodes: $total_episodes\"\n    echo \"  Watched: $watched_count\"\n    echo \"  Remaining: $remaining\"\n    \n    # Show season-specific progress\n    echo \"\"\n    echo \"Season Progress:\"\n    sao_show_season_progress\n    \n    if test $remaining -gt 0\n        set next_episode (sao_get_next_episode)\n        if test $status -eq 0\n            set current_season (sao_get_current_season)\n            echo \"\"\n            echo \"  Currently watching: $current_season\"\n            echo \"  Next episode: \"(basename \"$next_episode\")\n        end\n    end\nend\n\nfunction sao_show_season_progress\n    if not test -f \"$SAO_EPISODES_LIST\"; or not test -f \"$SAO_WATCHED_FILE\"\n        return\n    end\n    \n    # Get all seasons from directory structure\n    for season_dir in $SAO_DIR/S*\n        if test -d \"$season_dir\"\n            set season (basename \"$season_dir\")\n            \n            # Count total episodes in this season\n            if test \"$season\" = \"S4\"\n                # S4 has nested folders, count episodes in all subdirectories\n                set season_total 0\n                for part_dir in \"$season_dir\"/*\n                    if test -d \"$part_dir\"\n                        set part_episodes (find \"$part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n                        set season_total (math $season_total + $part_episodes)\n                    end\n                end\n            else\n                # Regular season folders\n                set season_total (find \"$season_dir\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | wc -l)\n            end\n            \n            # Count watched episodes in this season\n            set season_watched 0\n            if test -f \"$SAO_WATCHED_FILE\"\n                set season_watched (cat \"$SAO_WATCHED_FILE\" | grep \"/$season/\" | wc -l)\n            end\n            \n            set season_remaining (math $season_total - $season_watched)\n            \n            if test $season_watched -eq $season_total\n                echo \"  $season: ✅ Complete ($season_total/$season_total)\"\n            else if test $season_watched -eq 0\n                echo \"  $season: ⏸️  Not started (0/$season_total)\"\n            else\n                echo \"  $season: 📺 In progress ($season_watched/$season_total, $season_remaining remaining)\"\n            end\n        end\n    end\nend\n\nfunction sao_reset\n    read -P \"Are you sure you want to clear your watched episodes list? [y/N]: \" -l response\n    if test \"$response\" = \"y\"; or test \"$response\" = \"Y\"\n        echo \"\" > \"$SAO_WATCHED_FILE\"\n        echo \"Watched episodes list cleared!\"\n    else\n        echo \"Cancelled.\"\n    end\nend\n\nfunction sao_help\n    echo \"SAO Tracker Commands:\"\n    echo \"  sao - Play next unwatched episode\"\n    echo \"  sao_scan_episodes - Rescan directory for episodes\"\n    echo \"  sao_list_progress - Show watching progress\"\n    echo \"  sao_list_seasons - Show available seasons\"\n    echo \"  sao_jump_to_season <number> - Jump to a specific season\"\n    echo \"  sao_jump_to_episode <season> <episode> - Jump to a specific episode\"\n    echo \"  sao_mark_watched <path> - Manually mark episode as watched\"\n    echo \"  sao_reset - Clear watched episodes (start over)\"\n    echo \"  sao_help - Show this help\"\n    echo \"\"\n    echo \"Configuration:\"\n    echo \"  Edit SAO_DIR in $HOME/.config/fish/sao_tracker.fish\"\n    echo \"  Current directory: $SAO_DIR\"\nend\n\nfunction sao_jump_to_season\n    set target_season $argv[1]\n    if test -z \"$target_season\"\n        echo \"Usage: sao_jump_to_season <season_number>\"\n        echo \"Example: sao_jump_to_season 2\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"2\" becomes \"S2\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Find first episode of the target season\n    if test \"$formatted_season\" = \"S4\"\n        # S4 has nested folders, find the first episode in the first part\n        set first_part_dir (find \"$SAO_DIR/$formatted_season\" -type d -name \"*PART.1*\" | head -1)\n        if test -n \"$first_part_dir\"\n            set first_episode (find \"$first_part_dir\" -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n        end\n    else\n        # Regular season folders\n        set first_episode (find \"$SAO_DIR/$formatted_season\" -maxdepth 1 -type f \\( -name \"*.mkv\" -o -name \"*.mp4\" -o -name \"*.avi\" -o -name \"*.mov\" \\) | sort -V | head -1)\n    end\n    \n    if test -z \"$first_episode\"\n        echo \"No episodes found in season $target_season!\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Stop when we reach the target episode\n        if test \"$episode\" = \"$first_episode\"\n            break\n        end\n        \n        # Mark as watched if not already\n        if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n            echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n        end\n    end\n    \n    echo \"Jumped to $formatted_season - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$first_episode\")\nend\n\nfunction sao_list_seasons\n    sao_init\n    echo \"Available seasons:\"\n    sao_show_season_info\nend\n\nfunction sao_jump_to_episode\n    set target_season $argv[1]\n    set target_episode $argv[2]\n    \n    if test -z \"$target_season\"; or test -z \"$target_episode\"\n        echo \"Usage: sao_jump_to_episode <season> <episode>\"\n        echo \"Example: sao_jump_to_episode 1 5\"\n        echo \"         sao_jump_to_episode 2 10\"\n        return 1\n    end\n    \n    sao_init\n    \n    # Format season (e.g., \"1\" becomes \"S1\")\n    set formatted_season \"S$target_season\"\n    \n    # Check if season directory exists\n    if not test -d \"$SAO_DIR/$formatted_season\"\n        echo \"Season $target_season not found!\"\n        echo \"Available seasons:\"\n        sao_list_seasons\n        return 1\n    end\n    \n    # Format episode number for searching (e.g., \"5\" becomes \"E05\" or \"E5\")\n    set episode_patterns \"E$target_episode\" (printf \"E%02d\" $target_episode)\n    \n    # Find the specific episode\n    set target_episode_path \"\"\n    for pattern in $episode_patterns\n        set target_episode_path (cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | grep \"$pattern\")\n        if test -n \"$target_episode_path\"\n            break\n        end\n    end\n    \n    if test -z \"$target_episode_path\"\n        echo \"Episode $target_episode not found in season $target_season!\"\n        echo \"Available episodes in $formatted_season:\"\n        cat \"$SAO_EPISODES_LIST\" | grep \"/$formatted_season/\" | while read ep; echo \"  \"(basename \"$ep\"); end | head -5\n        echo \"...\"\n        return 1\n    end\n    \n    # Mark all previous episodes as watched\n    set found_target false\n    cat \"$SAO_EPISODES_LIST\" | while read episode\n        # Mark as watched if not already and we haven't reached target yet\n        if test \"$found_target\" = \"false\"\n            if test \"$episode\" = \"$target_episode_path\"\n                set found_target true\n                return\n            end\n            \n            if not grep -Fxq \"$episode\" \"$SAO_WATCHED_FILE\"\n                echo \"$episode\" >> \"$SAO_WATCHED_FILE\"\n            end\n        end\n    end\n    \n    echo \"Jumped to $formatted_season E$target_episode - marked all previous episodes as watched\"\n    echo \"Next episode: \"(basename \"$target_episode_path\")\nend\n\n# Auto-initialize when script is sourced\nsao_init\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2", "agentId": "github.copilot.editsAgent"}}]}], "linearHistoryIndex": 16, "initialFileContents": [["file:///home/<USER>/.config/fish/house_md_tracker.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/house.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/aliases.fish", "30b73ad"], ["file:///home/<USER>/.config/fish/functions/house_scan.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/house_progress.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/house_reset.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/house_demo.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/house_seasons.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/house_jump.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/sao_tracker.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/sao.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/sao_progress.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/sao_scan.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/sao_reset.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/sao_jump.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/house_episode.fish", "da39a3e"], ["file:///home/<USER>/.config/fish/functions/sao_episode.fish", "da39a3e"]], "recentSnapshot": {"entries": [{"resource": "file:///home/<USER>/.config/fish/house_md_tracker.fish", "languageId": "shellscript", "originalHash": "1ac29e7", "currentHash": "1ac29e7", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/house_md_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house.fish", "languageId": "shellscript", "originalHash": "fa42bb5", "currentHash": "fa42bb5", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/aliases.fish", "languageId": "shellscript", "originalHash": "2d85cf4", "currentHash": "2d85cf4", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/aliases.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_scan.fish", "languageId": "shellscript", "originalHash": "b8c9778", "currentHash": "b8c9778", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_progress.fish", "languageId": "shellscript", "originalHash": "56bc612", "currentHash": "56bc612", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_reset.fish", "languageId": "shellscript", "originalHash": "be9b7bc", "currentHash": "be9b7bc", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f696e4af-aa9e-4a0f-a562-f7b6ade630d2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_demo.fish", "languageId": "shellscript", "originalHash": "5c86ea2", "currentHash": "5c86ea2", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_demo.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_79e70b22-552f-4f23-92cf-aa0c1e99bcb5", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_seasons.fish", "languageId": "shellscript", "originalHash": "6c127be", "currentHash": "6c127be", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_jump.fish", "languageId": "shellscript", "originalHash": "3ff8b68", "currentHash": "3ff8b68", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_f5b00866-b870-4a9b-a438-479365354e43", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/sao_tracker.fish", "languageId": "shellscript", "originalHash": "1197c6f", "currentHash": "1197c6f", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/sao_tracker.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_0ef2fdf9-c41b-40ca-88c2-795d1f5259b2", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao.fish", "languageId": "shellscript", "originalHash": "e140397", "currentHash": "e140397", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_progress.fish", "languageId": "shellscript", "originalHash": "5bf945d", "currentHash": "5bf945d", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_progress.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_scan.fish", "languageId": "shellscript", "originalHash": "8ff0354", "currentHash": "8ff0354", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_scan.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_reset.fish", "languageId": "shellscript", "originalHash": "ac13e2e", "currentHash": "ac13e2e", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_reset.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_seasons.fish", "languageId": "shellscript", "originalHash": "f457569", "currentHash": "f457569", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_seasons.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_jump.fish", "languageId": "shellscript", "originalHash": "96ea733", "currentHash": "96ea733", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_jump.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_2e7aedb7-aea2-4de8-924b-4df141df3845", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/house_episode.fish", "languageId": "shellscript", "originalHash": "db2b272", "currentHash": "db2b272", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/house_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/functions/sao_episode.fish", "languageId": "shellscript", "originalHash": "f024675", "currentHash": "f024675", "originalToCurrentEdit": [], "state": 1, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/functions/sao_episode.fish?%7B%22sessionId%22%3A%223e3c2674-074d-4510-a7ed-7bfd87c163e4%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_591424d7-39e4-4759-8afd-54c495334769", "agentId": "github.copilot.editsAgent"}}]}}