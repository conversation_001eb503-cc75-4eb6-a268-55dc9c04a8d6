{"version": 2, "sessionId": "af5cb81b-6c69-46d4-bf41-ef12d3a119c9", "linearHistory": [{"requestId": "request_9613c428-b341-4a42-86b0-2341640d245e", "stops": [{"entries": []}, {"stopId": "02779a84-7afc-4711-bca4-78753b8e1af0", "entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_9613c428-b341-4a42-86b0-2341640d245e%22%2C%22undoStop%22%3A%2202779a84-7afc-4711-bca4-78753b8e1af0%22%7D", "telemetryInfo": {"requestId": "request_9613c428-b341-4a42-86b0-2341640d245e", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "1d2acdfe-6db3-4c6b-933b-a701376f9cdc", "entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "74989b4", "originalToCurrentEdit": [{"txt": "    ", "pos": 634, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_9613c428-b341-4a42-86b0-2341640d245e%22%2C%22undoStop%22%3A%221d2acdfe-6db3-4c6b-933b-a701376f9cdc%22%7D", "telemetryInfo": {"requestId": "request_9613c428-b341-4a42-86b0-2341640d245e", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "030c956d-1e44-4064-a76e-0ad35c58b131", "entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "d56c4ed", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_9613c428-b341-4a42-86b0-2341640d245e%22%2C%22undoStop%22%3A%22030c956d-1e44-4064-a76e-0ad35c58b131%22%7D", "telemetryInfo": {"requestId": "request_9613c428-b341-4a42-86b0-2341640d245e", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "7747d77", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}, {"txt": "    ", "pos": 1161, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_9613c428-b341-4a42-86b0-2341640d245e%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_9613c428-b341-4a42-86b0-2341640d245e", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_3dcfc40b-bca2-4aca-80d9-3b937231ad30", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "7747d77", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}, {"txt": "    ", "pos": 1161, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_3dcfc40b-bca2-4aca-80d9-3b937231ad30%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_9613c428-b341-4a42-86b0-2341640d245e", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "70342e53-915a-4372-a9e4-e31bbe202c32", "entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "7747d77", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}, {"txt": "    ", "pos": 1161, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_3dcfc40b-bca2-4aca-80d9-3b937231ad30%22%2C%22undoStop%22%3A%2270342e53-915a-4372-a9e4-e31bbe202c32%22%7D", "telemetryInfo": {"requestId": "request_3dcfc40b-bca2-4aca-80d9-3b937231ad30", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "a87b44d", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}, {"txt": "    ", "pos": 1161, "len": 0}, {"txt": "    ", "pos": 1260, "len": 0}, {"txt": "        ", "pos": 1302, "len": 0}, {"txt": " ", "pos": 1331, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_3dcfc40b-bca2-4aca-80d9-3b937231ad30%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_3dcfc40b-bca2-4aca-80d9-3b937231ad30", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "a87b44d", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}, {"txt": "    ", "pos": 1161, "len": 0}, {"txt": "    ", "pos": 1260, "len": 0}, {"txt": "        ", "pos": 1302, "len": 0}, {"txt": " ", "pos": 1331, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_3dcfc40b-bca2-4aca-80d9-3b937231ad30", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "a079f058-e202-415d-87ba-e63f2c1d78d3", "entries": [{"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098%22%2C%22undoStop%22%3A%22a079f058-e202-415d-87ba-e63f2c1d78d3%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "ecf9b81", "originalToCurrentEdit": [{"txt": "M", "pos": 349, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_d202e6aa-3f9b-4a3b-bf02-3eff6a55e80a", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d202e6aa-3f9b-4a3b-bf02-3eff6a55e80a%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_3dcfc40b-bca2-4aca-80d9-3b937231ad30", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d202e6aa-3f9b-4a3b-bf02-3eff6a55e80a%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d202e6aa-3f9b-4a3b-bf02-3eff6a55e80a%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_3dcfc40b-bca2-4aca-80d9-3b937231ad30", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "5bab8316-f56e-416c-8cb5-b2e323e9d6d0", "entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%225bab8316-f56e-416c-8cb5-b2e323e9d6d0%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "ce62709b-7b5d-4ee0-b6a7-74b95baa3d3e", "entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "830d338", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%22ce62709b-7b5d-4ee0-b6a7-74b95baa3d3e%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "24b188f3-476e-4fe0-b70e-92c365225086", "entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "d56c4ed", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%2224b188f3-476e-4fe0-b70e-92c365225086%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "f7e9f27d-a373-443b-848e-459f30757563", "entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "7747d77", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}, {"txt": "    ", "pos": 1161, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%22f7e9f27d-a373-443b-848e-459f30757563%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "e5760c11-b577-4236-9403-e17b1f2a63d5", "entries": [{"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%22e5760c11-b577-4236-9403-e17b1f2a63d5%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "5b5ce719-03f5-4287-9423-a116f182c632", "entries": [{"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "ecf9b81", "originalToCurrentEdit": [{"txt": "M", "pos": 349, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%225b5ce719-03f5-4287-9423-a116f182c632%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "3a05422", "originalToCurrentEdit": [{"txt": "# vim:fileencoding=utf-8:ft=conf", "pos": 0, "len": 32}, {"txt": "# Font family. You can also specify different fonts for the", "pos": 34, "len": 59}, {"txt": "# bold/italic/bold-italic variants. By default they are derived automatically,", "pos": 94, "len": 78}, {"txt": "# by the OSes font system. Setting them manually is useful for font families", "pos": 173, "len": 76}, {"txt": "# that have many weight variants like Book, Medium, Thick, etc. For example:", "pos": 250, "len": 76}, {"txt": "font_family      NotoMono Nerd Font", "pos": 328, "len": 35}, {"txt": "bold_font        auto", "pos": 364, "len": 21}, {"txt": "italic_font      auto", "pos": 386, "len": 21}, {"txt": "bold_italic_font auto", "pos": 408, "len": 21}, {"txt": "# Font size (in pts)", "pos": 431, "len": 20}, {"txt": "font_size        11.0", "pos": 452, "len": 21}, {"txt": "# The foreground color", "pos": 475, "len": 22}, {"txt": "foreground       #fac881", "pos": 498, "len": 24}, {"txt": "# The background color", "pos": 524, "len": 22}, {"txt": "background       #303446", "pos": 547, "len": 24}, {"txt": "# The foreground for selections", "pos": 573, "len": 31}, {"txt": "#selection_foreground #ffff00", "pos": 605, "len": 29}, {"txt": "selection_foreground #3a4449", "pos": 635, "len": 28}, {"txt": "# The background for selections", "pos": 665, "len": 31}, {"txt": "#selection_background #d75f5f", "pos": 697, "len": 29}, {"txt": "selection_background #f2fffc", "pos": 727, "len": 28}, {"txt": "# The cursor color", "pos": 757, "len": 18}, {"txt": "#cursor           #fac881", "pos": 776, "len": 25}, {"txt": "cursor            #f2fffc", "pos": 802, "len": 25}, {"txt": "# The cursor shape can be one of (block, beam, underline)", "pos": 829, "len": 57}, {"txt": "cursor_shape     block", "pos": 887, "len": 22}, {"txt": "# The interval (in seconds) at which to blink the cursor. Set to zero to", "pos": 911, "len": 72}, {"txt": "# disable blinking.", "pos": 984, "len": 19}, {"txt": "cursor_blink_interval     0.3", "pos": 1004, "len": 29}, {"txt": "# Stop blinking cursor after the specified number of seconds of keyboard inactivity. Set to", "pos": 1035, "len": 91}, {"txt": "# zero or a negative number to never stop blinking.", "pos": 1127, "len": 51}, {"txt": "# cursor_stop_blinking_after 15.0", "pos": 1179, "len": 33}, {"txt": "# Number of lines of history to keep in memory for scrolling back", "pos": 1214, "len": 65}, {"txt": "scrollback_lines 20000", "pos": 1280, "len": 22}, {"txt": "# Program with which to view scrollback in a new window. The scrollback buffer is passed as", "pos": 1304, "len": 91}, {"txt": "# STDIN to this program. If you change it, make sure the program you use can", "pos": 1396, "len": 76}, {"txt": "# handle ANSI escape sequences for colors and text formatting.", "pos": 1473, "len": 62}, {"txt": "scrollback_pager less +G -R", "pos": 1536, "len": 27}, {"txt": "# Wheel scroll multiplier (modify the amount scrolled by the mouse wheel)", "pos": 1565, "len": 73}, {"txt": "wheel_scroll_multiplier 5.0", "pos": 1639, "len": 27}, {"txt": "# The interval between successive clicks to detect double/triple clicks (in seconds)", "pos": 1668, "len": 84}, {"txt": "click_interval 0.5", "pos": 1753, "len": 18}, {"txt": "# Characters considered part of a word when double clicking. In addition to these characters", "pos": 1773, "len": 92}, {"txt": "# any character that is marked as an alpha-numeric character in the unicode", "pos": 1866, "len": 75}, {"txt": "# database will be matched.", "pos": 1942, "len": 27}, {"txt": "select_by_word_characters :@-./_~?&=%+#", "pos": 1970, "len": 39}, {"txt": "# Hide mouse cursor after the specified number of seconds of the mouse not being used. Set to", "pos": 2011, "len": 93}, {"txt": "# zero or a negative number to disable mouse cursor hiding.", "pos": 2105, "len": 59}, {"txt": "mouse_hide_wait 0.0", "pos": 2165, "len": 19}, {"txt": "# The enabled window layouts. A comma separated list of layout names. The special value * means", "pos": 2186, "len": 95}, {"txt": "# all layouts. The first listed layout will be used as the startup layout.", "pos": 2282, "len": 74}, {"txt": "# For a list of available layouts, see the file layouts.py", "pos": 2357, "len": 58}, {"txt": "enabled_layouts *", "pos": 2416, "len": 17}, {"txt": "# If enabled, the window size will be remembered so that new instances of kitty will have the same", "pos": 2435, "len": 98}, {"txt": "# size as the previous instance. If disabled, the window will initially have size configured", "pos": 2534, "len": 92}, {"txt": "# by initial_window_width/height, in pixels.", "pos": 2627, "len": 44}, {"txt": "remember_window_size   no", "pos": 2672, "len": 25}, {"txt": "initial_window_width   1008", "pos": 2698, "len": 27}, {"txt": "initial_window_height  530", "pos": 2726, "len": 26}, {"txt": "# Delay (in milliseconds) between screen updates. Decreasing it, increases fps", "pos": 2754, "len": 78}, {"txt": "# at the cost of more CPU usage. The default value yields ~100fps which is more", "pos": 2833, "len": 79}, {"txt": "# that sufficient for most uses.", "pos": 2913, "len": 32}, {"txt": "# repaint_delay    10", "pos": 2946, "len": 21}, {"txt": "repaint_delay    10", "pos": 2968, "len": 19}, {"txt": "# Delay (in milliseconds) before input from the program running in the terminal", "pos": 2989, "len": 79}, {"txt": "# is processed. Note that decreasing it will increase responsiveness, but also", "pos": 3069, "len": 78}, {"txt": "# increase CPU usage and might cause flicker in full screen programs that", "pos": 3148, "len": 73}, {"txt": "# redraw the entire screen on each loop, because kitty is so fast that partial", "pos": 3222, "len": 78}, {"txt": "# screen updates will be drawn.", "pos": 3301, "len": 31}, {"txt": "input_delay 3", "pos": 3333, "len": 13}, {"txt": "# Visual bell duration. Flash the screen when a bell occurs for the specified number of", "pos": 3348, "len": 87}, {"txt": "# seconds. Set to zero to disable.", "pos": 3436, "len": 34}, {"txt": "visual_bell_duration 0.0", "pos": 3471, "len": 24}, {"txt": "# Enable/disable the audio bell. Useful in environments that require silence.", "pos": 3497, "len": 77}, {"txt": "enable_audio_bell yes", "pos": 3575, "len": 21}, {"txt": "# The modifier keys to press when clicking with the mouse on URLs to open the URL", "pos": 3598, "len": 81}, {"txt": "open_url_modifiers ctrl+shift", "pos": 3680, "len": 29}, {"txt": "# The program with which to open URLs that are clicked on. The special value \"default\" means to", "pos": 3711, "len": 95}, {"txt": "# use the operating system's default URL handler.", "pos": 3807, "len": 49}, {"txt": "open_url_with default", "pos": 3857, "len": 21}, {"txt": "# The value of the TERM environment variable to set", "pos": 3880, "len": 51}, {"txt": "term xterm-kitty", "pos": 3932, "len": 16}, {"txt": "# The width (in pts) of window borders. Will be rounded to the nearest number of pixels based on screen resolution.", "pos": 3950, "len": 115}, {"txt": "window_border_width 0", "pos": 4066, "len": 21}, {"txt": "window_margin_width 0", "pos": 4089, "len": 21}, {"txt": "# window padding ", "pos": 4112, "len": 17}, {"txt": "window_padding_width 8", "pos": 4130, "len": 22}, {"txt": "# The color for the border of the active window", "pos": 4154, "len": 47}, {"txt": "active_border_color #ffed72", "pos": 4202, "len": 27}, {"txt": "# The color for the border of inactive windows", "pos": 4231, "len": 46}, {"txt": "inactive_border_color #cccccc", "pos": 4278, "len": 29}, {"txt": "# Tab-bar colors", "pos": 4309, "len": 16}, {"txt": "active_tab_foreground #000", "pos": 4326, "len": 26}, {"txt": "active_tab_background #eee", "pos": 4353, "len": 26}, {"txt": "inactive_tab_foreground #444", "pos": 4380, "len": 28}, {"txt": "inactive_tab_background #999", "pos": 4409, "len": 28}, {"txt": "# Works with picom (on linux) to add some transparency", "pos": 4439, "len": 54}, {"txt": "background_opacity 0.87", "pos": 4494, "len": 23}, {"txt": "dynamic_background_opacity yes", "pos": 4518, "len": 30}, {"txt": "# Key mapping", "pos": 4550, "len": 13}, {"txt": "# For a list of key names, see: http://www.glfw.org/docs/latest/group__keys.html", "pos": 4564, "len": 80}, {"txt": "# For a list of modifier names, see: http://www.glfw.org/docs/latest/group__mods.html", "pos": 4645, "len": 85}, {"txt": "# You can use the special action no_op to unmap a keyboard shortcut that is", "pos": 4731, "len": 75}, {"txt": "# assigned in the default configuration.", "pos": 4807, "len": 40}, {"txt": "# Clipboard", "pos": 4849, "len": 11}, {"txt": "map super+v             paste_from_clipboard", "pos": 4861, "len": 44}, {"txt": "map ctrl+shift+s        paste_from_selection", "pos": 4906, "len": 44}, {"txt": "map super+c             copy_to_clipboard", "pos": 4951, "len": 41}, {"txt": "map shift+insert        paste_from_selection", "pos": 4993, "len": 44}, {"txt": "# Scrolling", "pos": 5039, "len": 11}, {"txt": "map ctrl+shift+up        scroll_line_up", "pos": 5051, "len": 39}, {"txt": "map ctrl+shift+down      scroll_line_down", "pos": 5091, "len": 41}, {"txt": "map ctrl+shift+k         scroll_line_up", "pos": 5133, "len": 39}, {"txt": "map ctrl+shift+j         scroll_line_down", "pos": 5173, "len": 41}, {"txt": "map ctrl+shift+page_up   scroll_page_up", "pos": 5215, "len": 39}, {"txt": "map ctrl+shift+page_down scroll_page_down", "pos": 5255, "len": 41}, {"txt": "map ctrl+shift+home      scroll_home", "pos": 5297, "len": 36}, {"txt": "map ctrl+shift+end       scroll_end", "pos": 5334, "len": 35}, {"txt": "map ctrl+shift+h         show_scrollback", "pos": 5370, "len": 40}, {"txt": "# Window management", "pos": 5412, "len": 19}, {"txt": "map super+n             new_os_window", "pos": 5432, "len": 37}, {"txt": "map super+w             close_window", "pos": 5470, "len": 36}, {"txt": "map ctrl+shift+enter    new_window", "pos": 5507, "len": 34}, {"txt": "map ctrl+shift+]        next_window", "pos": 5542, "len": 35}, {"txt": "map ctrl+shift+[        previous_window", "pos": 5578, "len": 39}, {"txt": "map ctrl+shift+f        move_window_forward", "pos": 5618, "len": 43}, {"txt": "map ctrl+shift+b        move_window_backward", "pos": 5662, "len": 44}, {"txt": "map ctrl+shift+`        move_window_to_top", "pos": 5707, "len": 42}, {"txt": "map ctrl+shift+1        first_window", "pos": 5750, "len": 36}, {"txt": "map ctrl+shift+2        second_window", "pos": 5787, "len": 37}, {"txt": "map ctrl+shift+3        third_window", "pos": 5825, "len": 36}, {"txt": "map ctrl+shift+4        fourth_window", "pos": 5862, "len": 37}, {"txt": "map ctrl+shift+5        fifth_window", "pos": 5900, "len": 36}, {"txt": "map ctrl+shift+6        sixth_window", "pos": 5937, "len": 36}, {"txt": "map ctrl+shift+7        seventh_window", "pos": 5974, "len": 38}, {"txt": "map ctrl+shift+8        eighth_window", "pos": 6013, "len": 37}, {"txt": "map ctrl+shift+9        ninth_window", "pos": 6051, "len": 36}, {"txt": "map ctrl+shift+0        tenth_window", "pos": 6088, "len": 36}, {"txt": "# Tab management", "pos": 6126, "len": 16}, {"txt": "map ctrl+shift+right    next_tab", "pos": 6143, "len": 32}, {"txt": "map ctrl+shift+left     previous_tab", "pos": 6176, "len": 36}, {"txt": "map ctrl+shift+t        new_tab", "pos": 6213, "len": 31}, {"txt": "map ctrl+shift+q        close_tab", "pos": 6245, "len": 33}, {"txt": "map ctrl+shift+l        next_layout", "pos": 6279, "len": 35}, {"txt": "map ctrl+shift+.        move_tab_forward", "pos": 6315, "len": 40}, {"txt": "map ctrl+shift+,        move_tab_backward", "pos": 6356, "len": 41}, {"txt": "# Miscellaneous", "pos": 6399, "len": 15}, {"txt": "map ctrl+shift+up      increase_font_size", "pos": 6415, "len": 41}, {"txt": "map ctrl+shift+down    decrease_font_size", "pos": 6457, "len": 41}, {"txt": "map ctrl+shift+backspace restore_font_size", "pos": 6499, "len": 42}, {"txt": "# Symbol mapping (special font for specified unicode code points). Map the", "pos": 6543, "len": 74}, {"txt": "# specified unicode codepoints to a particular font. Useful if you need special", "pos": 6618, "len": 79}, {"txt": "# rendering for some symbols, such as for Powerline. Avoids the need for", "pos": 6698, "len": 72}, {"txt": "# patched fonts. Each unicode code point is specified in the form U+<code point", "pos": 6771, "len": 79}, {"txt": "# in hexadecimal>. You can specify multiple code points, separated by commas", "pos": 6851, "len": 76}, {"txt": "# and ranges separated by hyphens. symbol_map itself can be specified multiple times.", "pos": 6928, "len": 85}, {"txt": "# Syntax is:", "pos": 7014, "len": 12}, {"txt": "#", "pos": 7027, "len": 1}, {"txt": "# symbol_map codepoints Font Family Name", "pos": 7029, "len": 40}, {"txt": "#", "pos": 7070, "len": 1}, {"txt": "# For example:", "pos": 7072, "len": 14}, {"txt": "#", "pos": 7087, "len": 1}, {"txt": "#symbol_map U+E0A0-U+E0A2,U+E0B0-U+E0B3 PowerlineSymbols", "pos": 7089, "len": 56}, {"txt": "hide_window_decorations yes", "pos": 7146, "len": 27}, {"txt": "macos_option_as_alt no", "pos": 7174, "len": 22}, {"txt": "# Change the color of the kitty window's titlebar on macOS. A value of \"system\"", "pos": 7198, "len": 79}, {"txt": "# means to use the default system color, a value of \"background\" means to use", "pos": 7278, "len": 77}, {"txt": "# the default background color and finally you can use an arbitrary color, such", "pos": 7356, "len": 79}, {"txt": "# as #12af59 or \"red\".", "pos": 7436, "len": 22}, {"txt": "macos_titlebar_color background", "pos": 7459, "len": 31}, {"txt": "allow_remote_control yes", "pos": 7492, "len": 24}, {"txt": "# Graphics optimization settings for NVIDIA on Wayland", "pos": 7518, "len": 39}, {"txt": "# Force GPU usage instead of integrated graphics", "pos": 7558, "len": 67}, {"txt": "linux_display_server wayland", "pos": 7626, "len": 73}, {"txt": "", "pos": 7700, "len": 66}, {"txt": "# Disable GPU acceleration if causing issues (try this first)", "pos": 7767, "len": 0}, {"txt": "disable_ligatures never", "pos": 7768, "len": 59}, {"txt": "# Sync to monitor's refresh rate", "pos": 7829, "len": 30}, {"txt": "sync_to_monitor yes", "pos": 7860, "len": 25}, {"txt": "\n# Enable/disable vsync\nwait_for_repaint yes\n\n# GPU backend optimization\n# Try 'auto' first, then 'opengl' if issues persist\nbackend auto\n\n# Force proper font rendering\ntext_composition_strategy platform\n\n# Wayland specific settings\nwayland_titlebar_color background\n\n#include ~/.cache/wal/colors-kitty.conf\n#include ~/.config/kitty/background_opacity/background_opacity.conf\ninclude ~/.config/kitty/colorschemes/hyprland_redblizard_colorscheme.conf\n#include ~/.config/kitty/colorschemes/endeavouros_colorscheme.conf\n\n#kitty +kitten icat ~/.config/neofetch/endeavouros-dark.svg\n\n# 0 disables it; -1 enables it\nconfirm_os_window_close 0\n", "pos": 7886, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_8c3af281-b0a7-4876-a88e-5740ee6e75f6%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "a87b44d", "originalToCurrentEdit": [{"txt": "test", "pos": 109, "len": 1}, {"txt": "", "pos": 145, "len": 2}, {"txt": "    ", "pos": 148, "len": 0}, {"txt": "    ", "pos": 634, "len": 0}, {"txt": "    ", "pos": 1161, "len": 0}, {"txt": "    ", "pos": 1260, "len": 0}, {"txt": "        ", "pos": 1302, "len": 0}, {"txt": " ", "pos": 1331, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_4a05f573-641b-4db9-be1a-0b019e5961f3%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_4a05f573-641b-4db9-be1a-0b019e5961f3%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "3a05422", "originalToCurrentEdit": [{"txt": "M", "pos": 349, "len": 1}, {"txt": "\n# Graphics optimization settings for NVIDIA on Wayland\n# Force GPU usage instead of integrated graphics\nlinux_display_server wayland\n\n# Disable GPU acceleration if causing issues (try this first)\ndisable_ligatures never\n\n# Sync to monitor's refresh rate\nsync_to_monitor yes\n\n# Enable/disable vsync\nwait_for_repaint yes\n\n# GPU backend optimization\n# Try 'auto' first, then 'opengl' if issues persist\nbackend auto\n\n# Force proper font rendering\ntext_composition_strategy platform\n\n# Wayland specific settings\nwayland_titlebar_color background\n", "pos": 7517, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_4a05f573-641b-4db9-be1a-0b019e5961f3%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "7952274c-654d-4e6c-83f3-f039c204ebd7", "entries": [{"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "3a05422", "originalToCurrentEdit": [{"txt": "M", "pos": 349, "len": 1}, {"txt": "\n# Graphics optimization settings for NVIDIA on Wayland\n# Force GPU usage instead of integrated graphics\nlinux_display_server wayland\n\n# Disable GPU acceleration if causing issues (try this first)\ndisable_ligatures never\n\n# Sync to monitor's refresh rate\nsync_to_monitor yes\n\n# Enable/disable vsync\nwait_for_repaint yes\n\n# GPU backend optimization\n# Try 'auto' first, then 'opengl' if issues persist\nbackend auto\n\n# Force proper font rendering\ntext_composition_strategy platform\n\n# Wayland specific settings\nwayland_titlebar_color background\n", "pos": 7517, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_4a05f573-641b-4db9-be1a-0b019e5961f3%22%2C%22undoStop%22%3A%227952274c-654d-4e6c-83f3-f039c204ebd7%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "4a564ead-b390-4719-8f14-f52459f023c8", "entries": [{"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "985a099", "originalToCurrentEdit": [{"txt": "M", "pos": 349, "len": 1}, {"txt": "\n# Graphics optimization settings for NVIDIA on Wayland\n# Force GPU usage instead of integrated graphics\nlinux_display_server wayland\n\n# Disable GPU acceleration if causing issues (try this first)\ndisable_ligatures never\n\n# Sync to monitor's refresh rate\nsync_to_monitor yes\n\n# Enable/disable vsync\nwait_for_repaint yes\n\n# GPU backend optimization\n# Try 'auto' first, then 'opengl' if issues persist\nbackend auto\n\n# Force proper font rendering\ntext_composition_strategy platform\n\n# Wayland specific settings\nwayland_titlebar_color background\n", "pos": 7517, "len": 0}, {"txt": "\n# GPU acceleration and rendering fixes\nlinux_display_server wayland\nwayland_titlebar_color background\n\n# Fix black screen issues\nsync_to_monitor yes\ndisable_ligatures never\n\n# GPU acceleration settings for NVIDIA\n#gpu_acceleration yes\n\n# Alternative: disable GPU if issues persist\n#disable_desktop_notifications no\n\n# Debug rendering issues\n#debug_rendering no\n#debug_font_fallback no\n", "pos": 7886, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_4a05f573-641b-4db9-be1a-0b019e5961f3%22%2C%22undoStop%22%3A%224a564ead-b390-4719-8f14-f52459f023c8%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_4a05f573-641b-4db9-be1a-0b019e5961f3%22%2C%22undoStop%22%3A%224a564ead-b390-4719-8f14-f52459f023c8%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "e907da8", "originalToCurrentEdit": [{"txt": "M", "pos": 349, "len": 1}, {"txt": "\n# Graphics optimization settings for NVIDIA on Wayland\n# Force GPU usage instead of integrated graphics\nlinux_display_server wayland\n\n# Disable GPU acceleration if causing issues (try this first)\ndisable_ligatures never\n\n# Sync to monitor's refresh rate\nsync_to_monitor yes\n\n# Enable/disable vsync\nwait_for_repaint yes\n\n# GPU backend optimization\n# Try 'auto' first, then 'opengl' if issues persist\nbackend auto\n\n# Force proper font rendering\ntext_composition_strategy platform\n\n# Wayland specific settings\nwayland_titlebar_color background\n", "pos": 7517, "len": 0}, {"txt": "\n# GPU acceleration and rendering fixes\nlinux_display_server wayland\nwayland_titlebar_color background\n\n# Fix black screen issues\nsync_to_monitor yes\ndisable_ligatures never\n\n# GPU acceleration settings for NVIDIA - disabled for troubleshooting\n# gpu_acceleration yes\n\n# Force software rendering to fix black screen\ndisable_desktop_notifications no\n\n# Debug rendering issues\n#debug_rendering no\n#debug_font_fallback no\n", "pos": 7886, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_4a05f573-641b-4db9-be1a-0b019e5961f3%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "64295c2", "originalToCurrentEdit": [{"txt": "if status is-interactive\n    echo \"Minimal fish config loaded successfully!\"\n    echo \"Current directory: $(pwd)\"\n    echo \"Fish version: $(fish --version)\"\n    set -U fish_greeting \"\"\nend\n", "pos": 0, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_4a05f573-641b-4db9-be1a-0b019e5961f3%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_7c928ad9-4df8-463f-8481-122eb21cb861", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_7c928ad9-4df8-463f-8481-122eb21cb861%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_7c928ad9-4df8-463f-8481-122eb21cb861%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_7c928ad9-4df8-463f-8481-122eb21cb861%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_7c928ad9-4df8-463f-8481-122eb21cb861%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_ef4d4d8b-a3b9-4595-b530-c87fb9461a47", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_ef4d4d8b-a3b9-4595-b530-c87fb9461a47%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_ef4d4d8b-a3b9-4595-b530-c87fb9461a47%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_ef4d4d8b-a3b9-4595-b530-c87fb9461a47%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_ef4d4d8b-a3b9-4595-b530-c87fb9461a47%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_7e4b5231-e510-4b6e-903a-eb383bfd4acf", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_7e4b5231-e510-4b6e-903a-eb383bfd4acf%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_7e4b5231-e510-4b6e-903a-eb383bfd4acf%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_7e4b5231-e510-4b6e-903a-eb383bfd4acf%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_7e4b5231-e510-4b6e-903a-eb383bfd4acf%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_6bb2ad42-c891-48e0-8c5e-011414a5d641", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_6bb2ad42-c891-48e0-8c5e-011414a5d641%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_6bb2ad42-c891-48e0-8c5e-011414a5d641%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_6bb2ad42-c891-48e0-8c5e-011414a5d641%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_6bb2ad42-c891-48e0-8c5e-011414a5d641%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_e972cd19-676a-48c6-87b2-a935e0dd24fc%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_e972cd19-676a-48c6-87b2-a935e0dd24fc%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_e972cd19-676a-48c6-87b2-a935e0dd24fc%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_e972cd19-676a-48c6-87b2-a935e0dd24fc%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "945b0cda-9ee3-45fd-b52a-63d3db943e65", "entries": [{"resource": "file:///home/<USER>/.config/hypr/conf/env_var.conf", "languageId": "properties", "originalHash": "46f1963", "currentHash": "46f1963", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/hypr/conf/env_var.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_e972cd19-676a-48c6-87b2-a935e0dd24fc%22%2C%22undoStop%22%3A%22945b0cda-9ee3-45fd-b52a-63d3db943e65%22%7D", "telemetryInfo": {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "6750a03f-be71-49ce-84a1-e646aabfc120", "entries": [{"resource": "file:///home/<USER>/.config/hypr/conf/env_var.conf", "languageId": "properties", "originalHash": "46f1963", "currentHash": "7bd2f13", "originalToCurrentEdit": [{"txt": " - Made conditional for Hyprland only", "pos": 950, "len": 0}, {"txt": "# ", "pos": 955, "len": 0}, {"txt": "  # Let session manager set this", "pos": 989, "len": 0}, {"txt": "# ", "pos": 994, "len": 0}, {"txt": "      # Let session manager set this", "pos": 1024, "len": 0}, {"txt": "# ", "pos": 1029, "len": 0}, {"txt": "  # Let session manager set this", "pos": 1063, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/hypr/conf/env_var.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_e972cd19-676a-48c6-87b2-a935e0dd24fc%22%2C%22undoStop%22%3A%226750a03f-be71-49ce-84a1-e646aabfc120%22%7D", "telemetryInfo": {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "cc452257-4bb9-4b9e-aced-24a013e2993c", "entries": [{"resource": "file:///home/<USER>/.config/hypr/conf/env_var.conf", "languageId": "properties", "originalHash": "46f1963", "currentHash": "eb5165d", "originalToCurrentEdit": [{"txt": " - Made conditional for Hyprland only", "pos": 950, "len": 0}, {"txt": "# ", "pos": 955, "len": 0}, {"txt": "  # Let session manager set this", "pos": 989, "len": 0}, {"txt": "# ", "pos": 994, "len": 0}, {"txt": "      # Let session manager set this", "pos": 1024, "len": 0}, {"txt": "# ", "pos": 1029, "len": 0}, {"txt": "  # Let session manager set this", "pos": 1063, "len": 0}, {"txt": ",x11  # Allow fallback to X11", "pos": 1739, "len": 0}, {"txt": "# ", "pos": 1882, "len": 0}, {"txt": "  # Let kitty auto-detect", "pos": 1910, "len": 0}, {"txt": "# ", "pos": 1915, "len": 0}, {"txt": "    # Let applications auto-detect", "pos": 1941, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/hypr/conf/env_var.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_e972cd19-676a-48c6-87b2-a935e0dd24fc%22%2C%22undoStop%22%3A%22cc452257-4bb9-4b9e-aced-24a013e2993c%22%7D", "telemetryInfo": {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///home/<USER>/.config/hypr/conf/env_var.conf", "languageId": "properties", "originalHash": "46f1963", "currentHash": "5a8a6b9", "originalToCurrentEdit": [{"txt": " - Made conditional for Hyprland only", "pos": 950, "len": 0}, {"txt": "# ", "pos": 955, "len": 0}, {"txt": "  # Let session manager set this", "pos": 989, "len": 0}, {"txt": "# ", "pos": 994, "len": 0}, {"txt": "      # Let session manager set this", "pos": 1024, "len": 0}, {"txt": "# ", "pos": 1029, "len": 0}, {"txt": "  # Let session manager set this", "pos": 1063, "len": 0}, {"txt": ",x11  # Allow fallback to X11", "pos": 1739, "len": 0}, {"txt": "# ", "pos": 1882, "len": 0}, {"txt": "  # Let kitty auto-detect", "pos": 1910, "len": 0}, {"txt": "# ", "pos": 1915, "len": 0}, {"txt": "    # Let applications auto-detect", "pos": 1941, "len": 0}, {"txt": "# ", "pos": 2481, "len": 0}, {"txt": "  # Let session manager set this", "pos": 2511, "len": 0}, {"txt": "# ", "pos": 2789, "len": 0}, {"txt": "  # Let applications auto-detect", "pos": 2815, "len": 0}, {"txt": " (Hyprland-specific)", "pos": 2856, "len": 0}, {"txt": " ", "pos": 2857, "len": 0}, {"txt": "#", "pos": 2859, "len": 0}, {"txt": "      # Comment out for X11 compatibility", "pos": 2884, "len": 0}, {"txt": " ", "pos": 2885, "len": 0}, {"txt": "#", "pos": 2887, "len": 0}, {"txt": "     # Wayland compositor specific", "pos": 2913, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/hypr/conf/env_var.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_e972cd19-676a-48c6-87b2-a935e0dd24fc%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "agentId": "github.copilot.editsAgent"}}]}, {"requestId": "request_efc3471c-d7b6-4480-8734-48a901d707cd", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_efc3471c-d7b6-4480-8734-48a901d707cd%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_efc3471c-d7b6-4480-8734-48a901d707cd%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_efc3471c-d7b6-4480-8734-48a901d707cd%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_efc3471c-d7b6-4480-8734-48a901d707cd%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/hypr/conf/env_var.conf", "languageId": "properties", "originalHash": "46f1963", "currentHash": "5a8a6b9", "originalToCurrentEdit": [{"txt": " - Made conditional for Hyprland only", "pos": 950, "len": 0}, {"txt": "# ", "pos": 955, "len": 0}, {"txt": "  # Let session manager set this", "pos": 989, "len": 0}, {"txt": "# ", "pos": 994, "len": 0}, {"txt": "      # Let session manager set this", "pos": 1024, "len": 0}, {"txt": "# ", "pos": 1029, "len": 0}, {"txt": "  # Let session manager set this", "pos": 1063, "len": 0}, {"txt": ",x11  # Allow fallback to X11", "pos": 1739, "len": 0}, {"txt": "# ", "pos": 1882, "len": 0}, {"txt": "  # Let kitty auto-detect", "pos": 1910, "len": 0}, {"txt": "# ", "pos": 1915, "len": 0}, {"txt": "    # Let applications auto-detect", "pos": 1941, "len": 0}, {"txt": "# ", "pos": 2481, "len": 0}, {"txt": "  # Let session manager set this", "pos": 2511, "len": 0}, {"txt": "# ", "pos": 2789, "len": 0}, {"txt": "  # Let applications auto-detect", "pos": 2815, "len": 0}, {"txt": " (Hyprland-specific)", "pos": 2856, "len": 0}, {"txt": "# ", "pos": 2860, "len": 0}, {"txt": "      # Comment out for X11 compatibility", "pos": 2884, "len": 0}, {"txt": "# ", "pos": 2888, "len": 0}, {"txt": "     # Wayland compositor specific", "pos": 2913, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/hypr/conf/env_var.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_efc3471c-d7b6-4480-8734-48a901d707cd%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_859b8646-f32f-4ec7-8ce1-a9e3eaa81986", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_859b8646-f32f-4ec7-8ce1-a9e3eaa81986%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_859b8646-f32f-4ec7-8ce1-a9e3eaa81986%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_859b8646-f32f-4ec7-8ce1-a9e3eaa81986%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_859b8646-f32f-4ec7-8ce1-a9e3eaa81986%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/hypr/conf/env_var.conf", "languageId": "properties", "originalHash": "46f1963", "currentHash": "5a8a6b9", "originalToCurrentEdit": [{"txt": " - Made conditional for Hyprland only", "pos": 950, "len": 0}, {"txt": "# ", "pos": 955, "len": 0}, {"txt": "  # Let session manager set this", "pos": 989, "len": 0}, {"txt": "# ", "pos": 994, "len": 0}, {"txt": "      # Let session manager set this", "pos": 1024, "len": 0}, {"txt": "# ", "pos": 1029, "len": 0}, {"txt": "  # Let session manager set this", "pos": 1063, "len": 0}, {"txt": ",x11  # Allow fallback to X11", "pos": 1739, "len": 0}, {"txt": "# ", "pos": 1882, "len": 0}, {"txt": "  # Let kitty auto-detect", "pos": 1910, "len": 0}, {"txt": "# ", "pos": 1915, "len": 0}, {"txt": "    # Let applications auto-detect", "pos": 1941, "len": 0}, {"txt": "# ", "pos": 2481, "len": 0}, {"txt": "  # Let session manager set this", "pos": 2511, "len": 0}, {"txt": "# ", "pos": 2789, "len": 0}, {"txt": "  # Let applications auto-detect", "pos": 2815, "len": 0}, {"txt": " (Hyprland-specific)", "pos": 2856, "len": 0}, {"txt": "# ", "pos": 2860, "len": 0}, {"txt": "      # Comment out for X11 compatibility", "pos": 2884, "len": 0}, {"txt": "# ", "pos": 2888, "len": 0}, {"txt": "     # Wayland compositor specific", "pos": 2913, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/hypr/conf/env_var.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_859b8646-f32f-4ec7-8ce1-a9e3eaa81986%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "agentId": "github.copilot.editsAgent"}}]}]}, {"requestId": "request_d07fe83b-ff0b-4334-9c50-9e1139f342fa", "stops": [{"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "4d946a6", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d07fe83b-ff0b-4334-9c50-9e1139f342fa%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d07fe83b-ff0b-4334-9c50-9e1139f342fa%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d07fe83b-ff0b-4334-9c50-9e1139f342fa%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d07fe83b-ff0b-4334-9c50-9e1139f342fa%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/hypr/conf/env_var.conf", "languageId": "properties", "originalHash": "46f1963", "currentHash": "5a8a6b9", "originalToCurrentEdit": [{"txt": " - Made conditional for Hyprland only", "pos": 950, "len": 0}, {"txt": "# ", "pos": 955, "len": 0}, {"txt": "  # Let session manager set this", "pos": 989, "len": 0}, {"txt": "# ", "pos": 994, "len": 0}, {"txt": "      # Let session manager set this", "pos": 1024, "len": 0}, {"txt": "# ", "pos": 1029, "len": 0}, {"txt": "  # Let session manager set this", "pos": 1063, "len": 0}, {"txt": ",x11  # Allow fallback to X11", "pos": 1739, "len": 0}, {"txt": "# ", "pos": 1882, "len": 0}, {"txt": "  # Let kitty auto-detect", "pos": 1910, "len": 0}, {"txt": "# ", "pos": 1915, "len": 0}, {"txt": "    # Let applications auto-detect", "pos": 1941, "len": 0}, {"txt": "# ", "pos": 2481, "len": 0}, {"txt": "  # Let session manager set this", "pos": 2511, "len": 0}, {"txt": "# ", "pos": 2789, "len": 0}, {"txt": "  # Let applications auto-detect", "pos": 2815, "len": 0}, {"txt": " (Hyprland-specific)", "pos": 2856, "len": 0}, {"txt": "# ", "pos": 2860, "len": 0}, {"txt": "      # Comment out for X11 compatibility", "pos": 2884, "len": 0}, {"txt": "# ", "pos": 2888, "len": 0}, {"txt": "     # Wayland compositor specific", "pos": 2913, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/hypr/conf/env_var.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d07fe83b-ff0b-4334-9c50-9e1139f342fa%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "agentId": "github.copilot.editsAgent"}}]}, {"stopId": "78f2b916-f298-42e2-9fe4-cec4b6bd49b1", "entries": [{"resource": "file:///etc/sddm.conf", "languageId": "properties", "originalHash": "b5573e6", "currentHash": "b5573e6", "originalToCurrentEdit": [], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/etc/sddm.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d07fe83b-ff0b-4334-9c50-9e1139f342fa%22%2C%22undoStop%22%3A%2278f2b916-f298-42e2-9fe4-cec4b6bd49b1%22%7D", "telemetryInfo": {"requestId": "request_d07fe83b-ff0b-4334-9c50-9e1139f342fa", "agentId": "github.copilot.editsAgent"}}]}], "postEdit": [{"resource": "file:///etc/sddm.conf", "languageId": "properties", "originalHash": "b5573e6", "currentHash": "6bcc802", "originalToCurrentEdit": [{"txt": "", "pos": 154, "len": 1}, {"txt": "", "pos": 172, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/etc/sddm.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22request_d07fe83b-ff0b-4334-9c50-9e1139f342fa%22%2C%22undoStop%22%3A%22d19944f6-f46c-4e17-911b-79a8e843c7c0%22%7D", "telemetryInfo": {"requestId": "request_d07fe83b-ff0b-4334-9c50-9e1139f342fa", "agentId": "github.copilot.editsAgent"}}]}], "linearHistoryIndex": 31, "initialFileContents": [["file:///home/<USER>/.config/fish/config.fish", "4d946a6"], ["file:///home/<USER>/.config/fish/config_minimal.fish", "da39a3e"], ["file:///home/<USER>/.config/kitty/kitty.conf", "335e3ab"], ["file:///tmp/minimal_fish_config.fish", "da39a3e"], ["file:///home/<USER>/.config/hypr/conf/env_var.conf", "46f1963"], ["file:///etc/sddm.conf", "b5573e6"]], "recentSnapshot": {"entries": [{"resource": "file:///home/<USER>/.config/fish/config.fish", "languageId": "shellscript", "originalHash": "4d946a6", "currentHash": "add24f0", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_8c3af281-b0a7-4876-a88e-5740ee6e75f6", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/fish/config_minimal.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/fish/config_minimal.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_cd1a2fa6-05f4-4528-89ac-b7af2ddc5098", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/kitty/kitty.conf", "languageId": "properties", "originalHash": "335e3ab", "currentHash": "335e3ab", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/kitty/kitty.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///tmp/minimal_fish_config.fish", "languageId": "shellscript", "originalHash": "da39a3e", "currentHash": "da39a3e", "originalToCurrentEdit": [], "state": 2, "snapshotUri": "chat-editing-snapshot-text-model:/tmp/minimal_fish_config.fish?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_4a05f573-641b-4db9-be1a-0b019e5961f3", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///home/<USER>/.config/hypr/conf/env_var.conf", "languageId": "properties", "originalHash": "46f1963", "currentHash": "5a8a6b9", "originalToCurrentEdit": [{"txt": " - Made conditional for Hyprland only", "pos": 950, "len": 0}, {"txt": "# ", "pos": 955, "len": 0}, {"txt": "  # Let session manager set this", "pos": 989, "len": 0}, {"txt": "# ", "pos": 994, "len": 0}, {"txt": "      # Let session manager set this", "pos": 1024, "len": 0}, {"txt": "# ", "pos": 1029, "len": 0}, {"txt": "  # Let session manager set this", "pos": 1063, "len": 0}, {"txt": ",x11  # Allow fallback to X11", "pos": 1739, "len": 0}, {"txt": "# ", "pos": 1882, "len": 0}, {"txt": "  # Let kitty auto-detect", "pos": 1910, "len": 0}, {"txt": "# ", "pos": 1915, "len": 0}, {"txt": "    # Let applications auto-detect", "pos": 1941, "len": 0}, {"txt": "# ", "pos": 2481, "len": 0}, {"txt": "  # Let session manager set this", "pos": 2511, "len": 0}, {"txt": "# ", "pos": 2789, "len": 0}, {"txt": "  # Let applications auto-detect", "pos": 2815, "len": 0}, {"txt": " (Hyprland-specific)", "pos": 2856, "len": 0}, {"txt": "# ", "pos": 2860, "len": 0}, {"txt": "      # Comment out for X11 compatibility", "pos": 2884, "len": 0}, {"txt": "# ", "pos": 2888, "len": 0}, {"txt": "     # Wayland compositor specific", "pos": 2913, "len": 0}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/home/<USER>/.config/hypr/conf/env_var.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_e972cd19-676a-48c6-87b2-a935e0dd24fc", "agentId": "github.copilot.editsAgent"}}, {"resource": "file:///etc/sddm.conf", "languageId": "properties", "originalHash": "b5573e6", "currentHash": "6bcc802", "originalToCurrentEdit": [{"txt": "", "pos": 154, "len": 1}, {"txt": "", "pos": 172, "len": 1}], "state": 0, "snapshotUri": "chat-editing-snapshot-text-model:/etc/sddm.conf?%7B%22sessionId%22%3A%22af5cb81b-6c69-46d4-bf41-ef12d3a119c9%22%2C%22requestId%22%3A%22%22%2C%22undoStop%22%3A%22%22%7D", "telemetryInfo": {"requestId": "request_d07fe83b-ff0b-4334-9c50-9e1139f342fa", "agentId": "github.copilot.editsAgent"}}]}}