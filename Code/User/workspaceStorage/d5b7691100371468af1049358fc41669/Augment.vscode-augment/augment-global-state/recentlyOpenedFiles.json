[["/home/<USER>/.config/hypr/hyprland.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hyprland.conf"}}], ["/home/<USER>/.config/backup/hypr/hypr/scripts/hypr_blizz_update.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "backup/hypr/hypr/scripts/hypr_blizz_update.sh"}}], ["/home/<USER>/.config/gtk-3.0/apps/nemo.css", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "gtk-3.0/apps/nemo.css"}}], ["/home/<USER>/.config/gtk-4.0/gtk.css", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "gtk-4.0/gtk.css"}}], ["/home/<USER>/.config/gtk-4.0/apps/nemo.css", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "gtk-4.0/apps/nemo.css"}}], ["/home/<USER>/.config/gtk-3.0/gtk.css", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "gtk-3.0/gtk.css"}}], ["/home/<USER>/.config/backup/hypr/hypr/scripts/hypr_waybar_update.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "backup/hypr/hypr/scripts/hypr_waybar_update.sh"}}], ["/home/<USER>/.config/hypr/conf/exec_once.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/conf/exec_once.conf"}}], ["/home/<USER>/.config/backup/hypr/hypr/conf/key_binds.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "backup/hypr/hypr/conf/key_binds.conf"}}], ["/home/<USER>/.config/hypr/hdt_overlay_rules.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/hdt_overlay_rules.conf"}}], ["/home/<USER>/.config/hypr/workspaces.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/workspaces.conf"}}], ["/home/<USER>/.config/waybar/scripts/launch-steam.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "waybar/scripts/launch-steam.sh"}}], ["/home/<USER>/.config/game_memory_monitor.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "game_memory_monitor.sh"}}], ["/home/<USER>/.config/proton_memory_config.env", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "proton_memory_config.env"}}], ["/home/<USER>/.config/fix_steam_infinite_loop.sh", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "fix_steam_infinite_loop.sh"}}], ["/home/<USER>/.config/hypr/conf/key_binds.conf", {"value": {"rootPath": "/home/<USER>/.config", "relPath": "hypr/conf/key_binds.conf"}}]]