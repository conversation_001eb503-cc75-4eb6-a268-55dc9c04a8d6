
 # █▄▀ █▀▀ █▄█ █▄▄ █ █▄ █ █▀▄ █▀
 # █ █ ██▄  █  █▄█ █ █ ▀█ █▄▀ ▄█
 
# See https://wiki.hyprland.org/Configuring/Keywords/ for more
    $mainMod = SUPER

# ▄▀█ █▀█ █▀█ █▀
# █▀█ █▀▀ █▀▀ ▄█

    bind = $mainMod, Return,  exec,kitty
    bind = $mainMod SHIFT, D, exec, nemo
    bind = $mainMod, O, exec, oepra --app=https://chat.openai.com --password-store=basic %U
    bind = $mainMod SHIFT, Y, exec, kitty --class=pacseek pacseek
    bind = $mainMod, G, exec, [workspace 10] partitionmanager
    bind = $mainMod SHIFT, Q,exec,~/.config/hypr/scripts/temp.sh
    bind= $mainMod,F,fullscreen

 # █▀ █▀▀ █▀█ █ █▀█ ▀█▀ █▀
 # ▄█ █▄▄ █▀▄ █ █▀▀  █  ▄█

# open kitty in floating mode
    bind = $mainMod SHIFT,T, exec,kitty --title kitty_floats

# open kitty in fullscreen mode 
    bind = $mainMod ALT, F11, exec, kitty --start-as=fullscreen

# Draws rectangle and spawns kitty terminal
    bind = $mainMod ALT, Return, exec, ~/.config/hypr/scripts/spawn-terminal.sh 

 #█░█░█ █▀█ █▀▀ █
 #▀▄▀▄▀ █▄█ █▀░ █ 

# wofi launchers
    bind = $mainMod, D, exec,~/.config/hypr/scripts/wofi-drun
    bind = $mainMod, R, exec, ~/.config/hypr/scripts/wofi-run

# open cliphist / clipboard manager (wofi)
    bind = $mainMod SHIFT, H, exec,~/.config/hypr/scripts/cliphist-menu.sh
    bind = $mainMod, H, exec, cliphist list | wofi -dmenu | cliphist decode | wl-copy # open clipboard manager

# networkmanager-dmenu script what uses wofi or rofi see configs .config/networkmanager-dmenu the bakfile is now the wofi config
    bind = $mainMod, n, exec, ~/.config/waybar/scripts/networkmanager.sh

#█░█ █▄█ █▀█ █▀█ █▀█ █ █▀▀ █▄▀ █▀▀ █▀█
#█▀█ ░█░ █▀▀ █▀▄ █▀▀ █ █▄▄ █░█ ██▄ █▀▄

# hyprpicker / color picker app for wayaland/hyprland
    bind = $mainMod SHIFT, X, exec, hyprpicker -a -n

 #█▀ █▀▀ ▀█▀ ▀█▀ █ █▄░█ █▀▀ █▀
 #▄█ ██▄ ░█░ ░█░ █ █░▀█ █▄█ ▄█

# settings script see also the waybar information module
    bind = $mainMod, i, exec, ~/.config/waybar/scripts/settings.sh

#█░█░█ ▄▀█ █▄█ █▄▄ ▄▀█ █▀█ 
#▀▄▀▄▀ █▀█ ░█░ █▄█ █▀█ █▀▄ 

# reloads waybar
    bind = $mainMod ALT,B,exec,~/.config/waybar/scripts/toggle-waybar.sh

# switches waybar laptop version to desktop version
    #bind = $mainMod, W, exec,~/.config/waybar/scripts/switch-waybar-config.sh

#█░█ █▄█ █▀█ █▀█ █ █▀▄ █░░ █▀▀
#█▀█ ░█░ █▀▀ █▀▄ █ █▄▀ █▄▄ ██▄

# custom hypridle script
    bind = $mainMod SHIFT, i, exec, ~/.config/waybar/scripts/hypridle_toggle.sh

#█░█░█ ▄▀█ █░░ █░░ █▀█ ▄▀█ █▀█ █▀▀ █▀█
#▀▄▀▄▀ █▀█ █▄▄ █▄▄ █▀▀ █▀█ █▀▀ ██▄ █▀▄

# wallpaper switcher scripts / swww is needed for this to work
   #bind = $mainMod SHIFT, W, exec, ~/.config/hypr/scripts/random-wallpaper
    bind = $mainMod SHIFT, W, exec, ~/.config/hypr/scripts/changeWallpaper

#█▄▀ █▄▄ ░░▄▀ █░░ ▄▀█ █▄█ █▀█ █░█ ▀█▀
#█░█ █▄█ ▄▀░░ █▄▄ █▀█ ░█░ █▄█ █▄█ ░█░

# custom kb-layout switch script / see also waybar language module
#   bind = ALTSHIFT,Shift_L,exec,~/.config/hypr/scripts/switch_kb_layout.sh

#█░█ █▄█ █▀█ █▀█ █░░ █▀█ █▀▀ █▄▀
#█▀█ ░█░ █▀▀ █▀▄ █▄▄ █▄█ █▄▄ █░█

    bind = CTRL ALT, L, exec, hyprlock

#█░█░█ █░░ █▀█ █▀▀ █▀█ █░█ ▀█▀
#▀▄▀▄▀ █▄▄ █▄█ █▄█ █▄█ █▄█ ░█░

# wlogout
    bind = $mainMod, X, exec, wlogout --protocol layer-shell -b 5 -T 400 -B 400

# █▀ █▀▀ █▀█ █▀▀ █▀▀ █▄░█ █▀ █░█ █▀█ ▀█▀
# ▄█ █▄▄ █▀▄ ██▄ ██▄ █░▀█ ▄█ █▀█ █▄█ ░█░

# $screenshotarea = hyprctl keyword animation "fadeOut,0,0,default"; grimblast --notify copysave area; hyprctl keyword animation "fadeOut,1,4,default"
# Region screenshots
    bind = , Print, exec, ~/.config/hypr/scripts/screenshot rc
    bind = SUPER, Print, exec, ~/.config/hypr/scripts/screenshot rf
    bind = CTRL, Print, exec, ~/.config/hypr/scripts/screenshot ri
    bind = $mainMod SHIFT, S, exec, hyprshot -m region --clipboard-only --silent

# Screen screenshots
    bind = SHIFT, Print, exec, ~/.config/hypr/scripts/screenshot sc
    bind = SUPER SHIFT, Print, exec, ~/.config/hypr/scripts/screenshot sf
    bind = CTRL SHIFT, Print, exec, ~/.config/hypr/scripts/screenshot si

   #bind = ,Print,exec,hyprshot --mode region -o $HOME/Pictures/Screenshots/$(date +'%s.png')
   #bind = CTRL,Print, exec,hyprshot --mode output -o $HOME/Pictures/Screenshots/$(date +'%s.png')
   #bind = ALT, Print, exec,hyprshot --mode window -o $HOME/Pictures/Screenshots/$(date +'%s.png')

# █▀▄▀█ █░█ █░░ ▀█▀ █ █▀▄▀█ █▀▀ █▀▄ █ ▄▀█
# █░▀░█ █▄█ █▄▄ ░█░ █ █░▀░█ ██▄ █▄▀ █ █▀█

# Volume audio
    binde =, XF86AudioRaiseVolume, exec, ~/.config/hypr/scripts/volume.sh volume_up
    binde =, XF86AudioLowerVolume, exec, ~/.config/hypr/scripts/volume.sh volume_down
    binde =, XF86AudioMute, exec, ~/.config/hypr/scripts/volume.sh volume_mute

# Volume microphone
    binde = $mainMod, XF86AudioRaiseVolume, exec, ~/.config/hypr/scripts/volume.sh mic_up 
    binde = $mainMod, XF86AudioLowerVolume, exec, ~/.config/hypr/scripts/volume.sh mic_down
    binde = $mainMod, XF86AudioMute, exec, ~/.config/hypr/scripts/volume.sh mic_mute

# Volume microphone (use if needed 
   #binde = , XF86AudioMicRaiseVolume, exec, ~/.config/hypr/scripts/volume_notify.sh mic_up
   #binde = , XF86AudioMicLowerVolume, exec, ~/.config/hypr/scripts/volume_notify.sh mic_down
   #binde = , xf86AudioMicMute, exec, $volume --toggle-mic #mute mic

# not in use on the moment
   bind = , XF86AudioPlay, exec, playerctl play-pause
   bind = , XF86AudioPause, exec, playerctl play-pause
   bind = , XF86AudioNext, exec, playerctl next
   bind = , XF86AudioPrev, exec, playerctl previous

# Audio output switching
   bind = $mainMod SHIFT, KP_End, exec, ~/.config/hypr/scripts/audio-switch.sh spdif # Numpad1 - USB SPDIF
   bind = $mainMod SHIFT, KP_Down, exec, ~/.config/hypr/scripts/audio-switch.sh arctis # Numpad2 - Arctis Pro Wireless
   bind = $mainMod SHIFT, KP_Next, exec, ~/.config/hypr/scripts/audio-switch.sh headphones # Numpad3 - USB Headphones  
   bind = $mainMod SHIFT, KP_Left, exec, ~/.config/hypr/scripts/audio-switch.sh speakers # Numpad4 - USB 5.1 Speakers
   bind = $mainMod SHIFT, KP_Begin, exec, ~/.config/hypr/scripts/audio-switch.sh current # Numpad5 - Show current output

# █▄▄ █▀█ █ █▀▀ ▀█▀ █ █ █▄ █ █▀▀ █▀ █▀
# █▄█ █▀▄ █ █▄█  █  █▀█ █ ▀█ ██▄ ▄█ ▄█

    binde = ALT, F12, exec, brightnessctl set 5%+
    binde = , XF86MonBrightnessUp, exec, brightnessctl set 5%+
    binde = ALT, F11, exec, brightnessctl set 5%-
    binde = , XF86MonBrightnessDown, exec, brightnessctl set 5%-

# █▄▄ █   █ █ █▀▀ ▀█▀ █▀█ █▀█ ▀█▀ █ █
# █▄█ █▄▄ █▄█ ██▄  █  █▄█ █▄█  █  █▀█

# toggle bluetooth 
   bind = $mainMod ALT,B,exec,~/.config/hypr/scripts/toggle_bluetooth.sh 

# █▀▀ ▄▀█ █▀▄▀█ █▀▀ █▀▄▀█ █▀█ █▀▄ █▀▀
# █▄█ █▀█ █░▀░█ ██▄ █░▀░█ █▄█ █▄▀ ██▄

# enable game mode
   bind = $mainMod, F1, exec, ~/.config/hypr/scripts/gamemode.sh
