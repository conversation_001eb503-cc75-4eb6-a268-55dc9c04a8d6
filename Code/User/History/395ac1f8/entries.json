{"version": 1, "resource": "file:///home/<USER>/.config/hypr/conf/key_binds.conf", "entries": [{"id": "ehOZ.conf", "timestamp": 1746712780541}, {"id": "JNIN.conf", "timestamp": 1746717498817}, {"id": "Lp4r.conf", "source": "undoRedo.source", "timestamp": 1746717529387}, {"id": "O5Cd.conf", "timestamp": 1746732480297}, {"id": "7uKH.conf", "timestamp": 1746826380127}, {"id": "185U.conf", "timestamp": 1746909012241}, {"id": "iv3x.conf", "timestamp": 1747227716921}, {"id": "eYuP.conf", "timestamp": 1747304109323}, {"id": "TlC6.conf", "timestamp": 1747318698018}, {"id": "JbFz.conf", "timestamp": 1748110567635}, {"id": "AOgM.conf", "timestamp": 1748162345643}, {"id": "K6Qr.conf", "source": "Chat Edit: 'in key_binds.conf add 2 keybinds ($mainMod SHIFT, Numpad2 and $mainMod SHIFT, Npad1) to switch audio outputs to Arctis pro wireless and USB SPDIF, also put all current outputs there, here is pact<PERSON>, if you need more info, tell me\n\npactl list short sinks\n301\talsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__SPDIF__sink\tPipeWire\ts16le 2ch 48000Hz\tRUNNING\n302\talsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Headphones__sink\tPipeWire\ts32le 2ch 48000Hz\tIDLE\n303\talsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Speaker__sink\tPipeWire\ts32le 6ch 48000Hz\tIDLE\n499\talsa_output.usb-SteelSeries_Arctis_Nova_Pro_Wireless-00.analog-stereo\tPipeWire\ts24le 2ch 48000Hz\tIDLE'", "timestamp": 1748714922559}, {"id": "P40Y.conf", "timestamp": 1749079936590}, {"id": "dooe.conf", "source": "Chat Edit: 'why does this not work ? \n    bind = $mainMod SHIFT, D, exec, nemo'", "timestamp": 1750612860712}, {"id": "UYzx.conf", "timestamp": 1750687722219}, {"id": "BDv1.conf", "timestamp": 1750755410308}, {"id": "DfXI.conf", "timestamp": 1751635464148}, {"id": "spQ9.conf", "timestamp": 1751884265595}, {"id": "KQa9.conf", "timestamp": 1751884301719}]}