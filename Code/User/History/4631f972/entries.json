{"version": 1, "resource": "file:///home/<USER>/.config/fish/aliases.fish", "entries": [{"id": "HeM8.fish", "timestamp": 1746713655304}, {"id": "Ldbz.fish", "timestamp": 1746713688314}, {"id": "8BV1.fish", "timestamp": 1746713707264}, {"id": "kNc8.fish", "timestamp": 1746739302413}, {"id": "rDVo.fish", "timestamp": 1747918691401}, {"id": "Sij1.fish", "timestamp": 1748523283632}, {"id": "2EXM.fish", "timestamp": 1748523596028}, {"id": "VE08.fish", "timestamp": 1748523611849}, {"id": "Rcc8.fish", "timestamp": 1748523629795}, {"id": "Fy4j.fish", "timestamp": 1748525115297}, {"id": "TeGU.fish", "timestamp": 1748525542968}, {"id": "UhdO.fish", "timestamp": 1748525660991}, {"id": "iQVn.fish", "timestamp": 1748526172934}, {"id": "8YRG.fish", "timestamp": 1749079985804}, {"id": "ONWH.fish", "source": "Chat Edit: 'please make me 1 alias, the house one, when used, it should open mpv to the latest \"House MD\" episode I haven't watched yet, so you should also make a system / a way to know which I watched and keep track of it'", "timestamp": 1749080102666}, {"id": "0cEl.fish", "source": "Chat Edit: '@agent Continue: \"Continue to iterate?\"'", "timestamp": 1749080948731}, {"id": "98at.fish", "source": "Chat Edit: 'Can you do exactly the same for SAO please ?'", "timestamp": 1749081159711}, {"id": "LiDo.fish", "source": "Chat Edit: 'let me jump to an episode too (on both aliases)'", "timestamp": 1749081777997}, {"id": "9egn.fish", "timestamp": 1749243528181}, {"id": "ef9T.fish", "timestamp": 1750820703349}]}